"""本地文件服务器

提供本地文件的HTTP访问，用于Paraformer等需要URL访问的服务。
"""

import os
import threading
import time
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import unquote
import socket

from ..common.logging import get_logger


class LocalFileServer:
    """本地文件HTTP服务器"""
    
    def __init__(self, port: int = 8000, host: str = 'localhost'):
        """初始化本地文件服务器
        
        Args:
            port: 服务器端口
            host: 服务器主机
        """
        self.port = port
        self.host = host
        self.server = None
        self.server_thread = None
        self.logger = get_logger(__name__)
        self.is_running = False
        
        # 创建临时目录
        self.temp_dir = Path("temp_files")
        self.temp_dir.mkdir(exist_ok=True)
    
    def find_available_port(self, start_port: int = 8000, max_attempts: int = 100) -> int:
        """查找可用端口"""
        for port in range(start_port, start_port + max_attempts):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind((self.host, port))
                    return port
            except OSError:
                continue
        raise RuntimeError(f"无法找到可用端口 (尝试了 {start_port} 到 {start_port + max_attempts - 1})")
    
    def start(self) -> bool:
        """启动服务器"""
        if self.is_running:
            self.logger.info(f"服务器已在运行: http://{self.host}:{self.port}")
            return True
        
        try:
            # 查找可用端口
            self.port = self.find_available_port(self.port)
            
            # 切换到临时目录
            original_dir = os.getcwd()
            os.chdir(self.temp_dir)
            
            # 创建HTTP服务器
            self.server = HTTPServer((self.host, self.port), SimpleHTTPRequestHandler)
            
            # 恢复原目录
            os.chdir(original_dir)
            
            # 在单独线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            self.is_running = True
            self.logger.info(f"本地文件服务器已启动: http://{self.host}:{self.port}")
            
            # 等待一下确保服务器启动
            time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动本地文件服务器失败: {e}")
            return False
    
    def stop(self):
        """停止服务器"""
        if self.server and self.is_running:
            self.server.shutdown()
            self.server.server_close()
            
            if self.server_thread:
                self.server_thread.join(timeout=5)
            
            self.is_running = False
            self.logger.info("本地文件服务器已停止")
    
    def add_file(self, file_path: str, custom_name: str = None) -> str:
        """添加文件到服务器并返回URL
        
        Args:
            file_path: 本地文件路径
            custom_name: 自定义文件名
            
        Returns:
            str: 文件的HTTP URL
            
        Raises:
            FileNotFoundError: 文件不存在
            RuntimeError: 服务器未运行
        """
        if not self.is_running:
            if not self.start():
                raise RuntimeError("无法启动本地文件服务器")
        
        # 检查文件是否存在
        source_file = Path(file_path)
        if not source_file.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 确定目标文件名
        if custom_name:
            target_name = custom_name
        else:
            # 使用时间戳避免文件名冲突
            timestamp = int(time.time())
            target_name = f"{timestamp}_{source_file.name}"
        
        # 复制文件到临时目录
        target_file = self.temp_dir / target_name
        
        try:
            import shutil
            shutil.copy2(source_file, target_file)
            
            # 构造URL
            url = f"http://{self.host}:{self.port}/{target_name}"
            
            self.logger.info(f"文件已添加到服务器: {file_path} -> {url}")
            return url
            
        except Exception as e:
            self.logger.error(f"添加文件到服务器失败: {e}")
            raise
    
    def remove_file(self, file_name: str):
        """从服务器移除文件"""
        target_file = self.temp_dir / file_name
        if target_file.exists():
            try:
                target_file.unlink()
                self.logger.info(f"文件已从服务器移除: {file_name}")
            except Exception as e:
                self.logger.error(f"移除文件失败: {e}")
    
    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.logger.info("临时文件已清理")
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")
    
    def get_server_info(self) -> dict:
        """获取服务器信息"""
        return {
            'host': self.host,
            'port': self.port,
            'is_running': self.is_running,
            'base_url': f"http://{self.host}:{self.port}",
            'temp_dir': str(self.temp_dir)
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
        self.cleanup()


# 全局服务器实例
_global_server = None


def get_global_server() -> LocalFileServer:
    """获取全局服务器实例"""
    global _global_server
    if _global_server is None:
        _global_server = LocalFileServer()
    return _global_server


def start_global_server() -> bool:
    """启动全局服务器"""
    server = get_global_server()
    return server.start()


def stop_global_server():
    """停止全局服务器"""
    global _global_server
    if _global_server:
        _global_server.stop()
        _global_server.cleanup()
        _global_server = None


def serve_local_file(file_path: str, custom_name: str = None) -> str:
    """为本地文件提供HTTP访问
    
    Args:
        file_path: 本地文件路径
        custom_name: 自定义文件名
        
    Returns:
        str: 文件的HTTP URL
    """
    server = get_global_server()
    return server.add_file(file_path, custom_name)


def test_server():
    """测试服务器功能"""
    print("测试本地文件服务器...")
    
    with LocalFileServer() as server:
        print(f"服务器信息: {server.get_server_info()}")
        
        # 创建测试文件
        test_file = Path("test_server_file.txt")
        test_file.write_text("这是一个测试文件", encoding='utf-8')
        
        try:
            # 添加文件到服务器
            url = server.add_file(str(test_file))
            print(f"文件URL: {url}")
            
            # 测试访问
            import requests
            response = requests.get(url)
            if response.status_code == 200:
                print("✓ 文件访问成功")
                print(f"文件内容: {response.text}")
            else:
                print(f"✗ 文件访问失败: {response.status_code}")
                
        finally:
            # 清理测试文件
            if test_file.exists():
                test_file.unlink()


if __name__ == '__main__':
    test_server()
