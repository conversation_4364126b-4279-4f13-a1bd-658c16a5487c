#!/usr/bin/env python3
"""修正Cloudreve配置文件中的用户名格式"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config, ConfigManager


def fix_cloudreve_config():
    """修正Cloudreve配置中的用户名格式"""
    print("=== 修正Cloudreve配置 ===")
    
    # 获取当前配置
    current_username = get_config('file_uploader.uploaders.cloudreve.username', '')
    
    if not current_username:
        print("❌ 未找到Cloudreve用户名配置")
        return False
    
    print(f"当前用户名: {current_username}")
    
    # 检查是否已经是email格式
    if '@' in current_username:
        print("✅ 用户名已经是email格式，无需修改")
        return True
    
    # 提示用户输入正确的email
    print("\nCloudreve V4要求使用email格式的用户名")
    print("请输入您的Cloudreve账户邮箱地址：")
    
    while True:
        email = input("邮箱地址: ").strip()
        
        if not email:
            print("邮箱地址不能为空，请重新输入")
            continue
        
        if '@' not in email or '.' not in email:
            print("请输入有效的邮箱地址格式，如: <EMAIL>")
            continue
        
        break
    
    # 更新配置文件
    try:
        config_manager = ConfigManager()
        config_manager.set('file_uploader.uploaders.cloudreve.username', email)
        config_manager.save()
        
        print(f"✅ 配置已更新: {email}")
        print("请重新运行测试脚本验证配置")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False


def main():
    """主函数"""
    print("Cloudreve配置修正工具")
    print("=" * 50)
    
    success = fix_cloudreve_config()
    
    if success:
        print("\n🎉 配置修正完成!")
        print("现在可以运行以下命令测试Cloudreve客户端:")
        print("python test_cloudreve_client.py")
    else:
        print("\n❌ 配置修正失败")


if __name__ == "__main__":
    main()
