# VideoReader 项目依赖

# 核心依赖
opencv-python>=4.5.0          # 计算机视觉和视频处理
PySide6>=6.0.0                # 图形用户界面框架
numpy>=1.21.0                 # 数值计算
Pillow>=8.0.0                 # 图像处理
PyYAML>=6.0                   # YAML配置文件解析

# 音频和语音处理
librosa>=0.9.0                # 音频分析
soundfile>=0.10.0             # 音频文件读写
pydub>=0.25.0                 # 音频处理
dashscope>=1.14.0             # 阿里云Paraformer语音识别
openai-whisper>=20231117      # OpenAI Whisper语音识别
azure-cognitiveservices-speech>=1.30.0  # Azure语音服务

# 视频处理
ffmpeg-python>=0.2.0          # FFmpeg Python 绑定
moviepy>=1.0.3                # 视频编辑

# 数据处理
pandas>=1.3.0                 # 数据分析
scipy>=1.7.0                  # 科学计算

# 工具库
tqdm>=4.62.0                  # 进度条
requests>=2.25.0              # HTTP 请求
python-dateutil>=2.8.0        # 日期时间处理
configparser>=5.0.0           # 配置文件解析
tomli>=2.0.0;python_version<"3.11"  # TOML解析 (Python < 3.11)
tomli-w>=1.0.0                # TOML写入

# 开发和测试依赖
pytest>=6.0.0                 # 测试框架
pytest-cov>=2.12.0            # 测试覆盖率
black>=21.0.0                 # 代码格式化
flake8>=3.9.0                 # 代码检查
mypy>=0.910                   # 类型检查

# 可选依赖（根据需要安装）
# torch>=1.9.0                # PyTorch（如果使用深度学习模型）
# transformers>=4.0.0         # Hugging Face Transformers（如果使用预训练模型）
# matplotlib>=3.4.0           # 数据可视化
# seaborn>=0.11.0             # 统计数据可视化

# 打包和分发
PyInstaller>=4.5.0            # 打包为可执行文件
setuptools>=57.0.0            # 包管理工具
wheel>=0.36.0                 # 包构建工具
