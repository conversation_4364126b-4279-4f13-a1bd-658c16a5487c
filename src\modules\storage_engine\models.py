"""存储引擎数据模型

定义存储引擎模块内部使用的数据结构。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum
from pathlib import Path


class StorageFormat(Enum):
    """存储格式枚举"""
    JSON = "json"
    PICKLE = "pickle"
    YAML = "yaml"
    XML = "xml"


class CompressionType(Enum):
    """压缩类型枚举"""
    NONE = "none"
    GZIP = "gzip"
    BZIP2 = "bzip2"
    LZMA = "lzma"


@dataclass
class StorageConfig:
    """存储配置数据模型"""
    format: StorageFormat = StorageFormat.JSON
    compression: CompressionType = CompressionType.NONE
    backup_enabled: bool = True
    max_backups: int = 5
    auto_cleanup: bool = True
    cleanup_days: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'format': self.format.value,
            'compression': self.compression.value,
            'backup_enabled': self.backup_enabled,
            'max_backups': self.max_backups,
            'auto_cleanup': self.auto_cleanup,
            'cleanup_days': self.cleanup_days
        }


@dataclass
class CacheEntry:
    """缓存条目数据模型"""
    cache_key: str
    file_name: str
    file_size: int
    created_time: float
    last_accessed: float
    access_count: int
    video_path: str
    config_hash: str
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'cache_key': self.cache_key,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'created_time': self.created_time,
            'last_accessed': self.last_accessed,
            'access_count': self.access_count,
            'video_path': self.video_path,
            'config_hash': self.config_hash,
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """从字典创建实例"""
        return cls(
            cache_key=data['cache_key'],
            file_name=data['file_name'],
            file_size=data['file_size'],
            created_time=data['created_time'],
            last_accessed=data['last_accessed'],
            access_count=data['access_count'],
            video_path=data['video_path'],
            config_hash=data['config_hash'],
            metadata=data.get('metadata', {})
        )



@dataclass
class CacheInfo:
    """缓存信息数据模型"""
    total_size: int
    total_count: int
    oldest_cache_time: float
    newest_cache_time: float
    cache_dir: str
    max_size: int
    max_age: int

    @property
    def size_mb(self) -> float:
        """缓存大小（MB）"""
        return self.total_size / (1024 * 1024)

    @property
    def usage_percentage(self) -> float:
        """使用百分比"""
        return (self.total_size / self.max_size) * 100 if self.max_size > 0 else 0


@dataclass
class BackupInfo:
    """备份信息数据模型"""
    original_path: str
    backup_path: str
    created_time: datetime
    file_size: int
    checksum: str
    version: str = "1.0.0"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'original_path': self.original_path,
            'backup_path': self.backup_path,
            'created_time': self.created_time.isoformat(),
            'file_size': self.file_size,
            'checksum': self.checksum,
            'version': self.version
        }


@dataclass
class StorageStats:
    """存储统计信息数据模型"""
    total_metadata_files: int = 0
    total_cache_entries: int = 0
    total_backup_files: int = 0
    metadata_storage_size: int = 0  # 字节
    cache_storage_size: int = 0     # 字节
    backup_storage_size: int = 0    # 字节
    last_cleanup_time: Optional[datetime] = None
    
    @property
    def total_storage_size(self) -> int:
        """总存储大小"""
        return self.metadata_storage_size + self.cache_storage_size + self.backup_storage_size
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'total_metadata_files': self.total_metadata_files,
            'total_cache_entries': self.total_cache_entries,
            'total_backup_files': self.total_backup_files,
            'metadata_storage_size': self.metadata_storage_size,
            'cache_storage_size': self.cache_storage_size,
            'backup_storage_size': self.backup_storage_size,
            'total_storage_size': self.total_storage_size,
            'last_cleanup_time': self.last_cleanup_time.isoformat() if self.last_cleanup_time else None
        }


@dataclass
class ExportTemplate:
    """导出模板数据模型"""
    name: str
    format: str
    template_content: str
    variables: List[str] = field(default_factory=list)
    description: str = ""
    author: str = ""
    version: str = "1.0.0"
    created_time: datetime = field(default_factory=datetime.now)
    
    def render(self, context: Dict[str, Any]) -> str:
        """渲染模板"""
        try:
            # 简单的模板渲染，可以后续扩展为使用Jinja2等模板引擎
            content = self.template_content
            for key, value in context.items():
                placeholder = f"{{{key}}}"
                content = content.replace(placeholder, str(value))
            return content
        except Exception as e:
            raise ValueError(f"模板渲染失败: {e}")


@dataclass
class ExportJob:
    """导出任务数据模型"""
    job_id: str
    format: str
    output_path: str
    options: Dict[str, Any]
    status: str = "pending"  # pending, running, completed, failed
    progress: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    result_info: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_completed(self) -> bool:
        return self.status in ["completed", "failed"]
    
    @property
    def duration(self) -> Optional[float]:
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    def start(self):
        """开始任务"""
        self.status = "running"
        self.start_time = datetime.now()
    
    def complete(self, result_info: Dict[str, Any] = None):
        """完成任务"""
        self.status = "completed"
        self.end_time = datetime.now()
        self.progress = 1.0
        if result_info:
            self.result_info.update(result_info)
    
    def fail(self, error_message: str):
        """任务失败"""
        self.status = "failed"
        self.end_time = datetime.now()
        self.error_message = error_message


@dataclass
class MetadataIndex:
    """元数据索引数据模型"""
    video_path: str
    metadata_path: str
    last_modified: datetime
    file_size: int
    checksum: str
    tags: List[str] = field(default_factory=list)
    summary: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'video_path': self.video_path,
            'metadata_path': self.metadata_path,
            'last_modified': self.last_modified.isoformat(),
            'file_size': self.file_size,
            'checksum': self.checksum,
            'tags': self.tags,
            'summary': self.summary
        }


@dataclass
class StorageOperation:
    """存储操作数据模型"""
    operation_id: str
    operation_type: str  # save, load, export, backup, restore
    target_path: str
    status: str = "pending"
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    bytes_processed: int = 0
    total_bytes: Optional[int] = None
    error_message: Optional[str] = None
    
    @property
    def progress(self) -> float:
        if self.total_bytes and self.total_bytes > 0:
            return min(1.0, self.bytes_processed / self.total_bytes)
        return 0.0
    
    @property
    def is_completed(self) -> bool:
        return self.status in ["completed", "failed", "cancelled"]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'operation_id': self.operation_id,
            'operation_type': self.operation_type,
            'target_path': self.target_path,
            'status': self.status,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'bytes_processed': self.bytes_processed,
            'total_bytes': self.total_bytes,
            'progress': self.progress,
            'error_message': self.error_message
        }


@dataclass
class StorageQuota:
    """存储配额数据模型"""
    max_metadata_size: int = 100 * 1024 * 1024  # 100MB
    max_cache_size: int = 500 * 1024 * 1024     # 500MB
    max_backup_size: int = 1024 * 1024 * 1024   # 1GB
    max_files_per_directory: int = 1000
    
    def check_quota(self, stats: StorageStats) -> Dict[str, bool]:
        """检查配额限制"""
        return {
            'metadata_quota_ok': stats.metadata_storage_size <= self.max_metadata_size,
            'cache_quota_ok': stats.cache_storage_size <= self.max_cache_size,
            'backup_quota_ok': stats.backup_storage_size <= self.max_backup_size
        }
    
    def get_usage_percentage(self, stats: StorageStats) -> Dict[str, float]:
        """获取使用百分比"""
        return {
            'metadata_usage': (stats.metadata_storage_size / self.max_metadata_size) * 100,
            'cache_usage': (stats.cache_storage_size / self.max_cache_size) * 100,
            'backup_usage': (stats.backup_storage_size / self.max_backup_size) * 100
        }
