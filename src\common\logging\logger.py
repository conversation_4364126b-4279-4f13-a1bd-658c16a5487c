"""日志管理器

提供统一的日志管理功能。
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any

from .formatters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON><PERSON>atter, Detailed<PERSON>ormatter, SimpleFormatter


class LoggerManager:
    """日志管理器"""
    
    _instance = None
    _loggers: Dict[str, logging.Logger] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
            self.log_dir = Path("logs")
            self.log_dir.mkdir(exist_ok=True)
            self._setup_root_logger()
    
    def _setup_root_logger(self):
        """设置根日志记录器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(ColoredFormatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        ))
        root_logger.addHandler(console_handler)
        
        # 文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "videoreader.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(DetailedFormatter())
        root_logger.addHandler(file_handler)
        
        # 错误文件处理器
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "error.log",
            maxBytes=5*1024*1024,   # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(error_handler)
    
    def get_logger(self, name: str, level: Optional[str] = None) -> logging.Logger:
        """获取指定名称的日志记录器"""
        if name not in self._loggers:
            logger = logging.getLogger(name)
            if level:
                logger.setLevel(getattr(logging, level.upper()))
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def set_level(self, level: str):
        """设置全局日志级别"""
        logging.getLogger().setLevel(getattr(logging, level.upper()))
    
    def add_file_handler(self, name: str, filename: str, level: str = "DEBUG"):
        """为指定日志记录器添加文件处理器"""
        logger = self.get_logger(name)
        
        handler = logging.FileHandler(
            self.log_dir / filename,
            encoding='utf-8'
        )
        handler.setLevel(getattr(logging, level.upper()))
        handler.setFormatter(DetailedFormatter())
        
        logger.addHandler(handler)
    
    def configure_module_logger(self, module_name: str, config: Dict[str, Any]):
        """配置模块日志记录器"""
        logger = self.get_logger(module_name)
        
        if 'level' in config:
            logger.setLevel(getattr(logging, config['level'].upper()))
        
        if 'file' in config:
            self.add_file_handler(module_name, config['file'], config.get('file_level', 'DEBUG'))


# 全局日志管理器实例
_logger_manager = LoggerManager()


def get_logger(name: str = None) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    if name is None:
        # 获取调用者的模块名
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return _logger_manager.get_logger(name)


def set_log_level(level: str):
    """设置日志级别的便捷函数"""
    _logger_manager.set_level(level)


def configure_logger(module_name: str, config: Dict[str, Any]):
    """配置日志记录器的便捷函数"""
    _logger_manager.configure_module_logger(module_name, config)
