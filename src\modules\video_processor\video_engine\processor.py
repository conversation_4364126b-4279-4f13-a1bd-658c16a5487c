"""视频处理引擎实现

负责视频文件的加载、分析和关键帧提取等功能。
"""

import cv2
import numpy as np
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..interface import VideoEngineInterface
from ....core.models import VideoInfo
from ....common.exceptions import VideoLoadError, ProcessingError
from ....common.logging import get_logger
from ....common.utils import format_file_size


logger = get_logger(__name__)


class VideoEngineImpl(VideoEngineInterface):
    """视频引擎实现类"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.logger.info("视频引擎初始化完成")
    
    def load_video(self, video_path: str) -> VideoInfo:
        """加载视频文件并获取基本信息"""
        try:
            video_path = str(Path(video_path).resolve())
            
            # 检查文件是否存在
            if not Path(video_path).exists():
                raise VideoLoadError(f"视频文件不存在: {video_path}")
            
            # 使用OpenCV打开视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise VideoLoadError(f"无法打开视频文件: {video_path}")
            
            try:
                # 获取视频基本信息
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                # 计算时长
                duration = frame_count / fps if fps > 0 else 0.0
                
                # 获取编码信息
                fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
                codec = self._fourcc_to_string(fourcc)
                
                # 获取文件信息
                file_path = Path(video_path)
                file_size = file_path.stat().st_size
                
                # 检查是否有音频轨道
                has_audio = self._check_audio_track(video_path)
                
                # 创建VideoInfo对象
                video_info = VideoInfo(
                    file_path=video_path,
                    file_name=file_path.name,
                    file_size=file_size,
                    duration=duration,
                    width=width,
                    height=height,
                    fps=fps,
                    codec=codec,
                    has_audio=has_audio,
                    creation_time=datetime.fromtimestamp(file_path.stat().st_ctime),
                    metadata={
                        'frame_count': frame_count,
                        'file_size_formatted': format_file_size(file_size),
                        'resolution': f"{width}x{height}",
                        'aspect_ratio': width / height if height > 0 else 1.0
                    }
                )
                
                self.logger.info(f"视频加载成功: {file_path.name}, "
                               f"分辨率: {width}x{height}, "
                               f"时长: {duration:.2f}秒, "
                               f"帧率: {fps:.2f}fps")
                
                return video_info
                
            finally:
                cap.release()
                
        except VideoLoadError:
            raise
        except Exception as e:
            self.logger.error(f"加载视频失败: {e}")
            raise VideoLoadError(f"加载视频失败: {e}")
    
    def extract_frame(self, video_path: str, timestamp: float, 
                     size: Optional[tuple] = None) -> np.ndarray:
        """提取指定时间戳的帧"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ProcessingError(f"无法打开视频文件: {video_path}")
            
            try:
                # 设置时间戳位置
                cap.set(cv2.CAP_PROP_POS_MSEC, timestamp * 1000)
                
                # 读取帧
                ret, frame = cap.read()
                if not ret:
                    raise ProcessingError(f"无法读取时间戳 {timestamp} 处的帧")
                
                # 转换颜色格式 (BGR -> RGB)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 调整大小
                if size:
                    frame = cv2.resize(frame, size, interpolation=cv2.INTER_AREA)
                
                return frame
                
            finally:
                cap.release()
                
        except ProcessingError:
            raise
        except Exception as e:
            raise ProcessingError(f"提取视频帧失败: {e}")
    
    def extract_frames_batch(self, video_path: str, timestamps: List[float],
                           size: Optional[tuple] = None) -> List[np.ndarray]:
        """批量提取帧"""
        frames = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ProcessingError(f"无法打开视频文件: {video_path}")
            
            try:
                for timestamp in timestamps:
                    # 设置时间戳位置
                    cap.set(cv2.CAP_PROP_POS_MSEC, timestamp * 1000)
                    
                    # 读取帧
                    ret, frame = cap.read()
                    if ret:
                        # 转换颜色格式
                        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        
                        # 调整大小
                        if size:
                            frame = cv2.resize(frame, size, interpolation=cv2.INTER_AREA)
                        
                        frames.append(frame)
                    else:
                        self.logger.warning(f"无法读取时间戳 {timestamp} 处的帧")
                        # 添加空帧占位
                        if frames:
                            frames.append(frames[-1].copy())  # 使用上一帧
                        else:
                            # 创建黑色帧
                            h, w = size if size else (480, 640)
                            frames.append(np.zeros((h, w, 3), dtype=np.uint8))
                
                return frames
                
            finally:
                cap.release()
                
        except Exception as e:
            raise ProcessingError(f"批量提取视频帧失败: {e}")
    
    def analyze_scene_changes(self, video_path: str, threshold: float = 0.3) -> List[float]:
        """分析场景变化"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ProcessingError(f"无法打开视频文件: {video_path}")
            
            scene_changes = []
            prev_frame = None
            frame_index = 0
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            try:
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    
                    # 转换为灰度图
                    gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                    
                    # 缩小图像以提高处理速度
                    small_frame = cv2.resize(gray_frame, (160, 120))
                    
                    if prev_frame is not None:
                        # 计算帧间差异
                        diff = cv2.absdiff(prev_frame, small_frame)
                        diff_score = np.mean(diff) / 255.0
                        
                        # 如果差异超过阈值，认为是场景变化
                        if diff_score > threshold:
                            timestamp = frame_index / fps
                            scene_changes.append(timestamp)
                            self.logger.debug(f"检测到场景变化: {timestamp:.2f}s, 差异: {diff_score:.3f}")
                    
                    prev_frame = small_frame
                    frame_index += 1
                    
                    # 每隔一定帧数处理一次，提高效率
                    if frame_index % 5 == 0:
                        continue
                
                self.logger.info(f"场景变化分析完成，共检测到 {len(scene_changes)} 个场景变化")
                return scene_changes
                
            finally:
                cap.release()
                
        except Exception as e:
            raise ProcessingError(f"场景变化分析失败: {e}")
    
    def _fourcc_to_string(self, fourcc: int) -> str:
        """将FourCC代码转换为字符串"""
        try:
            return "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
        except:
            return "unknown"
    
    def _check_audio_track(self, video_path: str) -> bool:
        """检查视频是否包含音频轨道"""
        try:
            # 这里可以使用更专业的库如ffprobe来检查
            # 暂时使用简单的方法
            import subprocess
            import json
            
            # 尝试使用ffprobe检查音频轨道
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                streams = data.get('streams', [])
                
                # 检查是否有音频流
                for stream in streams:
                    if stream.get('codec_type') == 'audio':
                        return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"检查音频轨道失败: {e}")
            # 默认假设有音频
            return True
