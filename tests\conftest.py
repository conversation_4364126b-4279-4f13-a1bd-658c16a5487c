"""pytest配置文件

定义测试的全局配置、fixtures和工具函数。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from typing import Generator, Dict, Any
import sys

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.models import VideoInfo, ProcessResult, SegmentInfo, ParserType
from src.common.config import get_config_manager
from src.common.logging import get_logger


@pytest.fixture(scope="session")
def test_data_dir() -> Path:
    """测试数据目录"""
    return Path(__file__).parent / "fixtures"


@pytest.fixture(scope="session")
def temp_dir() -> Generator[Path, None, None]:
    """临时目录fixture"""
    temp_path = Path(tempfile.mkdtemp(prefix="videoreader_test_"))
    try:
        yield temp_path
    finally:
        shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def config_manager():
    """配置管理器fixture"""
    return get_config_manager()


@pytest.fixture
def logger():
    """日志器fixture"""
    return get_logger("test")


@pytest.fixture
def sample_video_info() -> VideoInfo:
    """示例视频信息"""
    return VideoInfo(
        file_path="/test/sample.mp4",
        file_name="sample.mp4",
        file_size=1024 * 1024,  # 1MB
        duration=120.0,  # 2分钟
        width=1920,
        height=1080,
        fps=30.0,
        codec="h264",
        has_audio=True,
        audio_codec="aac"
    )


@pytest.fixture
def sample_segments() -> list:
    """示例段落列表"""
    return [
        SegmentInfo(
            id=0,
            start_time=0.0,
            end_time=30.0,
            text="这是第一个段落的内容，包含一些测试文本。",
            confidence=0.95
        ),
        SegmentInfo(
            id=1,
            start_time=30.0,
            end_time=60.0,
            text="这是第二个段落的内容，用于测试搜索功能。",
            confidence=0.92
        ),
        SegmentInfo(
            id=2,
            start_time=60.0,
            end_time=90.0,
            text="第三个段落包含不同的关键词和内容。",
            confidence=0.88
        ),
        SegmentInfo(
            id=3,
            start_time=90.0,
            end_time=120.0,
            text="最后一个段落用于完成测试用例。",
            confidence=0.90
        )
    ]


@pytest.fixture
def sample_process_result(sample_video_info, sample_segments) -> ProcessResult:
    """示例处理结果"""
    return ProcessResult(
        video_info=sample_video_info,
        segments=sample_segments,
        parser_type=ParserType.SCENE_CHANGE,
        parser_config={"threshold": 0.3},
        processing_time=45.5,
        total_segments=len(sample_segments)
    )


@pytest.fixture
def mock_video_file(temp_dir) -> Path:
    """模拟视频文件"""
    video_file = temp_dir / "test_video.mp4"
    # 创建一个空文件作为模拟视频
    video_file.write_bytes(b"fake video content")
    return video_file


@pytest.fixture
def mock_audio_file(temp_dir) -> Path:
    """模拟音频文件"""
    audio_file = temp_dir / "test_audio.wav"
    # 创建一个空文件作为模拟音频
    audio_file.write_bytes(b"fake audio content")
    return audio_file


@pytest.fixture
def test_config() -> Dict[str, Any]:
    """测试配置"""
    return {
        "video": {
            "supported_formats": [".mp4", ".avi", ".mov"],
            "max_file_size": 100 * 1024 * 1024,  # 100MB
            "default_fps": 30
        },
        "audio": {
            "sample_rate": 16000,
            "channels": 1,
            "speech_engines": {
                "whisper": {
                    "model": "base",
                    "language": "zh"
                }
            }
        },
        "parsers": {
            "scene_change": {
                "threshold": 0.3,
                "min_duration": 2.0
            },
            "text_length": {
                "min_length": 50,
                "max_length": 500
            }
        }
    }


class MockVideoProcessor:
    """模拟视频处理器"""
    
    def __init__(self):
        self.is_processing_flag = False
        self.progress = 0.0
        self.message = "就绪"
    
    def process_video(self, video_path, config, progress_callback=None):
        """模拟视频处理"""
        self.is_processing_flag = True
        
        # 模拟处理进度
        for i in range(5):
            self.progress = (i + 1) / 5
            self.message = f"处理步骤 {i + 1}/5"
            if progress_callback:
                progress_callback(self.progress, self.message)
        
        self.is_processing_flag = False
        return True
    
    def get_video_info(self, video_path):
        """模拟获取视频信息"""
        return VideoInfo(
            file_path=video_path,
            file_name=Path(video_path).name,
            file_size=1024,
            duration=60.0,
            width=640,
            height=480,
            fps=25.0,
            codec="h264",
            has_audio=True
        )
    
    def is_processing(self):
        return self.is_processing_flag
    
    def get_processing_progress(self):
        return (self.progress, self.message)
    
    def cancel_processing(self):
        self.is_processing_flag = False
        return True


@pytest.fixture
def mock_video_processor():
    """模拟视频处理器fixture"""
    return MockVideoProcessor()


class MockSearcher:
    """模拟搜索器"""
    
    def __init__(self):
        self.index_built = False
        self.documents = {}
    
    def build_index(self, segments):
        """模拟构建索引"""
        self.index_built = True
        for segment in segments:
            self.documents[segment.id] = segment
        return True
    
    def search_full_text(self, query, max_results=100):
        """模拟全文搜索"""
        if not self.index_built:
            return []
        
        results = []
        for segment in self.documents.values():
            if query.lower() in segment.text.lower():
                from src.core.models import SearchResult
                result = SearchResult(
                    segment=segment,
                    matches=[],
                    relevance_score=0.8
                )
                results.append(result)
        
        return results[:max_results]


@pytest.fixture
def mock_searcher():
    """模拟搜索器fixture"""
    return MockSearcher()


def pytest_configure(config):
    """pytest配置"""
    # 设置测试标记
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "gui: GUI测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试项"""
    # 为没有标记的测试添加unit标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)


@pytest.fixture(autouse=True)
def setup_test_environment(temp_dir):
    """自动设置测试环境"""
    # 设置测试用的临时目录
    import os
    os.environ["VIDEOREADER_TEST_MODE"] = "1"
    os.environ["VIDEOREADER_DATA_DIR"] = str(temp_dir)
    
    yield
    
    # 清理环境变量
    os.environ.pop("VIDEOREADER_TEST_MODE", None)
    os.environ.pop("VIDEOREADER_DATA_DIR", None)
