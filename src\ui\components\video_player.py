"""视频播放器组件

提供视频播放、暂停、跳转等功能的UI组件。
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QSlider, QLabel,
    QFrame, QSizePolicy, QStyle
)
from PySide6.QtCore import Qt, QTimer, Signal, QUrl
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from typing import Optional

try:
    from ...common.logging import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from src.common.logging import get_logger


logger = get_logger(__name__)


class VideoPlayerWidget(QWidget):
    """视频播放器组件"""
    
    # 信号定义
    position_changed = Signal(float)  # 播放位置改变
    duration_changed = Signal(float)  # 视频时长改变
    state_changed = Signal(int)  # 播放状态改变
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 媒体播放器
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)
        self.video_widget = QVideoWidget()
        self.media_player.setVideoOutput(self.video_widget)
        
        # 当前视频信息
        self.current_video_path = None
        self.video_duration = 0.0
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()
        
        self.logger.info("视频播放器组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 视频显示区域
        self.video_widget.setMinimumSize(320, 180)  # 减小最小尺寸以便看到右边距
        self.video_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(self.video_widget)
        
        # 控制面板
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        control_frame.setMaximumHeight(80)
        layout.addWidget(control_frame)
        
        control_layout = QVBoxLayout(control_frame)
        
        # 进度条
        progress_layout = QHBoxLayout()
        
        self.time_label = QLabel("00:00")
        self.time_label.setMinimumWidth(50)
        progress_layout.addWidget(self.time_label)
        
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 0)
        self.position_slider.sliderMoved.connect(self.set_position)
        progress_layout.addWidget(self.position_slider)
        
        self.duration_label = QLabel("00:00")
        self.duration_label.setMinimumWidth(50)
        progress_layout.addWidget(self.duration_label)
        
        control_layout.addLayout(progress_layout)
        
        # 播放控制按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 播放/暂停按钮
        self.play_button = QPushButton()
        self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.play_button.clicked.connect(self.toggle_play_pause)
        button_layout.addWidget(self.play_button)
        
        # 停止按钮
        self.stop_button = QPushButton()
        self.stop_button.setIcon(self.style().standardIcon(QStyle.SP_MediaStop))
        self.stop_button.clicked.connect(self.stop)
        button_layout.addWidget(self.stop_button)
        
        # 音量控制
        volume_label = QLabel("音量:")
        button_layout.addWidget(volume_label)
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_slider.setMaximumWidth(100)
        self.volume_slider.valueChanged.connect(self.set_volume)
        button_layout.addWidget(self.volume_slider)
        
        button_layout.addStretch()
        control_layout.addLayout(button_layout)
        
        # 设置媒体播放器的视频输出
        self.media_player.setVideoOutput(self.video_widget)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # PySide6中QMediaPlayer的信号名称
        try:
            self.media_player.playbackStateChanged.connect(self._on_state_changed)
        except AttributeError:
            # 如果没有playbackStateChanged，尝试stateChanged
            self.media_player.stateChanged.connect(self._on_state_changed)

        self.media_player.positionChanged.connect(self._on_position_changed)
        self.media_player.durationChanged.connect(self._on_duration_changed)

        try:
            self.media_player.errorOccurred.connect(self._on_error)
        except AttributeError:
            # 如果没有errorOccurred，尝试error
            self.media_player.error.connect(self._on_error)
    
    def load_video(self, video_path: str) -> bool:
        """加载视频文件
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            self.logger.info(f"加载视频: {video_path}")
            
            # 停止当前播放
            self.stop()
            
            # 设置媒体内容
            self.media_player.setSource(QUrl.fromLocalFile(video_path))
            
            self.current_video_path = video_path
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载视频失败: {e}")
            return False
    
    def play(self):
        """播放视频"""
        # PySide6中使用playbackState()而不是state()
        try:
            current_state = self.media_player.playbackState()
            if current_state == QMediaPlayer.PlayingState:
                return
        except AttributeError:
            # 如果没有playbackState，使用state
            if self.media_player.state() == QMediaPlayer.PlayingState:
                return

        self.media_player.play()
        self.logger.debug("开始播放视频")

    def pause(self):
        """暂停视频"""
        try:
            current_state = self.media_player.playbackState()
            if current_state == QMediaPlayer.PausedState:
                return
        except AttributeError:
            if self.media_player.state() == QMediaPlayer.PausedState:
                return

        self.media_player.pause()
        self.logger.debug("暂停视频")
    
    def stop(self):
        """停止视频"""
        self.media_player.stop()
        self.logger.debug("停止视频")
    
    def toggle_play_pause(self):
        """切换播放/暂停状态"""
        try:
            current_state = self.media_player.playbackState()
            if current_state == QMediaPlayer.PlayingState:
                self.pause()
            else:
                self.play()
        except AttributeError:
            if self.media_player.state() == QMediaPlayer.PlayingState:
                self.pause()
            else:
                self.play()
    
    def seek_to_time(self, time_seconds: float):
        """跳转到指定时间
        
        Args:
            time_seconds: 目标时间（秒）
        """
        if self.video_duration > 0:
            position_ms = int(time_seconds * 1000)
            self.media_player.setPosition(position_ms)
            self.logger.debug(f"跳转到时间: {time_seconds:.2f}s")
    
    def seek_to_segment(self, start_time: float, end_time: Optional[float] = None):
        """跳转到指定段落
        
        Args:
            start_time: 段落开始时间
            end_time: 段落结束时间（可选）
        """
        self.seek_to_time(start_time)
        
        # 如果指定了结束时间，可以设置播放范围
        if end_time is not None:
            # 这里可以实现段落播放逻辑
            pass
    
    def set_position(self, position):
        """设置播放位置（由滑块触发）"""
        self.media_player.setPosition(position)
    
    def set_volume(self, volume):
        """设置音量"""
        self.media_player.setVolume(volume)
    
    def _on_state_changed(self, state):
        """播放状态改变处理"""
        if state == QMediaPlayer.PlayingState:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPause))
        else:
            self.play_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))

        self.state_changed.emit(state)
    
    def _on_position_changed(self, position):
        """播放位置改变处理"""
        # 更新进度条
        self.position_slider.setValue(position)
        
        # 更新时间显示
        time_seconds = position / 1000.0
        self.time_label.setText(self._format_time(time_seconds))
        
        # 发送信号
        self.position_changed.emit(time_seconds)
    
    def _on_duration_changed(self, duration):
        """视频时长改变处理"""
        self.video_duration = duration / 1000.0
        self.position_slider.setRange(0, duration)
        self.duration_label.setText(self._format_time(self.video_duration))
        
        # 发送信号
        self.duration_changed.emit(self.video_duration)
    
    def _on_error(self, error):
        """错误处理"""
        error_string = str(error)
        self.logger.error(f"视频播放错误: {error_string}")
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def get_current_position(self) -> float:
        """获取当前播放位置（秒）"""
        return self.media_player.position() / 1000.0
    
    def get_duration(self) -> float:
        """获取视频总时长（秒）"""
        return self.video_duration
    
    def is_playing(self) -> bool:
        """检查是否正在播放"""
        try:
            return self.media_player.playbackState() == QMediaPlayer.PlayingState
        except AttributeError:
            return self.media_player.state() == QMediaPlayer.PlayingState

    def is_paused(self) -> bool:
        """检查是否已暂停"""
        try:
            return self.media_player.playbackState() == QMediaPlayer.PausedState
        except AttributeError:
            return self.media_player.state() == QMediaPlayer.PausedState
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        self.media_player.setSource(QUrl())
        self.current_video_path = None
