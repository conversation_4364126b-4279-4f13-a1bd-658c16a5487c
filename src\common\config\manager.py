"""配置管理器

负责配置的加载、保存和管理。
"""

import json
import os
import yaml
from pathlib import Path
from typing import Any, Dict, Optional, Union
from copy import deepcopy

# TOML支持
try:
    import tomllib  # Python 3.11+
    import tomli_w
    TOML_AVAILABLE = True
except ImportError:
    try:
        import tomli as tomllib
        import tomli_w
        TOML_AVAILABLE = True
    except ImportError:
        try:
            import toml
            TOML_AVAILABLE = True
            # 为了兼容性，创建统一的接口
            tomllib = toml
            tomli_w = toml
        except ImportError:
            TOML_AVAILABLE = False

from .defaults import DEFAULT_CONFIG
from ..exceptions import ConfigurationError, FileOperationError
from ..logging import get_logger


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[Union[str, Path]] = None):
        self.logger = get_logger(__name__)

        # 设置配置文件路径
        if config_file is None:
            config_dir = Path.home() / '.videoreader'
            config_dir.mkdir(exist_ok=True)
            # 优先使用TOML格式
            if TOML_AVAILABLE:
                self.config_file = config_dir / 'config.toml'
            else:
                self.config_file = config_dir / 'config.json'
        else:
            self.config_file = Path(config_file)
        
        # 初始化配置
        self._config = deepcopy(DEFAULT_CONFIG)
        self._load_config()
        
        # 确保必要的目录存在
        self._ensure_directories()
    
    def _load_config(self):
        """加载配置文件"""
        if not self.config_file.exists():
            self.logger.info(f"配置文件不存在，使用默认配置: {self.config_file}")
            self.save()
            return
        
        try:
            suffix = self.config_file.suffix.lower()

            if suffix == '.toml':
                if not TOML_AVAILABLE:
                    raise ConfigurationError("TOML支持不可用，请安装tomli或toml库")
                with open(self.config_file, 'rb') as f:
                    user_config = tomllib.load(f)
            elif suffix in ['.yaml', '.yml']:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f)
            else:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
            
            # 递归合并配置
            self._merge_config(self._config, user_config)
            self.logger.info(f"配置文件加载成功: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise ConfigurationError(f"无法加载配置文件: {e}")
    
    def _merge_config(self, default: Dict, user: Dict):
        """递归合并配置"""
        for key, value in user.items():
            if key in default:
                if isinstance(default[key], dict) and isinstance(value, dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
            else:
                default[key] = value
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        try:
            for path_key, path_value in self._config['paths'].items():
                if isinstance(path_value, (str, Path)):
                    Path(path_value).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.logger.warning(f"创建目录失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        支持点号分隔的嵌套键，如 'video.max_file_size'
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
        except Exception:
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值
        
        支持点号分隔的嵌套键，如 'video.max_file_size'
        """
        keys = key.split('.')
        config = self._config
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        self.logger.debug(f"配置已更新: {key} = {value}")
    
    def _convert_paths_to_strings(self, obj):
        """递归将Path对象转换为字符串，并处理None值"""
        if obj is None:
            return ""  # 将None转换为空字符串，因为TOML不支持null
        elif isinstance(obj, Path):
            return str(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_paths_to_strings(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_paths_to_strings(item) for item in obj]
        else:
            return obj

    def save(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)

            # 转换Path对象为字符串
            config_to_save = self._convert_paths_to_strings(self._config)
            suffix = self.config_file.suffix.lower()

            if suffix == '.toml':
                if not TOML_AVAILABLE:
                    raise ConfigurationError("TOML支持不可用，请安装tomli_w或toml库")
                with open(self.config_file, 'wb') as f:
                    tomli_w.dump(config_to_save, f)
            elif suffix in ['.yaml', '.yml']:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config_to_save, f, default_flow_style=False, allow_unicode=True)
            else:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_to_save, f, indent=2, ensure_ascii=False)

            self.logger.info(f"配置已保存: {self.config_file}")

        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            raise FileOperationError(f"无法保存配置文件: {e}")
    
    def reload(self):
        """重新加载配置"""
        self._config = deepcopy(DEFAULT_CONFIG)
        self._load_config()
        self.logger.info("配置已重新加载")
    
    def reset(self):
        """重置为默认配置"""
        self._config = deepcopy(DEFAULT_CONFIG)
        self.save()
        self.logger.info("配置已重置为默认值")
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置段"""
        return self.get(section, {})
    
    def update_section(self, section: str, config: Dict[str, Any]):
        """更新配置段"""
        if section in self._config:
            if isinstance(self._config[section], dict):
                self._config[section].update(config)
            else:
                self._config[section] = config
        else:
            self._config[section] = config
        
        self.logger.debug(f"配置段已更新: {section}")
    
    def to_dict(self) -> Dict[str, Any]:
        """获取完整配置字典"""
        return deepcopy(self._config)
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证必要的配置项
            required_sections = ['app', 'paths', 'video', 'audio']
            for section in required_sections:
                if section not in self._config:
                    raise ConfigurationError(f"缺少必要的配置段: {section}")
            
            # 验证路径配置
            paths = self._config.get('paths', {})
            for path_key, path_value in paths.items():
                if isinstance(path_value, (str, Path)):
                    path = Path(path_value)
                    if not path.parent.exists():
                        self.logger.warning(f"路径的父目录不存在: {path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False


# 全局配置管理器实例
_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return get_config_manager().get(key, default)


def set_config(key: str, value: Any):
    """设置配置值的便捷函数"""
    get_config_manager().set(key, value)


def save_config():
    """保存配置的便捷函数"""
    get_config_manager().save()
