# TOML配置文件指南

VideoReader使用TOML格式的配置文件来管理各种设置，提供了比环境变量更加结构化和易于管理的配置方式。

## 概述

TOML（Tom's Obvious, Minimal Language）是一种易于阅读的配置文件格式。VideoReader的配置文件默认位于：
- Linux/macOS: `~/.videoreader/config.toml`
- Windows: `%USERPROFILE%\.videoreader\config.toml`

## 配置优先级

VideoReader按以下优先级读取配置：
1. **代码参数** - 直接传递给类构造函数的参数
2. **TOML配置文件** - `~/.videoreader/config.toml`
3. **环境变量** - 系统环境变量

## 快速开始

### 1. 使用配置向导（推荐）

```bash
python scripts/create_config.py
```

配置向导将引导您完成所有必要的配置项设置。

### 2. 手动创建配置文件

复制项目根目录的 `config.toml` 模板文件到配置目录：

```bash
# Linux/macOS
cp config.toml ~/.videoreader/config.toml

# Windows
copy config.toml %USERPROFILE%\.videoreader\config.toml
```

然后编辑配置文件，填入您的实际配置信息。

## 配置文件结构

### 应用基础配置

```toml
[app]
name = "VideoReader"
version = "1.0.0"
debug = false
log_level = "INFO"
```

### 语音识别配置

```toml
[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"
model = "paraformer-v2"
endpoint = "https://dashscope.aliyuncs.com"

[audio.speech_engines.azure]
api_key = "your-azure-speech-key"
region = "eastasia"
endpoint = "your-azure-endpoint"

[audio.speech_engines.whisper]
model = "base"
language = "zh"
device = "auto"
```

### 文件上传器配置

```toml
[file_uploader]
default_uploader = "auto"  # auto, cloudreve, oss, http, local

[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
username = "your-username"
password = "your-password"
# 或者使用访问令牌（推荐）
token = "your-access-token"
timeout = 60
chunk_size = 5242880  # 5MB
max_retries = 3

[file_uploader.uploaders.oss]
access_key_id = "your-oss-access-key-id"
access_key_secret = "your-oss-access-key-secret"
endpoint = "your-oss-endpoint"
bucket_name = "your-bucket-name"
timeout = 60
```

### 视频处理配置

```toml
[video]
supported_formats = [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm"]
max_file_size = 2147483648  # 2GB
default_fps = 30
thumbnail_size = [320, 240]
keyframe_size = [640, 480]
```

### 解析器配置

```toml
[parsers.scene_change]
threshold = 0.3
min_duration = 2.0
max_duration = 300.0

[parsers.text_length]
min_length = 50
max_length = 500
overlap_ratio = 0.1
```

## 常用配置示例

### 最小配置（仅Paraformer + Cloudreve）

```toml
[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"

[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
username = "your-username"
password = "your-password"
```

### 完整配置（多引擎支持）

```toml
[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"
model = "paraformer-v2"

[audio.speech_engines.azure]
api_key = "your-azure-speech-key"
region = "eastasia"

[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
token = "your-access-token"

[file_uploader.uploaders.oss]
access_key_id = "your-oss-key-id"
access_key_secret = "your-oss-key-secret"
endpoint = "your-oss-endpoint"
bucket_name = "your-bucket-name"
```

## 配置验证

### 测试配置文件

```bash
# 测试TOML配置功能
python test_toml_config.py

# 测试具体功能
python test_cloudreve_uploader.py
python test_paraformer_cloudreve_integration.py
```

### 查看当前配置

```python
from src.common.config import get_config_manager

config_manager = get_config_manager()
print(f"配置文件: {config_manager.config_file}")
print(f"Paraformer API密钥: {config_manager.get('audio.speech_engines.paraformer.api_key', '未配置')}")
print(f"Cloudreve URL: {config_manager.get('file_uploader.uploaders.cloudreve.base_url', '未配置')}")
```

## 安全注意事项

1. **保护敏感信息**: API密钥、密码等敏感信息应妥善保管
2. **文件权限**: 确保配置文件只有当前用户可读写
3. **版本控制**: 不要将包含敏感信息的配置文件提交到版本控制系统
4. **备份**: 定期备份配置文件

### 设置文件权限（Linux/macOS）

```bash
chmod 600 ~/.videoreader/config.toml
```

## 故障排除

### 常见问题

1. **配置文件不生效**
   - 检查文件路径是否正确
   - 检查TOML语法是否正确
   - 确认已安装tomli/tomli-w库

2. **TOML语法错误**
   - 使用在线TOML验证器检查语法
   - 注意字符串需要用引号包围
   - 注意数组和表的语法

3. **配置项不生效**
   - 检查配置项名称是否正确
   - 确认配置优先级（代码参数 > 配置文件 > 环境变量）

### 调试配置

启用调试模式查看配置加载过程：

```toml
[app]
debug = true
log_level = "DEBUG"
```

## 迁移指南

### 从环境变量迁移

如果您之前使用环境变量配置，可以按以下对应关系迁移到TOML配置：

| 环境变量 | TOML配置路径 |
|---------|-------------|
| `DASHSCOPE_API_KEY` | `audio.speech_engines.paraformer.api_key` |
| `CLOUDREVE_BASE_URL` | `file_uploader.uploaders.cloudreve.base_url` |
| `CLOUDREVE_USERNAME` | `file_uploader.uploaders.cloudreve.username` |
| `CLOUDREVE_PASSWORD` | `file_uploader.uploaders.cloudreve.password` |
| `CLOUDREVE_TOKEN` | `file_uploader.uploaders.cloudreve.token` |
| `OSS_ACCESS_KEY_ID` | `file_uploader.uploaders.oss.access_key_id` |
| `OSS_ACCESS_KEY_SECRET` | `file_uploader.uploaders.oss.access_key_secret` |
| `OSS_ENDPOINT` | `file_uploader.uploaders.oss.endpoint` |
| `OSS_BUCKET_NAME` | `file_uploader.uploaders.oss.bucket_name` |

## 高级用法

### 多环境配置

您可以为不同环境创建不同的配置文件：

```bash
# 开发环境
python -c "
from src.common.config import ConfigManager
config = ConfigManager('config-dev.toml')
"

# 生产环境
python -c "
from src.common.config import ConfigManager
config = ConfigManager('config-prod.toml')
"
```

### 动态配置更新

```python
from src.common.config import get_config_manager

config_manager = get_config_manager()

# 更新配置
config_manager.set('file_uploader.uploaders.cloudreve.timeout', 120)

# 保存配置
config_manager.save()

# 重新加载配置
config_manager.reload()
```

## 参考资源

- [TOML官方文档](https://toml.io/)
- [Python tomli库文档](https://pypi.org/project/tomli/)
- [VideoReader配置示例](../config.toml)
