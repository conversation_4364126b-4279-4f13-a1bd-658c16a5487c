#!/usr/bin/env python3
"""ParaformerRecognizer与Cloudreve集成测试

测试ParaformerRecognizer使用Cloudreve上传器的完整流程。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer
from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager, CloudreveUploader
from src.common.logging import get_logger

logger = get_logger(__name__)


def check_environment():
    """检查环境配置"""
    print("=== 环境检查 ===")
    
    # 检查阿里云API密钥
    dashscope_key = os.getenv('DASHSCOPE_API_KEY')
    if dashscope_key:
        print("✓ DASHSCOPE_API_KEY 已配置")
    else:
        print("✗ DASHSCOPE_API_KEY 未配置")
    
    # 检查Cloudreve配置
    cloudreve_url = os.getenv('CLOUDREVE_BASE_URL')
    cloudreve_username = os.getenv('CLOUDREVE_USERNAME')
    cloudreve_password = os.getenv('CLOUDREVE_PASSWORD')
    cloudreve_token = os.getenv('CLOUDREVE_TOKEN')
    
    if cloudreve_url:
        print("✓ CLOUDREVE_BASE_URL 已配置")
    else:
        print("✗ CLOUDREVE_BASE_URL 未配置")
    
    if cloudreve_token:
        print("✓ CLOUDREVE_TOKEN 已配置")
    elif cloudreve_username and cloudreve_password:
        print("✓ CLOUDREVE_USERNAME 和 CLOUDREVE_PASSWORD 已配置")
    else:
        print("✗ Cloudreve认证信息未配置")
    
    # 检查测试文件
    test_file = "test.mp4"
    if os.path.exists(test_file):
        print(f"✓ 测试文件存在: {test_file}")
    else:
        print(f"✗ 测试文件不存在: {test_file}")
    
    return all([
        dashscope_key,
        cloudreve_url,
        (cloudreve_token or (cloudreve_username and cloudreve_password)),
        os.path.exists(test_file)
    ])


def test_cloudreve_uploader():
    """测试Cloudreve上传器"""
    print("\n=== 测试Cloudreve上传器 ===")
    
    try:
        uploader = CloudreveUploader()
        
        if uploader.is_available():
            print("✓ Cloudreve上传器可用")
            return True
        else:
            print("✗ Cloudreve上传器不可用")
            return False
    except Exception as e:
        print(f"✗ Cloudreve上传器测试失败: {e}")
        return False


def test_file_upload_manager():
    """测试文件上传管理器"""
    print("\n=== 测试文件上传管理器 ===")
    
    try:
        manager = FileUploadManager()
        available_uploaders = manager.get_available_uploaders()
        
        print(f"可用的上传器: {available_uploaders}")
        
        if 'cloudreve' in available_uploaders:
            print("✓ Cloudreve上传器在管理器中可用")
            return True
        else:
            print("✗ Cloudreve上传器在管理器中不可用")
            return False
    except Exception as e:
        print(f"✗ 文件上传管理器测试失败: {e}")
        return False


def test_paraformer_recognizer():
    """测试ParaformerRecognizer"""
    print("\n=== 测试ParaformerRecognizer ===")
    
    try:
        recognizer = ParaformerRecognizer(model='paraformer-v2')
        
        if recognizer.is_available():
            print("✓ ParaformerRecognizer可用")
            
            # 检查文件上传器
            if recognizer.file_uploader.is_uploader_available('cloudreve'):
                print("✓ ParaformerRecognizer可以使用Cloudreve上传器")
                return True
            else:
                print("✗ ParaformerRecognizer无法使用Cloudreve上传器")
                return False
        else:
            print("✗ ParaformerRecognizer不可用")
            return False
    except Exception as e:
        print(f"✗ ParaformerRecognizer测试失败: {e}")
        return False


def test_full_integration():
    """测试完整集成流程"""
    print("\n=== 测试完整集成流程 ===")
    
    test_file = "test.mp4"
    if not os.path.exists(test_file):
        print(f"✗ 测试文件不存在: {test_file}")
        return False
    
    try:
        # 创建ParaformerRecognizer
        recognizer = ParaformerRecognizer(model='paraformer-v2')
        
        if not recognizer.is_available():
            print("✗ ParaformerRecognizer不可用")
            return False
        
        print("✓ ParaformerRecognizer已创建")
        
        # 检查上传器优先级
        manager = recognizer.file_uploader
        available_uploaders = manager.get_available_uploaders()
        print(f"可用的上传器: {available_uploaders}")
        
        if 'cloudreve' not in available_uploaders:
            print("✗ Cloudreve上传器不可用，无法进行完整测试")
            return False
        
        print("✓ 准备进行语音识别（使用Cloudreve上传）")
        print("注意：这将实际上传文件到Cloudreve并调用阿里云API")
        
        # 询问用户是否继续
        response = input("是否继续进行实际的语音识别测试？(y/N): ")
        if response.lower() != 'y':
            print("用户取消了实际测试")
            return True
        
        # 执行语音识别
        print(f"开始识别文件: {test_file}")
        segments = recognizer.recognize(test_file, language='zh')
        
        print(f"✓ 识别完成，共 {len(segments)} 个段落")
        
        # 显示前几个段落
        for i, segment in enumerate(segments[:3]):
            print(f"段落 {i+1}: [{segment.time_range_str}] {segment.text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 完整集成测试失败: {e}")
        return False


def show_configuration_help():
    """显示配置帮助"""
    print("\n=== 配置帮助 ===")
    print("要完成集成测试，请选择以下配置方式之一：")
    print()
    print("方法1：使用TOML配置文件（推荐）")
    print("   1. 运行配置向导：python scripts/create_config.py")
    print("   2. 或手动编辑：~/.videoreader/config.toml")
    print("   3. 配置示例：")
    print("      [audio.speech_engines.paraformer]")
    print("      api_key = 'your-dashscope-api-key'")
    print("      ")
    print("      [file_uploader.uploaders.cloudreve]")
    print("      base_url = 'http://your-cloudreve-server.com:5212'")
    print("      username = 'your-username'")
    print("      password = 'your-password'")
    print()
    print("方法2：使用环境变量")
    print("   export DASHSCOPE_API_KEY='your-dashscope-api-key'")
    print("   export CLOUDREVE_BASE_URL='http://your-cloudreve-server.com:5212'")
    print("   export CLOUDREVE_USERNAME='your-username'")
    print("   export CLOUDREVE_PASSWORD='your-password'")
    print("   # 或者使用访问令牌")
    print("   export CLOUDREVE_TOKEN='your-access-token'")
    print()
    print("3. 准备测试文件：")
    print("   确保项目根目录下有test.mp4文件")
    print()
    print("Windows用户环境变量设置请使用set命令替代export")
    print()
    print("配置优先级：代码参数 > TOML配置文件 > 环境变量")


def main():
    """主函数"""
    print("ParaformerRecognizer与Cloudreve集成测试")
    print("=" * 60)
    
    # 检查环境
    env_ok = check_environment()
    
    if not env_ok:
        print("\n环境配置不完整，无法进行完整测试")
        show_configuration_help()
        return
    
    # 逐步测试
    tests = [
        ("Cloudreve上传器", test_cloudreve_uploader),
        ("文件上传管理器", test_file_upload_manager),
        ("ParaformerRecognizer", test_paraformer_recognizer),
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        if not test_func():
            all_passed = False
            break
    
    if all_passed:
        # 进行完整集成测试
        test_full_integration()
    
    print("\n=== 测试总结 ===")
    if all_passed:
        print("✓ 所有基础测试通过")
        print("集成配置正确，可以使用ParaformerRecognizer + Cloudreve进行语音识别")
    else:
        print("✗ 部分测试失败")
        print("请检查配置并解决问题后重新测试")
    
    show_configuration_help()


if __name__ == "__main__":
    main()
