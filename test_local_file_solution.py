#!/usr/bin/env python3
"""测试本地文件解决方案

测试使用本地HTTP服务器为Paraformer提供文件访问的解决方案。
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer, SpeechRecognizer
from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager
from src.utils.local_file_server import LocalFileServer, get_global_server
from src.common.exceptions import SpeechRecognitionError
from src.common.logging import get_logger


def check_environment():
    """检查环境配置"""
    print("=== 环境检查 ===")
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if api_key:
        print(f"✓ DASHSCOPE_API_KEY: {api_key[:10]}...")
    else:
        print("✗ DASHSCOPE_API_KEY 未配置")
        return False
    
    # 检查SDK
    try:
        import dashscope
        print("✓ DashScope SDK 已安装")
    except ImportError:
        print("✗ DashScope SDK 未安装")
        return False
    
    # 检查test.mp4文件
    test_file = Path("test.mp4")
    if test_file.exists():
        print(f"✓ test.mp4文件存在: ({test_file.stat().st_size / 1024 / 1024:.1f} MB)")
    else:
        print("✗ test.mp4文件不存在")
        return False
    
    return True


def test_local_http_server():
    """测试本地HTTP服务器"""
    print("=== 测试本地HTTP服务器 ===")
    
    try:
        # 创建测试文件
        test_file = Path("test_http_server.txt")
        test_file.write_text("这是一个测试文件，用于验证本地HTTP服务器功能。", encoding='utf-8')
        
        # 启动服务器并测试
        with LocalFileServer() as server:
            print(f"服务器信息: {server.get_server_info()}")
            
            # 添加文件
            url = server.add_file(str(test_file))
            print(f"文件URL: {url}")
            
            # 测试访问
            import requests
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print("✓ 本地HTTP服务器工作正常")
                print(f"  响应内容: {response.text[:50]}...")
                return True
            else:
                print(f"✗ 文件访问失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"✗ 本地HTTP服务器测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()


def test_file_upload_manager():
    """测试文件上传管理器"""
    print("=== 测试文件上传管理器 ===")
    
    try:
        uploader = FileUploadManager()
        
        # 检查可用的上传器
        available_uploaders = uploader.get_available_uploaders()
        print(f"可用的上传器: {available_uploaders}")
        
        if 'http' not in available_uploaders:
            print("✗ HTTP上传器不可用")
            return None
        
        print("✓ HTTP上传器可用")
        
        # 测试上传test.mp4
        print("上传test.mp4文件...")
        url = uploader.upload("test.mp4", uploader='http')
        
        print(f"✓ 文件上传成功: {url}")
        return url
        
    except Exception as e:
        print(f"✗ 文件上传失败: {e}")
        return None


def test_paraformer_with_local_file():
    """测试Paraformer处理本地文件"""
    print("=== 测试Paraformer处理本地文件 ===")
    
    try:
        # 创建Paraformer识别器
        recognizer = ParaformerRecognizer(model='paraformer-v2')
        
        if not recognizer.is_available():
            print("✗ ParaformerRecognizer不可用")
            return None
        
        print("✓ ParaformerRecognizer可用")
        
        # 直接使用本地文件路径
        print("开始识别本地文件: test.mp4")
        print("注意：这会自动启动本地HTTP服务器并上传文件")
        
        segments = recognizer.recognize("test.mp4", language='zh')
        
        print(f"✓ 识别完成，共 {len(segments)} 个段落")
        return segments
        
    except Exception as e:
        print(f"✗ 识别失败: {e}")
        return None


def test_speech_recognizer_manager():
    """测试通过SpeechRecognizer管理器"""
    print("=== 测试SpeechRecognizer管理器 ===")
    
    try:
        speech_recognizer = SpeechRecognizer()
        
        # 检查可用引擎
        available_engines = speech_recognizer.get_available_engines()
        print(f"可用的识别引擎: {available_engines}")
        
        if 'paraformer' not in available_engines:
            print("✗ Paraformer引擎不可用")
            return None
        
        print("✓ Paraformer引擎可用")
        
        # 使用本地文件
        print("通过管理器识别本地文件: test.mp4")
        segments = speech_recognizer.recognize(
            audio_path="test.mp4",
            language='zh',
            engine='paraformer'
        )
        
        print(f"✓ 识别完成，共 {len(segments)} 个段落")
        return segments
        
    except Exception as e:
        print(f"✗ 识别失败: {e}")
        return None


def display_results(segments, title="识别结果"):
    """显示识别结果"""
    if not segments:
        print("没有识别结果")
        return
    
    print(f"\n=== {title} ===")
    print(f"总段落数: {len(segments)}")
    print()
    
    for i, segment in enumerate(segments, 1):
        print(f"段落 {i}:")
        print(f"  时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s ({segment.duration:.2f}s)")
        print(f"  文本: {segment.text}")
        print(f"  置信度: {segment.confidence:.2f}")
        if segment.speaker_id:
            print(f"  说话人: {segment.speaker_id}")
        print()
    
    # 完整文本
    full_text = ' '.join(seg.text for seg in segments)
    print("=== 完整文本 ===")
    print(full_text)
    print()


def save_results(segments, output_file: str):
    """保存结果到文件"""
    if not segments:
        return
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("test.mp4 本地文件Paraformer识别结果\n")
            f.write("=" * 50 + "\n\n")
            
            for i, segment in enumerate(segments, 1):
                f.write(f"段落 {i}:\n")
                f.write(f"时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s\n")
                f.write(f"文本: {segment.text}\n")
                f.write(f"置信度: {segment.confidence:.2f}\n")
                if segment.speaker_id:
                    f.write(f"说话人: {segment.speaker_id}\n")
                f.write("\n")
            
            # 完整文本
            f.write("\n" + "=" * 50 + "\n")
            f.write("完整文本:\n")
            f.write("=" * 50 + "\n")
            full_text = ' '.join(seg.text for seg in segments)
            f.write(full_text)
        
        print(f"✓ 结果已保存到 {output_file}")
        
    except Exception as e:
        print(f"✗ 保存失败: {e}")


def main():
    """主函数"""
    logger = get_logger(__name__)
    logger.info("开始测试本地文件解决方案")
    
    print("本地文件Paraformer识别解决方案测试")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n环境检查失败，请解决上述问题后重试")
        return
    
    print()
    
    # 测试1：本地HTTP服务器
    print("=" * 60)
    server_ok = test_local_http_server()
    
    if not server_ok:
        print("本地HTTP服务器测试失败，无法继续")
        return
    
    # 测试2：文件上传管理器
    print("\n" + "=" * 60)
    upload_url = test_file_upload_manager()
    
    # 测试3：Paraformer直接处理本地文件
    print("\n" + "=" * 60)
    segments1 = test_paraformer_with_local_file()
    
    if segments1:
        display_results(segments1, "直接调用ParaformerRecognizer结果")
        save_results(segments1, "test_local_file_direct_results.txt")
    
    # 测试4：通过SpeechRecognizer管理器
    print("\n" + "=" * 60)
    segments2 = test_speech_recognizer_manager()
    
    if segments2:
        display_results(segments2, "SpeechRecognizer管理器结果")
        save_results(segments2, "test_local_file_manager_results.txt")
    
    # 总结
    print("\n" + "=" * 60)
    print("=== 测试总结 ===")
    
    print(f"✓ 本地HTTP服务器: {'成功' if server_ok else '失败'}")
    print(f"✓ 文件上传管理器: {'成功' if upload_url else '失败'}")
    print(f"✓ 直接调用Paraformer: {'成功' if segments1 else '失败'}")
    print(f"✓ 管理器调用Paraformer: {'成功' if segments2 else '失败'}")
    
    if segments1 or segments2:
        print("\n🎉 本地文件解决方案测试成功！")
        print("✓ 可以直接使用本地文件路径进行Paraformer识别")
        print("✓ 系统会自动启动HTTP服务器并上传文件")
        print("✓ 无需手动处理文件上传和URL转换")
    else:
        print("\n❌ 本地文件解决方案测试失败")
    
    print("\n使用方法:")
    print("1. 直接调用: ParaformerRecognizer().recognize('test.mp4', 'zh')")
    print("2. 管理器调用: SpeechRecognizer().recognize('test.mp4', 'zh', 'paraformer')")
    print("3. 系统会自动处理文件上传和URL转换")
    
    # 清理
    try:
        from src.utils.local_file_server import stop_global_server
        stop_global_server()
        print("\n✓ 已清理本地HTTP服务器")
    except:
        pass
    
    print("\n测试完成！")


if __name__ == '__main__':
    main()
