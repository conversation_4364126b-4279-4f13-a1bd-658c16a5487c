#!/usr/bin/env python3
"""Cloudreve API端点发现"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def discover_api_endpoints():
    """发现正确的API端点"""
    print("=== Cloudreve API端点发现 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    try:
        import requests
        
        # 常见的API端点模式
        api_patterns = [
            # Cloudreve v3/v4 API模式
            "/api/v3/user/session",
            "/api/v3/site/ping",
            "/api/v3/file/upload",
            
            # 可能的其他模式
            "/api/user/session",
            "/api/site/ping", 
            "/api/file/upload",
            
            # 直接路径
            "/user/session",
            "/site/ping",
            "/file/upload",
            
            # 带版本号的其他模式
            "/v1/user/session",
            "/v2/user/session",
            "/v4/user/session",
        ]
        
        print(f"测试服务器: {base_url}")
        print(f"用户名: {username}")
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
        })
        
        # 测试每个可能的API端点
        for pattern in api_patterns:
            url = f"{base_url}{pattern}"
            print(f"\n测试: {url}")
            
            try:
                # 先测试GET请求
                response = session.get(url, timeout=10)
                print(f"  GET {response.status_code}: {response.text[:100]}")
                
                # 如果是登录端点，测试POST请求
                if "session" in pattern:
                    login_data = {'username': username, 'password': password}
                    
                    # 测试JSON格式
                    session.headers['Content-Type'] = 'application/json'
                    response = session.post(url, json=login_data, timeout=10)
                    print(f"  POST JSON {response.status_code}: {response.text[:100]}")
                    
                    # 测试表单格式
                    session.headers['Content-Type'] = 'application/x-www-form-urlencoded'
                    response = session.post(url, data=login_data, timeout=10)
                    print(f"  POST FORM {response.status_code}: {response.text[:100]}")
                    
                    # 重置Content-Type
                    session.headers.pop('Content-Type', None)
                    
            except Exception as e:
                print(f"  错误: {e}")
        
        # 尝试查找API文档或配置信息
        print("\n=== 查找API信息 ===")
        info_endpoints = [
            "/api",
            "/api/v3",
            "/api/v3/site/config",
            "/site/config",
            "/config",
            "/.well-known/api",
            "/swagger",
            "/docs"
        ]
        
        for endpoint in info_endpoints:
            try:
                url = f"{base_url}{endpoint}"
                response = session.get(url, timeout=10)
                if response.status_code == 200 and 'json' in response.headers.get('content-type', '').lower():
                    print(f"\n找到API信息: {url}")
                    print(f"响应: {response.text[:300]}")
            except:
                pass
                
    except ImportError:
        print("❌ 需要安装requests库")


def test_manual_api_call():
    """手动测试API调用"""
    print("\n=== 手动API测试 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    try:
        import requests
        
        # 基于Cloudreve官方文档，尝试正确的API路径
        # Cloudreve v3+ 通常使用 /api/v3/ 前缀
        
        print("尝试Cloudreve v3 API...")
        session = requests.Session()
        
        # 1. 获取站点配置（不需要认证）
        try:
            config_url = f"{base_url}/api/v3/site/config"
            response = session.get(config_url, timeout=10)
            print(f"站点配置 {response.status_code}: {response.text[:200]}")
            
            if response.status_code == 200:
                try:
                    config_data = response.json()
                    print("✅ 找到v3 API!")
                    print(f"站点配置: {json.dumps(config_data, indent=2, ensure_ascii=False)[:500]}")
                except:
                    pass
        except Exception as e:
            print(f"站点配置请求失败: {e}")
        
        # 2. 尝试登录
        login_endpoints = [
            "/api/v3/user/session",
            "/api/v3/auth/login", 
            "/api/v3/user/login"
        ]
        
        for endpoint in login_endpoints:
            try:
                url = f"{base_url}{endpoint}"
                print(f"\n尝试登录端点: {url}")
                
                # 设置正确的请求头
                headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
                }
                
                login_data = {
                    'userName': username,  # 有些API使用userName
                    'Password': password
                }
                
                response = session.post(url, json=login_data, headers=headers, timeout=30)
                print(f"  响应状态: {response.status_code}")
                print(f"  响应头: {dict(response.headers)}")
                print(f"  响应内容: {response.text[:300]}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"  JSON解析成功: {json.dumps(result, indent=2, ensure_ascii=False)[:500]}")
                    except:
                        print("  非JSON响应")
                
                # 也尝试标准的username/password字段
                login_data2 = {
                    'username': username,
                    'password': password
                }
                
                response2 = session.post(url, json=login_data2, headers=headers, timeout=30)
                print(f"  备用格式 {response2.status_code}: {response2.text[:100]}")
                
            except Exception as e:
                print(f"  登录测试失败: {e}")
                
    except ImportError:
        print("❌ 需要安装requests库")


def main():
    """主函数"""
    print("Cloudreve API端点发现工具")
    print("=" * 60)
    
    discover_api_endpoints()
    test_manual_api_call()
    
    print("\n" + "=" * 60)
    print("发现完成！")
    print("\n根据测试结果，我们需要:")
    print("1. 确定正确的API端点路径")
    print("2. 确定正确的请求格式")
    print("3. 更新CloudreveUploader代码")


if __name__ == "__main__":
    main()
