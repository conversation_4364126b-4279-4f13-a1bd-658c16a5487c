"""搜索器实现

负责执行各种类型的搜索操作。
"""

import re
import time
from typing import List, Dict, Any, Optional, Set
from difflib import SequenceMatcher

from .interface import SearcherInterface
from .models import (
    InvertedIndex, IndexDocument, SearchMatch, MatchType, 
    SearchStatistics, SearchCache, HighlightConfig
)
from core.models import SegmentInfo, SearchResult, SearchQuery
from common.exceptions import SearchIndexError
from common.logging import get_logger


logger = get_logger(__name__)


class SearcherImpl(SearcherInterface):
    """搜索器实现类"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.index: Optional[InvertedIndex] = None
        self.documents: Dict[int, IndexDocument] = {}
        self.segments: Dict[int, SegmentInfo] = {}
        self.statistics = SearchStatistics()
        self.cache = SearchCache()
        self.highlight_config = HighlightConfig()
        
        self.logger.info("搜索器初始化完成")
    
    def set_index(self, index: InvertedIndex, documents: Dict[int, IndexDocument], 
                  segments: Dict[int, SegmentInfo]):
        """设置索引和文档"""
        self.index = index
        self.documents = documents
        self.segments = segments
        self.logger.info(f"搜索器索引设置完成，文档数量: {len(documents)}")
    
    def search_full_text(self, query: str, max_results: int = 100) -> List[SearchResult]:
        """全文搜索"""
        start_time = time.time()
        
        try:
            if not self.index:
                raise SearchIndexError("搜索索引未初始化")
            
            # 检查缓存
            cache_key = f"fulltext:{query}:{max_results}"
            cached_results = self.cache.get(cache_key)
            if cached_results:
                return cached_results
            
            # 分词
            terms = self._tokenize_query(query)
            if not terms:
                return []
            
            # 查找包含所有词项的文档
            doc_ids = self._find_documents_with_terms(terms)
            
            # 计算相关性分数并排序
            results = []
            for doc_id in doc_ids:
                if doc_id in self.segments:
                    segment = self.segments[doc_id]
                    relevance = self._calculate_relevance_score(segment, query, terms)
                    
                    # 查找匹配位置
                    matches = self._find_matches_in_text(segment.text, terms)
                    
                    # 生成高亮文本
                    highlight_text = self.highlight_matches(segment.text, query)
                    
                    result = SearchResult(
                        segment=segment,
                        matches=[{
                            'start': match.start_pos,
                            'end': match.end_pos,
                            'text': match.matched_text,
                            'type': match.match_type.value
                        } for match in matches],
                        relevance_score=relevance,
                        highlight_text=highlight_text
                    )
                    results.append(result)
            
            # 按相关性排序
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            results = results[:max_results]
            
            # 缓存结果
            self.cache.put(cache_key, results)
            
            # 记录统计
            response_time = time.time() - start_time
            self.statistics.record_query(query, True, response_time)
            
            self.logger.debug(f"全文搜索完成: '{query}', 结果数: {len(results)}, 耗时: {response_time:.3f}s")
            return results
            
        except Exception as e:
            response_time = time.time() - start_time
            self.statistics.record_query(query, False, response_time)
            self.logger.error(f"全文搜索失败: {e}")
            raise SearchIndexError(f"全文搜索失败: {e}")
    
    def search_regex(self, pattern: str, max_results: int = 100) -> List[SearchResult]:
        """正则表达式搜索"""
        start_time = time.time()
        
        try:
            if not self.segments:
                return []
            
            # 检查缓存
            cache_key = f"regex:{pattern}:{max_results}"
            cached_results = self.cache.get(cache_key)
            if cached_results:
                return cached_results
            
            # 编译正则表达式
            regex = re.compile(pattern, re.IGNORECASE)
            
            results = []
            for segment in self.segments.values():
                matches = list(regex.finditer(segment.text))
                if matches:
                    # 创建搜索匹配对象
                    search_matches = [
                        SearchMatch(
                            start_pos=match.start(),
                            end_pos=match.end(),
                            matched_text=match.group(),
                            match_type=MatchType.EXACT,
                            score=1.0
                        )
                        for match in matches
                    ]
                    
                    # 生成高亮文本
                    highlight_text = self._highlight_regex_matches(segment.text, matches)
                    
                    result = SearchResult(
                        segment=segment,
                        matches=[{
                            'start': match.start_pos,
                            'end': match.end_pos,
                            'text': match.matched_text,
                            'type': match.match_type.value
                        } for match in search_matches],
                        relevance_score=len(matches),  # 匹配数量作为相关性
                        highlight_text=highlight_text
                    )
                    results.append(result)
            
            # 按匹配数量排序
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            results = results[:max_results]
            
            # 缓存结果
            self.cache.put(cache_key, results)
            
            # 记录统计
            response_time = time.time() - start_time
            self.statistics.record_query(pattern, True, response_time)
            
            self.logger.debug(f"正则搜索完成: '{pattern}', 结果数: {len(results)}")
            return results
            
        except Exception as e:
            response_time = time.time() - start_time
            self.statistics.record_query(pattern, False, response_time)
            self.logger.error(f"正则搜索失败: {e}")
            raise SearchIndexError(f"正则搜索失败: {e}")
    
    def search_time_range(self, start_time: float, end_time: float) -> List[SearchResult]:
        """时间范围搜索"""
        try:
            if not self.segments:
                return []
            
            results = []
            for segment in self.segments.values():
                # 检查段落是否在时间范围内
                if (segment.start_time <= end_time and segment.end_time >= start_time):
                    result = SearchResult(
                        segment=segment,
                        matches=[],
                        relevance_score=1.0,
                        highlight_text=segment.text
                    )
                    results.append(result)
            
            # 按时间排序
            results.sort(key=lambda x: x.segment.start_time)
            
            self.logger.debug(f"时间范围搜索完成: {start_time}-{end_time}, 结果数: {len(results)}")
            return results
            
        except Exception as e:
            self.logger.error(f"时间范围搜索失败: {e}")
            raise SearchIndexError(f"时间范围搜索失败: {e}")
    
    def search_keyword(self, keywords: List[str], max_results: int = 100) -> List[SearchResult]:
        """关键词搜索"""
        # 将关键词列表转换为查询字符串
        query = " ".join(keywords)
        return self.search_full_text(query, max_results)
    
    def search_fuzzy(self, query: str, max_distance: int = 2, 
                    max_results: int = 100) -> List[SearchResult]:
        """模糊搜索"""
        start_time = time.time()
        
        try:
            if not self.segments:
                return []
            
            # 检查缓存
            cache_key = f"fuzzy:{query}:{max_distance}:{max_results}"
            cached_results = self.cache.get(cache_key)
            if cached_results:
                return cached_results
            
            query_terms = self._tokenize_query(query)
            results = []
            
            for segment in self.segments.values():
                segment_terms = self._tokenize_query(segment.text)
                fuzzy_matches = []
                
                # 对每个查询词项进行模糊匹配
                for query_term in query_terms:
                    for segment_term in segment_terms:
                        similarity = SequenceMatcher(None, query_term, segment_term).ratio()
                        
                        # 计算编辑距离
                        distance = self._levenshtein_distance(query_term, segment_term)
                        
                        if distance <= max_distance and similarity > 0.6:
                            # 在原文中查找匹配位置
                            match_pos = segment.text.lower().find(segment_term)
                            if match_pos >= 0:
                                fuzzy_matches.append(SearchMatch(
                                    start_pos=match_pos,
                                    end_pos=match_pos + len(segment_term),
                                    matched_text=segment_term,
                                    match_type=MatchType.FUZZY,
                                    score=similarity
                                ))
                
                if fuzzy_matches:
                    # 计算总相关性分数
                    relevance = sum(match.score for match in fuzzy_matches) / len(query_terms)
                    
                    result = SearchResult(
                        segment=segment,
                        matches=[{
                            'start': match.start_pos,
                            'end': match.end_pos,
                            'text': match.matched_text,
                            'type': match.match_type.value,
                            'score': match.score
                        } for match in fuzzy_matches],
                        relevance_score=relevance,
                        highlight_text=self._highlight_fuzzy_matches(segment.text, fuzzy_matches)
                    )
                    results.append(result)
            
            # 按相关性排序
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            results = results[:max_results]
            
            # 缓存结果
            self.cache.put(cache_key, results)
            
            # 记录统计
            response_time = time.time() - start_time
            self.statistics.record_query(query, True, response_time)
            
            self.logger.debug(f"模糊搜索完成: '{query}', 结果数: {len(results)}")
            return results
            
        except Exception as e:
            response_time = time.time() - start_time
            self.statistics.record_query(query, False, response_time)
            self.logger.error(f"模糊搜索失败: {e}")
            raise SearchIndexError(f"模糊搜索失败: {e}")
    
    def search_semantic(self, query: str, max_results: int = 100) -> List[SearchResult]:
        """语义搜索（简化实现）"""
        # 暂时使用全文搜索作为语义搜索的简化实现
        # 在实际项目中，这里应该使用词向量、BERT等技术
        self.logger.warning("语义搜索使用简化实现（全文搜索）")
        return self.search_full_text(query, max_results)
    
    def highlight_matches(self, text: str, query: str) -> str:
        """高亮匹配文本"""
        try:
            terms = self._tokenize_query(query)
            matches = self._find_matches_in_text(text, terms)
            return self.highlight_config.highlight_text(text, matches)
        except Exception as e:
            self.logger.warning(f"高亮文本失败: {e}")
            return text
    
    def calculate_relevance(self, segment: SegmentInfo, query: str) -> float:
        """计算相关性分数"""
        terms = self._tokenize_query(query)
        return self._calculate_relevance_score(segment, query, terms)
    
    def _tokenize_query(self, query: str) -> List[str]:
        """查询分词"""
        # 移除标点符号并转换为小写
        query = re.sub(r'[^\w\s]', ' ', query.lower())
        return [term.strip() for term in query.split() if term.strip()]
    
    def _find_documents_with_terms(self, terms: List[str]) -> Set[int]:
        """查找包含指定词项的文档"""
        if not terms or not self.index:
            return set()
        
        # 获取第一个词项的文档集合
        doc_sets = [self.index.search_term(term) for term in terms]
        doc_sets = [doc_set for doc_set in doc_sets if doc_set]
        
        if not doc_sets:
            return set()
        
        # 计算交集（AND操作）
        result = doc_sets[0]
        for doc_set in doc_sets[1:]:
            result = result.intersection(doc_set)
        
        return result
    
    def _find_matches_in_text(self, text: str, terms: List[str]) -> List[SearchMatch]:
        """在文本中查找匹配位置"""
        matches = []
        text_lower = text.lower()
        
        for term in terms:
            start = 0
            while True:
                pos = text_lower.find(term, start)
                if pos == -1:
                    break
                
                matches.append(SearchMatch(
                    start_pos=pos,
                    end_pos=pos + len(term),
                    matched_text=text[pos:pos + len(term)],
                    match_type=MatchType.EXACT,
                    score=1.0
                ))
                start = pos + 1
        
        return matches
    
    def _calculate_relevance_score(self, segment: SegmentInfo, query: str, terms: List[str]) -> float:
        """计算相关性分数"""
        if not terms:
            return 0.0
        
        text_lower = segment.text.lower()
        score = 0.0
        
        # TF-IDF简化计算
        for term in terms:
            # 词频 (TF)
            tf = text_lower.count(term) / len(text_lower.split())
            
            # 逆文档频率 (IDF) - 简化计算
            doc_count = len(self.segments)
            docs_with_term = len(self.index.search_term(term)) if self.index else 1
            idf = doc_count / max(docs_with_term, 1)
            
            score += tf * idf
        
        # 考虑段落置信度
        score *= segment.confidence
        
        return score
    
    def _highlight_regex_matches(self, text: str, matches: List[re.Match]) -> str:
        """高亮正则表达式匹配"""
        if not matches:
            return text
        
        result = ""
        last_end = 0
        
        for match in matches:
            result += text[last_end:match.start()]
            result += self.highlight_config.pre_tag + match.group() + self.highlight_config.post_tag
            last_end = match.end()
        
        result += text[last_end:]
        return result
    
    def _highlight_fuzzy_matches(self, text: str, matches: List[SearchMatch]) -> str:
        """高亮模糊匹配"""
        return self.highlight_config.highlight_text(text, matches)
    
    def _levenshtein_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self._levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
