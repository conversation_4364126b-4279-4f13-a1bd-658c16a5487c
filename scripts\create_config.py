#!/usr/bin/env python3
"""配置文件创建脚本

帮助用户创建和配置VideoReader的配置文件。
"""

import os
import sys
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config_manager


def create_default_config():
    """创建默认配置文件"""
    print("=== VideoReader 配置文件创建向导 ===")
    
    # 获取配置管理器
    config_manager = get_config_manager()
    config_file = config_manager.config_file
    
    print(f"配置文件路径: {config_file}")
    
    # 检查配置文件是否已存在
    if config_file.exists():
        response = input(f"配置文件已存在，是否覆盖？(y/N): ")
        if response.lower() != 'y':
            print("取消创建配置文件")
            return False
    
    # 确保目录存在
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 复制模板文件
    template_file = project_root / "config.toml"
    if template_file.exists():
        shutil.copy2(template_file, config_file)
        print(f"✓ 配置文件已创建: {config_file}")
    else:
        # 如果模板文件不存在，使用配置管理器创建默认配置
        config_manager.save()
        print(f"✓ 默认配置文件已创建: {config_file}")
    
    return True


def interactive_config():
    """交互式配置"""
    print("\n=== 交互式配置 ===")
    
    config_manager = get_config_manager()
    
    # 配置阿里云Paraformer
    print("\n1. 阿里云Paraformer语音识别配置")
    dashscope_key = input("请输入阿里云DashScope API密钥 (留空跳过): ").strip()
    if dashscope_key:
        config_manager.set('audio.speech_engines.paraformer.api_key', dashscope_key)
        print("✓ 阿里云Paraformer API密钥已设置")
    
    # 配置Cloudreve
    print("\n2. Cloudreve文件存储配置")
    cloudreve_url = input("请输入Cloudreve服务器地址 (如 http://your-server.com:5212, 留空跳过): ").strip()
    if cloudreve_url:
        config_manager.set('file_uploader.uploaders.cloudreve.base_url', cloudreve_url)
        
        # 选择认证方式
        print("请选择认证方式:")
        print("1. 用户名密码")
        print("2. 访问令牌")
        auth_choice = input("请选择 (1/2): ").strip()
        
        if auth_choice == '1':
            username = input("请输入用户名: ").strip()
            password = input("请输入密码: ").strip()
            if username and password:
                config_manager.set('file_uploader.uploaders.cloudreve.username', username)
                config_manager.set('file_uploader.uploaders.cloudreve.password', password)
                print("✓ Cloudreve用户名密码已设置")
        elif auth_choice == '2':
            token = input("请输入访问令牌: ").strip()
            if token:
                config_manager.set('file_uploader.uploaders.cloudreve.token', token)
                print("✓ Cloudreve访问令牌已设置")
    
    # 配置Azure语音服务（可选）
    print("\n3. Azure语音服务配置 (可选)")
    azure_key = input("请输入Azure语音服务API密钥 (留空跳过): ").strip()
    if azure_key:
        config_manager.set('audio.speech_engines.azure.api_key', azure_key)
        azure_region = input("请输入Azure区域 (默认: eastasia): ").strip() or "eastasia"
        config_manager.set('audio.speech_engines.azure.region', azure_region)
        print("✓ Azure语音服务配置已设置")
    
    # 配置阿里云OSS（可选）
    print("\n4. 阿里云OSS配置 (可选)")
    oss_key_id = input("请输入阿里云OSS Access Key ID (留空跳过): ").strip()
    if oss_key_id:
        oss_key_secret = input("请输入阿里云OSS Access Key Secret: ").strip()
        oss_endpoint = input("请输入阿里云OSS端点: ").strip()
        oss_bucket = input("请输入阿里云OSS存储桶名称: ").strip()
        
        if all([oss_key_secret, oss_endpoint, oss_bucket]):
            config_manager.set('file_uploader.uploaders.oss.access_key_id', oss_key_id)
            config_manager.set('file_uploader.uploaders.oss.access_key_secret', oss_key_secret)
            config_manager.set('file_uploader.uploaders.oss.endpoint', oss_endpoint)
            config_manager.set('file_uploader.uploaders.oss.bucket_name', oss_bucket)
            print("✓ 阿里云OSS配置已设置")
    
    # 保存配置
    try:
        config_manager.save()
        print(f"\n✓ 配置已保存到: {config_manager.config_file}")
        return True
    except Exception as e:
        print(f"\n✗ 保存配置失败: {e}")
        return False


def show_config_info():
    """显示配置信息"""
    print("\n=== 配置信息 ===")
    
    config_manager = get_config_manager()
    
    print(f"配置文件: {config_manager.config_file}")
    print(f"配置文件存在: {'是' if config_manager.config_file.exists() else '否'}")
    
    # 检查关键配置
    print("\n关键配置检查:")
    
    # Paraformer API密钥
    paraformer_key = config_manager.get('audio.speech_engines.paraformer.api_key', '')
    print(f"- Paraformer API密钥: {'已配置' if paraformer_key else '未配置'}")
    
    # Cloudreve配置
    cloudreve_url = config_manager.get('file_uploader.uploaders.cloudreve.base_url', '')
    cloudreve_username = config_manager.get('file_uploader.uploaders.cloudreve.username', '')
    cloudreve_token = config_manager.get('file_uploader.uploaders.cloudreve.token', '')
    print(f"- Cloudreve服务器: {'已配置' if cloudreve_url else '未配置'}")
    print(f"- Cloudreve认证: {'已配置' if (cloudreve_username or cloudreve_token) else '未配置'}")
    
    # Azure配置
    azure_key = config_manager.get('audio.speech_engines.azure.api_key', '')
    print(f"- Azure语音服务: {'已配置' if azure_key else '未配置'}")
    
    # OSS配置
    oss_key_id = config_manager.get('file_uploader.uploaders.oss.access_key_id', '')
    print(f"- 阿里云OSS: {'已配置' if oss_key_id else '未配置'}")


def main():
    """主函数"""
    print("VideoReader 配置文件管理工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 创建默认配置文件")
        print("2. 交互式配置")
        print("3. 显示配置信息")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            create_default_config()
        elif choice == '2':
            if interactive_config():
                print("\n配置完成！您现在可以使用VideoReader了。")
            else:
                print("\n配置失败，请检查输入并重试。")
        elif choice == '3':
            show_config_info()
        elif choice == '4':
            print("再见！")
            break
        else:
            print("无效选择，请重试。")


if __name__ == "__main__":
    main()
