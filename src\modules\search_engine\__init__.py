"""搜索引擎模块

负责构建搜索索引和执行搜索功能。
"""

from .interface import (
    SearchEngineInterface as SearchEngine,
    SearcherInterface as Searcher,
    IndexerInterface as Indexer,
    QueryParserInterface as QueryParser,
    FilterInterface as Filter,
    SuggestionInterface as Suggestion
)
from .searcher import SearcherImpl
from .indexer import SearchIndexer
from .models import SearchIndex, IndexEntry, SearchConfig
from core.models import SearchResult, SearchQuery

# 创建默认实例的工厂函数
def create_searcher() -> Searcher:
    """创建搜索器实例"""
    return SearcherImpl()

def create_indexer() -> SearchIndexer:
    """创建索引器实例"""
    return SearchIndexer()

__all__ = [
    'SearchEngine',
    'Searcher',
    'Indexer',
    'QueryParser',
    'Filter',
    'Suggestion',
    'SearcherImpl',
    'SearchIndexer',
    'SearchIndex',
    'IndexEntry',
    'SearchConfig',
    'SearchResult',
    'SearchQuery',
    'create_searcher',
    'create_indexer'
]
