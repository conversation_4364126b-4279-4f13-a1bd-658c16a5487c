# Paraformer语音识别器使用指南

## 概述

Paraformer是阿里云提供的新一代非自回归端到端语音识别模型，具有识别准确率高、功能丰富、领域覆盖广泛等优势。本项目集成了Paraformer语音识别功能，支持多种模型和配置选项。

## 功能特性

### 支持的模型

| 模型名称 | 描述 | 支持语言 | 采样率 | 特色功能 |
|---------|------|----------|--------|----------|
| paraformer-v2 | 最新多语种模型（推荐） | 中文、英文、日语、韩语、德语、法语、俄语 | 任意 | 标点符号、ITN、热词、语言提示 |
| paraformer-8k-v2 | 最新中文模型(8kHz) | 中文 | 8kHz | 标点符号、ITN、热词 |
| paraformer-v1 | 中英文模型 | 中文、英文 | 任意 | 标点符号、ITN、热词 |
| paraformer-8k-v1 | 中文模型(8kHz) | 中文 | 8kHz | 标点符号、ITN、热词 |
| paraformer-mtl-v1 | 多语言模型 | 中文、英文、日语、韩语等10种语言 | 16kHz+ | 标点符号、ITN、热词 |

### 核心功能

- **高精度识别**：基于新一代非自回归端到端模型
- **多语言支持**：支持中文、英文、日语、韩语等多种语言
- **丰富功能**：支持标点符号预测、时间戳、说话人分离等
- **灵活配置**：支持定制热词、语言提示等高级功能

## 环境配置

### 1. 安装依赖

```bash
# 安装阿里云DashScope SDK
pip install dashscope

# 安装OSS SDK（可选，用于文件上传）
pip install oss2

# 安装其他依赖
pip install requests
```

### 2. 配置API密钥

#### 方法一：环境变量（推荐）

```bash
# 设置DashScope API密钥
export DASHSCOPE_API_KEY="your_api_key_here"

# 可选：配置OSS上传（如果需要自动上传文件）
export OSS_ACCESS_KEY_ID="your_oss_access_key_id"
export OSS_ACCESS_KEY_SECRET="your_oss_access_key_secret"
export OSS_ENDPOINT="https://oss-cn-beijing.aliyuncs.com"
export OSS_BUCKET_NAME="your_bucket_name"
```

#### 方法二：代码中配置

```python
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer

# 直接传入API密钥
recognizer = ParaformerRecognizer(api_key="your_api_key_here")
```

### 3. 获取API密钥

1. 访问[阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 在API-KEY管理页面创建新的API密钥
3. 复制生成的API密钥用于配置

## 使用方法

### 基本使用

```python
from src.modules.video_processor.audio_engine.recognizer import SpeechRecognizer

# 创建语音识别器
speech_recognizer = SpeechRecognizer()

# 使用Paraformer进行识别
segments = speech_recognizer.recognize(
    audio_path="path/to/your/audio.wav",
    language="zh",
    engine="paraformer"
)

# 处理识别结果
for segment in segments:
    print(f"[{segment.time_range_str}] {segment.text}")
```

### 高级配置

```python
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer

# 创建Paraformer识别器并指定模型
recognizer = ParaformerRecognizer(
    api_key="your_api_key",
    model="paraformer-v2"
)

# 执行识别
segments = recognizer.recognize(
    audio_path="path/to/your/audio.wav",
    language="zh"
)
```

### 检查可用性

```python
from src.modules.video_processor.audio_engine.recognizer import SpeechRecognizer

speech_recognizer = SpeechRecognizer()

# 检查Paraformer是否可用
if speech_recognizer.is_engine_available("paraformer"):
    print("Paraformer识别器可用")
else:
    print("Paraformer识别器不可用，请检查配置")

# 获取所有可用的识别引擎
available_engines = speech_recognizer.get_available_engines()
print(f"可用的识别引擎: {available_engines}")
```

## 文件上传配置

由于Paraformer API要求音频文件必须通过公网URL访问，系统提供了自动文件上传功能。

### 配置阿里云OSS上传

```bash
# 设置OSS配置
export OSS_ACCESS_KEY_ID="your_access_key_id"
export OSS_ACCESS_KEY_SECRET="your_access_key_secret"
export OSS_ENDPOINT="https://oss-cn-beijing.aliyuncs.com"
export OSS_BUCKET_NAME="your_bucket_name"
```

### 使用本地文件服务器（测试用）

```python
from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager

# 创建文件上传管理器
uploader = FileUploadManager()

# 检查可用的上传器
available_uploaders = uploader.get_available_uploaders()
print(f"可用的上传器: {available_uploaders}")

# 手动上传文件
url = uploader.upload("path/to/audio.wav", uploader="local")
print(f"文件上传成功: {url}")
```

## 错误处理

### 常见错误及解决方案

1. **SDK未安装**
   ```
   错误：Paraformer SDK未安装或未配置API密钥
   解决：pip install dashscope 并配置DASHSCOPE_API_KEY
   ```

2. **文件上传失败**
   ```
   错误：音频文件上传失败
   解决：检查OSS配置或使用本地文件服务器
   ```

3. **API密钥无效**
   ```
   错误：API调用失败
   解决：检查API密钥是否正确，是否有足够的配额
   ```

4. **文件格式不支持**
   ```
   错误：文件格式错误
   解决：确保音频文件格式为支持的格式（wav、mp3、flac等）
   ```

### 错误处理示例

```python
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer
from src.common.exceptions import AudioProcessingError

recognizer = ParaformerRecognizer()

try:
    segments = recognizer.recognize("audio.wav", "zh")
    print(f"识别成功，共 {len(segments)} 个段落")
except AudioProcessingError as e:
    print(f"识别失败: {e}")
    # 根据错误信息进行相应处理
```

## 性能优化建议

### 1. 模型选择

- **通用场景**：使用 `paraformer-v2`（推荐）
- **电话客服**：使用 `paraformer-8k-v2`
- **多语言场景**：使用 `paraformer-mtl-v1`

### 2. 文件预处理

- 确保音频文件大小不超过2GB
- 音频时长不超过12小时
- 推荐使用16kHz采样率的单声道音频

### 3. 批量处理

```python
# 批量处理多个文件
audio_files = ["audio1.wav", "audio2.wav", "audio3.wav"]
results = []

for audio_file in audio_files:
    try:
        segments = recognizer.recognize(audio_file, "zh")
        results.append({
            "file": audio_file,
            "segments": segments,
            "status": "success"
        })
    except Exception as e:
        results.append({
            "file": audio_file,
            "error": str(e),
            "status": "failed"
        })
```

## 计费说明

- **录音文件识别**：0.288元/小时
- **免费额度**：10小时/月
- **计费单位**：按实际语音内容时长计费

## 技术支持

如遇到问题，请：

1. 检查[阿里云百炼文档](https://help.aliyun.com/zh/model-studio/paraformer-speech-recognition/)
2. 查看项目日志文件获取详细错误信息
3. 参考测试文件 `tests/test_paraformer_recognizer.py` 中的示例
4. 提交Issue到项目仓库

## 更新日志

- **v1.0.0**：初始版本，支持基本的Paraformer语音识别功能
- 支持多种模型选择
- 集成文件上传功能
- 提供完整的错误处理机制
