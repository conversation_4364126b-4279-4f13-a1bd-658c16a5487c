"""时间固定解析器

按固定时间间隔分割段落。
"""

from typing import List, Dict, Any

from .base import BaseParser
from ....core.models import VideoInfo, SegmentInfo, ParserType
from ..audio_engine.models import TranscriptSegment
from ....common.exceptions import ProcessingError


class TimeFixedParser(BaseParser):
    """时间固定解析器
    
    按照固定的时间间隔分割视频段落。
    """
    
    def __init__(self):
        super().__init__(ParserType.TIME_FIXED)
    
    def _do_parse(self, video_info: VideoInfo, transcript: List[TranscriptSegment],
                 config: Dict[str, Any]) -> List[SegmentInfo]:
        """执行时间固定解析"""
        try:
            # 获取配置参数
            duration = config.get('duration', 30.0)
            overlap = config.get('overlap', 0.0)
            align_to_speech = config.get('align_to_speech', True)
            min_speech_ratio = config.get('min_speech_ratio', 0.3)
            
            self.logger.info(f"时间固定解析参数: duration={duration}, overlap={overlap}")
            
            # 生成时间分割点
            split_points = self._generate_time_splits(
                video_info.duration, duration, overlap
            )
            
            # 如果需要对齐到语音边界，调整分割点
            if align_to_speech:
                split_points = self._align_to_speech_boundaries(
                    split_points, transcript, min_speech_ratio
                )
            
            # 创建段落
            segments = self._create_segments_from_time_splits(
                split_points, transcript, video_info
            )
            
            return segments
            
        except Exception as e:
            self.logger.error(f"时间固定解析失败: {e}")
            raise ProcessingError(f"时间固定解析失败: {e}")
    
    def _generate_time_splits(self, total_duration: float, 
                             segment_duration: float, overlap: float) -> List[float]:
        """生成时间分割点"""
        split_points = [0.0]
        current_time = 0.0
        
        while current_time + segment_duration < total_duration:
            current_time += segment_duration - overlap
            split_points.append(current_time)
        
        # 添加结束点
        if split_points[-1] < total_duration:
            split_points.append(total_duration)
        
        return split_points
    
    def _align_to_speech_boundaries(self, split_points: List[float],
                                   transcript: List[TranscriptSegment],
                                   min_speech_ratio: float) -> List[float]:
        """将分割点对齐到语音边界"""
        if not transcript:
            return split_points
        
        aligned_points = [split_points[0]]  # 保持起始点
        
        for i in range(1, len(split_points) - 1):
            target_time = split_points[i]
            
            # 寻找最近的语音边界
            best_boundary = target_time
            min_distance = float('inf')
            
            for segment in transcript:
                # 检查段落开始和结束时间
                for boundary_time in [segment.start_time, segment.end_time]:
                    distance = abs(boundary_time - target_time)
                    if distance < min_distance:
                        min_distance = distance
                        best_boundary = boundary_time
            
            # 只有在距离合理的情况下才调整
            if min_distance <= 5.0:  # 5秒内
                aligned_points.append(best_boundary)
            else:
                aligned_points.append(target_time)
        
        # 保持结束点
        aligned_points.append(split_points[-1])
        
        return aligned_points
    
    def _create_segments_from_time_splits(self, split_points: List[float],
                                        transcript: List[TranscriptSegment],
                                        video_info: VideoInfo) -> List[SegmentInfo]:
        """根据时间分割点创建段落"""
        segments = []
        
        for i in range(len(split_points) - 1):
            start_time = split_points[i]
            end_time = split_points[i + 1]
            
            # 获取时间范围内的转录文本
            segment_transcript = self._find_transcript_segments_in_range(
                transcript, start_time, end_time
            )
            
            # 合并文本
            text = self._merge_transcript_segments(transcript, start_time, end_time)
            
            # 生成摘要
            summary = self._generate_summary(text)
            
            # 计算置信度
            confidence = self._calculate_confidence(segment_transcript)
            
            # 计算语音比例
            speech_duration = sum(
                min(seg.end_time, end_time) - max(seg.start_time, start_time)
                for seg in segment_transcript
                if seg.end_time > start_time and seg.start_time < end_time
            )
            speech_ratio = speech_duration / (end_time - start_time) if end_time > start_time else 0
            
            # 创建段落信息
            segment = SegmentInfo(
                id=i,
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                text=text,
                summary=summary,
                confidence=confidence,
                key_frame_path="",
                thumbnail_path="",
                metadata={
                    'parser_type': self.parser_type.value,
                    'speech_ratio': speech_ratio,
                    'transcript_segments': len(segment_transcript)
                }
            )
            
            segments.append(segment)
        
        return segments
