#!/usr/bin/env python3
"""Cloudreve配置示例

演示如何配置和使用Cloudreve上传器。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.file_uploader import CloudreveUploader, FileUploadManager
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer


def example_1_environment_variables():
    """示例1：使用环境变量配置"""
    print("=== 示例1：使用环境变量配置 ===")
    
    # 设置环境变量（在实际使用中，这些应该在系统环境中设置）
    # os.environ['CLOUDREVE_BASE_URL'] = 'http://your-cloudreve-server.com'
    # os.environ['CLOUDREVE_USERNAME'] = 'your-username'
    # os.environ['CLOUDREVE_PASSWORD'] = 'your-password'
    
    # 创建上传器（会自动从环境变量读取配置）
    uploader = CloudreveUploader()
    
    if uploader.is_available():
        print("✓ Cloudreve上传器配置成功")
        # file_url = uploader.upload('test.mp4')
        # print(f"文件上传成功: {file_url}")
    else:
        print("✗ Cloudreve上传器配置失败，请检查环境变量")


def example_2_direct_parameters():
    """示例2：直接传递参数"""
    print("\n=== 示例2：直接传递参数 ===")
    
    # 直接传递配置参数
    uploader = CloudreveUploader(
        base_url='http://your-cloudreve-server.com',
        username='your-username',
        password='your-password'
    )
    
    if uploader.is_available():
        print("✓ Cloudreve上传器配置成功")
        # file_url = uploader.upload('test.mp4', 'videos/my-video.mp4')
        # print(f"文件上传成功: {file_url}")
    else:
        print("✗ Cloudreve上传器配置失败，请检查参数")


def example_3_token_authentication():
    """示例3：使用访问令牌认证"""
    print("\n=== 示例3：使用访问令牌认证 ===")
    
    # 使用访问令牌（推荐用于生产环境）
    uploader = CloudreveUploader(
        base_url='http://your-cloudreve-server.com',
        token='your-access-token'
    )
    
    if uploader.is_available():
        print("✓ Cloudreve上传器配置成功")
        # file_url = uploader.upload('test.mp4')
        # print(f"文件上传成功: {file_url}")
    else:
        print("✗ Cloudreve上传器配置失败，请检查令牌")


def example_4_file_upload_manager():
    """示例4：使用文件上传管理器"""
    print("\n=== 示例4：使用文件上传管理器 ===")
    
    # 创建文件上传管理器
    manager = FileUploadManager()
    
    # 检查可用的上传器
    available_uploaders = manager.get_available_uploaders()
    print(f"可用的上传器: {available_uploaders}")
    
    if 'cloudreve' in available_uploaders:
        print("✓ Cloudreve上传器可用")
        
        # 使用自动选择模式（会优先选择Cloudreve）
        # file_url = manager.upload('test.mp4', uploader='auto')
        # print(f"自动选择上传成功: {file_url}")
        
        # 或者明确指定使用Cloudreve
        # file_url = manager.upload('test.mp4', uploader='cloudreve')
        # print(f"Cloudreve上传成功: {file_url}")
    else:
        print("✗ Cloudreve上传器不可用")


def example_5_paraformer_integration():
    """示例5：ParaformerRecognizer集成"""
    print("\n=== 示例5：ParaformerRecognizer集成 ===")
    
    # 创建ParaformerRecognizer（会自动使用Cloudreve上传器）
    recognizer = ParaformerRecognizer(model='paraformer-v2')
    
    if recognizer.is_available():
        print("✓ ParaformerRecognizer可用")
        
        # 检查是否可以使用Cloudreve上传器
        if recognizer.file_uploader.is_uploader_available('cloudreve'):
            print("✓ 可以使用Cloudreve上传器")
            
            # 直接识别本地文件（会自动上传到Cloudreve）
            # segments = recognizer.recognize('test.mp4', language='zh')
            # print(f"识别完成，共 {len(segments)} 个段落")
        else:
            print("✗ 无法使用Cloudreve上传器")
    else:
        print("✗ ParaformerRecognizer不可用")


def show_configuration_steps():
    """显示详细配置步骤"""
    print("\n=== 详细配置步骤 ===")
    print()
    print("1. 部署Cloudreve服务器")
    print("   - 下载Cloudreve 4.1.2版本")
    print("   - 配置并启动服务器")
    print("   - 确保服务器可以通过网络访问")
    print()
    print("2. 创建用户账户")
    print("   - 在Cloudreve管理界面创建用户")
    print("   - 或者使用现有的管理员账户")
    print()
    print("3. 配置环境变量")
    print("   Linux/macOS:")
    print("   export CLOUDREVE_BASE_URL='http://your-server:5212'")
    print("   export CLOUDREVE_USERNAME='your-username'")
    print("   export CLOUDREVE_PASSWORD='your-password'")
    print()
    print("   Windows:")
    print("   set CLOUDREVE_BASE_URL=http://your-server:5212")
    print("   set CLOUDREVE_USERNAME=your-username")
    print("   set CLOUDREVE_PASSWORD=your-password")
    print()
    print("4. 安装依赖")
    print("   pip install requests")
    print()
    print("5. 测试连接")
    print("   python test_cloudreve_uploader.py")


def main():
    """主函数"""
    print("Cloudreve配置示例")
    print("=" * 50)
    
    # 显示配置步骤
    show_configuration_steps()
    
    # 运行示例
    example_1_environment_variables()
    example_2_direct_parameters()
    example_3_token_authentication()
    example_4_file_upload_manager()
    example_5_paraformer_integration()
    
    print("\n=== 注意事项 ===")
    print("1. 请将示例中的URL、用户名、密码替换为实际值")
    print("2. 生产环境建议使用访问令牌而不是用户名密码")
    print("3. 确保Cloudreve服务器配置了正确的CORS策略")
    print("4. 大文件上传可能需要较长时间，请耐心等待")
    print("5. 如果上传失败，请检查网络连接和服务器状态")


if __name__ == "__main__":
    main()
