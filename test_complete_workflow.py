#!/usr/bin/env python3
"""完整工作流程测试

测试从本地文件到语音识别的完整流程。
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer
from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager
from src.common.config import get_config
from src.common.logging import get_logger

logger = get_logger(__name__)


def check_configuration():
    """检查配置"""
    print("=== 检查配置 ===")
    
    # 检查Paraformer配置
    paraformer_key = get_config('audio.speech_engines.paraformer.api_key', '')
    paraformer_model = get_config('audio.speech_engines.paraformer.model', 'paraformer-v2')
    
    print(f"Paraformer API密钥: {'已配置' if paraformer_key else '未配置'}")
    print(f"Paraformer模型: {paraformer_model}")
    
    # 检查文件上传器配置
    manager = FileUploadManager()
    available_uploaders = manager.get_available_uploaders()
    print(f"可用的上传器: {available_uploaders}")
    
    return bool(paraformer_key), available_uploaders


def create_test_video():
    """创建测试视频文件（模拟）"""
    print("\n=== 创建测试视频文件 ===")
    
    # 创建一个小的模拟MP4文件
    # 这不是真正的视频文件，只是用于测试上传功能
    test_content = b"ftypisom" + b"\x00" * 1000  # 模拟MP4文件头
    
    test_file = Path("test_video_for_upload.mp4")
    with open(test_file, 'wb') as f:
        f.write(test_content)
    
    print(f"✅ 测试视频文件已创建: {test_file}")
    print(f"   文件大小: {len(test_content)} bytes")
    print("   注意：这是模拟文件，不包含真实的音频内容")
    
    return test_file


def test_file_upload_workflow():
    """测试文件上传工作流程"""
    print("\n=== 测试文件上传工作流程 ===")
    
    # 创建测试文件
    test_file = create_test_video()
    
    try:
        # 创建文件上传管理器
        manager = FileUploadManager()
        
        print("\n开始上传文件...")
        start_time = time.time()
        
        # 上传文件
        file_url = manager.upload(str(test_file), uploader='auto')
        
        upload_time = time.time() - start_time
        
        print(f"✅ 文件上传成功!")
        print(f"   上传时间: {upload_time:.2f}秒")
        print(f"   文件URL: {file_url}")
        
        # 验证文件可访问性
        print("\n验证文件可访问性...")
        try:
            import requests
            response = requests.get(file_url, timeout=10)
            if response.status_code == 200:
                print("✅ 文件可以正常访问")
                print(f"   响应大小: {len(response.content)} bytes")
            else:
                print(f"⚠️ 文件访问异常: HTTP {response.status_code}")
        except Exception as e:
            print(f"⚠️ 文件访问测试失败: {e}")
        
        return file_url
        
    except Exception as e:
        print(f"❌ 文件上传失败: {e}")
        return None
    finally:
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
            print(f"✅ 测试文件已清理: {test_file}")


def test_paraformer_integration():
    """测试ParaformerRecognizer集成"""
    print("\n=== 测试ParaformerRecognizer集成 ===")
    
    try:
        # 创建ParaformerRecognizer
        recognizer = ParaformerRecognizer()
        
        print("✅ ParaformerRecognizer已创建")
        print(f"   模型: {recognizer.model}")
        print(f"   API密钥: {'已配置' if recognizer.api_key else '未配置'}")
        
        # 检查是否可用
        if recognizer.is_available():
            print("✅ ParaformerRecognizer可用")
        else:
            print("❌ ParaformerRecognizer不可用（可能缺少API密钥）")
            return False
        
        # 检查文件上传器
        available_uploaders = recognizer.file_uploader.get_available_uploaders()
        print(f"   可用上传器: {available_uploaders}")
        
        if not available_uploaders:
            print("❌ 没有可用的文件上传器")
            return False
        
        print("✅ ParaformerRecognizer集成测试通过")
        print("   注意：由于没有真实音频文件，跳过实际识别测试")
        
        return True
        
    except Exception as e:
        print(f"❌ ParaformerRecognizer集成测试失败: {e}")
        return False


def show_usage_example():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    
    example_code = '''
# 使用ParaformerRecognizer进行语音识别的完整示例

from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer

# 1. 创建识别器（自动从配置文件读取设置）
recognizer = ParaformerRecognizer()

# 2. 直接识别本地视频文件
# 系统会自动：
# - 提取音频（如果是视频文件）
# - 上传到可用的文件服务器（Cloudreve或备用服务器）
# - 调用阿里云Paraformer API进行识别
# - 返回识别结果

segments = recognizer.recognize('your_video.mp4', language='zh')

# 3. 处理识别结果
for segment in segments:
    print(f"[{segment.time_range_str}] {segment.text}")
'''
    
    print(example_code)


def show_system_status():
    """显示系统状态总结"""
    print("\n=== 系统状态总结 ===")
    
    # 检查配置
    has_paraformer_key, available_uploaders = check_configuration()
    
    print("\n📊 功能状态:")
    print(f"✅ TOML配置系统: 正常")
    print(f"{'✅' if has_paraformer_key else '❌'} Paraformer API: {'已配置' if has_paraformer_key else '未配置'}")
    print(f"{'✅' if 'cloudreve' in available_uploaders else '⚠️'} Cloudreve上传器: {'可用' if 'cloudreve' in available_uploaders else '不可用（使用备用方案）'}")
    print(f"{'✅' if 'http' in available_uploaders else '❌'} 本地HTTP服务器: {'可用' if 'http' in available_uploaders else '不可用'}")
    print(f"✅ 自动故障转移: 正常")
    
    print("\n🎯 当前状态:")
    if has_paraformer_key and available_uploaders:
        print("🟢 系统完全可用 - 可以进行语音识别")
    elif has_paraformer_key:
        print("🟡 部分可用 - 有API密钥但缺少文件上传器")
    elif available_uploaders:
        print("🟡 部分可用 - 有文件上传器但缺少API密钥")
    else:
        print("🔴 系统不可用 - 缺少必要配置")
    
    print("\n📋 下一步操作:")
    if not has_paraformer_key:
        print("1. 配置Paraformer API密钥")
        print("   - 编辑 ~/.videoreader/config.toml")
        print("   - 设置 audio.speech_engines.paraformer.api_key")
    
    if 'cloudreve' not in available_uploaders:
        print("2. 修复Cloudreve连接（可选）")
        print("   - 联系管理员检查API配置")
        print("   - 或继续使用备用上传方案")
    
    if has_paraformer_key and available_uploaders:
        print("✨ 系统已就绪，可以开始使用语音识别功能！")


def main():
    """主函数"""
    print("VideoReader完整工作流程测试")
    print("=" * 60)
    
    # 检查配置
    has_paraformer_key, available_uploaders = check_configuration()
    
    # 测试文件上传工作流程
    upload_result = test_file_upload_workflow()
    
    # 测试ParaformerRecognizer集成
    integration_result = test_paraformer_integration()
    
    # 显示使用示例
    show_usage_example()
    
    # 显示系统状态
    show_system_status()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    if upload_result and integration_result:
        print("\n✅ 所有测试通过！")
        print("您的VideoReader系统已经配置完成，可以正常使用。")
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")


if __name__ == "__main__":
    main()
