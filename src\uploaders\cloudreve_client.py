#!/usr/bin/env python3
"""
Cloudreve V4 API客户端
基于官方API文档实现: https://cloudrevev4.apifox.cn
"""

import json
import time
import logging
import requests
from typing import Optional, Dict, Any, Tuple
from pathlib import Path


class CloudreveError(Exception):
    """Cloudreve API异常"""
    def __init__(self, message: str, code: int = None, response: Dict = None):
        super().__init__(message)
        self.code = code
        self.response = response


class CloudreveClient:
    """Cloudreve V4 API客户端"""
    
    def __init__(self, base_url: str, email: str, password: str, timeout: int = 60):
        """
        初始化Cloudreve客户端
        
        Args:
            base_url: Cloudreve服务器地址，如 "http://your-server.com:5212"
            email: 用户邮箱地址
            password: 用户密码
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.email = email
        self.password = password
        self.timeout = timeout
        
        # 认证信息
        self.access_token = None
        self.refresh_token = None
        self.token_expires = None
        
        # HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
        })
        
        # 日志
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数
            
        Returns:
            API响应的JSON数据
            
        Raises:
            CloudreveError: API请求失败
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                raise CloudreveError(
                    f"HTTP {response.status_code}: {response.text}",
                    code=response.status_code
                )
            
            # 解析JSON响应
            try:
                data = response.json()
            except json.JSONDecodeError:
                raise CloudreveError(f"无效的JSON响应: {response.text}")
            
            # 检查API响应码
            if data.get('code', 0) != 0:
                raise CloudreveError(
                    data.get('msg', '未知错误'),
                    code=data.get('code'),
                    response=data
                )
            
            return data
            
        except requests.RequestException as e:
            raise CloudreveError(f"请求失败: {e}")
    
    def ping(self) -> str:
        """
        获取服务器版本信息
        
        Returns:
            服务器版本号
        """
        response = self._make_request('GET', '/api/v4/site/ping')
        return response['data']
    
    def get_captcha(self) -> Tuple[str, str]:
        """
        获取验证码
        
        Returns:
            (验证码图片base64, 验证码ticket)
        """
        response = self._make_request('GET', '/api/v4/site/captcha')
        data = response['data']
        return data['image'], data['ticket']
    
    def login(self, captcha: str = None, ticket: str = None) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            captcha: 验证码（如果需要）
            ticket: 验证码ticket（如果需要）
            
        Returns:
            登录响应数据，包含用户信息和token
            
        Raises:
            CloudreveError: 登录失败
        """
        login_data = {
            'email': self.email,
            'password': self.password
        }
        
        # 添加验证码（如果提供）
        if captcha and ticket:
            login_data['captcha'] = captcha
            login_data['ticket'] = ticket
        
        try:
            response = self._make_request('POST', '/api/v4/session/token', json=login_data)
            
            # 保存认证信息
            token_data = response['data']['token']
            self.access_token = token_data['access_token']
            self.refresh_token = token_data['refresh_token']
            
            # 解析token过期时间
            access_expires = token_data.get('access_expires')
            if access_expires:
                # 假设时间格式为ISO格式，需要解析
                # 这里简化处理，实际应该解析时间字符串
                self.token_expires = time.time() + 3600  # 默认1小时后过期
            
            # 设置认证头
            self.session.headers['Authorization'] = f'Bearer {self.access_token}'
            
            self.logger.info(f"登录成功: {response['data']['user']['email']}")
            return response['data']
            
        except CloudreveError as e:
            if e.code == 40001:
                raise CloudreveError("邮箱格式错误，请检查email地址", e.code, e.response)
            elif e.code == 40002:
                raise CloudreveError("密码错误", e.code, e.response)
            elif e.code == 40003:
                raise CloudreveError("需要验证码", e.code, e.response)
            else:
                raise
    
    def refresh_access_token(self) -> bool:
        """
        刷新访问令牌
        
        Returns:
            是否刷新成功
        """
        if not self.refresh_token:
            return False
        
        try:
            response = self._make_request(
                'POST', 
                '/api/v4/session/refresh',
                json={'refresh_token': self.refresh_token}
            )
            
            # 更新token
            token_data = response['data']
            self.access_token = token_data['access_token']
            self.session.headers['Authorization'] = f'Bearer {self.access_token}'
            
            self.logger.info("Token刷新成功")
            return True
            
        except CloudreveError:
            self.logger.warning("Token刷新失败")
            return False
    
    def ensure_authenticated(self) -> bool:
        """
        确保已认证，如果token过期则尝试刷新或重新登录
        
        Returns:
            是否认证成功
        """
        # 检查是否有token
        if not self.access_token:
            try:
                self.login()
                return True
            except CloudreveError:
                return False
        
        # 检查token是否过期
        if self.token_expires and time.time() > self.token_expires:
            # 尝试刷新token
            if not self.refresh_access_token():
                # 刷新失败，重新登录
                try:
                    self.login()
                    return True
                except CloudreveError:
                    return False
        
        return True
    
    def logout(self) -> bool:
        """
        用户登出
        
        Returns:
            是否登出成功
        """
        if not self.access_token:
            return True
        
        try:
            self._make_request('DELETE', '/api/v4/session/token')
            
            # 清除认证信息
            self.access_token = None
            self.refresh_token = None
            self.token_expires = None
            
            # 移除认证头
            if 'Authorization' in self.session.headers:
                del self.session.headers['Authorization']
            
            self.logger.info("登出成功")
            return True
            
        except CloudreveError:
            self.logger.warning("登出失败")
            return False

    def create_upload_session(self, file_path: str, policy_id: str = None) -> Dict[str, Any]:
        """
        创建文件上传会话

        Args:
            file_path: 本地文件路径
            policy_id: 存储策略ID（可选）

        Returns:
            上传会话信息

        Raises:
            CloudreveError: 创建上传会话失败
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        file_path = Path(file_path)
        if not file_path.exists():
            raise CloudreveError(f"文件不存在: {file_path}")

        # 获取文件信息
        file_size = file_path.stat().st_size
        file_name = file_path.name

        # 构建上传URI（简化版本，实际可能需要更复杂的路径处理）
        upload_uri = f"cloudreve://my/{file_name}"

        upload_data = {
            'uri': upload_uri,
            'size': file_size,
            'last_modified': int(file_path.stat().st_mtime * 1000),  # 毫秒时间戳
        }

        # 添加存储策略ID（如果提供）
        if policy_id:
            upload_data['policy_id'] = policy_id

        # 尝试检测MIME类型
        mime_type = self._get_mime_type(file_path)
        if mime_type:
            upload_data['mime_type'] = mime_type

        response = self._make_request('PUT', '/api/v4/file/upload', json=upload_data)
        return response['data']

    def upload_file_chunk(self, session_id: str, chunk_data: bytes, chunk_index: int) -> bool:
        """
        上传文件块

        Args:
            session_id: 上传会话ID
            chunk_data: 文件块数据
            chunk_index: 文件块索引

        Returns:
            是否上传成功
        """
        if not self.ensure_authenticated():
            raise CloudreveError("认证失败")

        # 构建multipart/form-data请求
        files = {
            'file': (f'chunk_{chunk_index}', chunk_data, 'application/octet-stream')
        }

        data = {
            'session_id': session_id,
            'chunk': str(chunk_index)
        }

        try:
            # 临时移除Content-Type头，让requests自动设置multipart
            original_content_type = self.session.headers.get('Content-Type')
            if 'Content-Type' in self.session.headers:
                del self.session.headers['Content-Type']

            response = self.session.post(
                f"{self.base_url}/api/v4/file/upload/chunk",
                files=files,
                data=data,
                timeout=self.timeout
            )

            # 恢复Content-Type头
            if original_content_type:
                self.session.headers['Content-Type'] = original_content_type

            if response.status_code != 200:
                raise CloudreveError(f"上传文件块失败: HTTP {response.status_code}")

            return True

        except requests.RequestException as e:
            raise CloudreveError(f"上传文件块失败: {e}")

    def upload_file(self, file_path: str, policy_id: str = None,
                   chunk_size: int = 5242880, progress_callback=None) -> str:
        """
        上传文件

        Args:
            file_path: 本地文件路径
            policy_id: 存储策略ID（可选）
            chunk_size: 文件块大小（默认5MB）
            progress_callback: 进度回调函数 callback(uploaded_bytes, total_bytes)

        Returns:
            上传后的文件URL或ID

        Raises:
            CloudreveError: 上传失败
        """
        file_path = Path(file_path)

        # 创建上传会话
        session_info = self.create_upload_session(str(file_path), policy_id)
        session_id = session_info['session_id']

        self.logger.info(f"开始上传文件: {file_path.name}")

        # 分块上传文件
        uploaded_bytes = 0
        total_bytes = file_path.stat().st_size
        chunk_index = 0

        with open(file_path, 'rb') as f:
            while True:
                chunk_data = f.read(chunk_size)
                if not chunk_data:
                    break

                # 上传文件块
                self.upload_file_chunk(session_id, chunk_data, chunk_index)

                uploaded_bytes += len(chunk_data)
                chunk_index += 1

                # 调用进度回调
                if progress_callback:
                    progress_callback(uploaded_bytes, total_bytes)

                self.logger.debug(f"已上传 {uploaded_bytes}/{total_bytes} 字节")

        self.logger.info(f"文件上传完成: {file_path.name}")

        # 返回会话信息中的URI或其他标识
        return session_info.get('uri', session_id)

    def _get_mime_type(self, file_path: Path) -> Optional[str]:
        """
        获取文件MIME类型

        Args:
            file_path: 文件路径

        Returns:
            MIME类型字符串
        """
        import mimetypes
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type
