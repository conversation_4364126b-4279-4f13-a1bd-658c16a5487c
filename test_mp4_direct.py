#!/usr/bin/env python3
"""直接使用test.mp4测试ParaformerRecognizer

阿里云Paraformer支持直接处理MP4格式文件，无需提取音频。
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer, SpeechRecognizer
from src.common.exceptions import SpeechRecognitionError
from src.common.logging import get_logger


def check_environment():
    """检查环境配置"""
    print("=== 环境检查 ===")
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if api_key:
        print(f"✓ DASHSCOPE_API_KEY: {api_key[:10]}...")
    else:
        print("✗ DASHSCOPE_API_KEY 未配置")
        return False
    
    # 检查SDK
    try:
        import dashscope
        print("✓ DashScope SDK 已安装")
    except ImportError:
        print("✗ DashScope SDK 未安装")
        return False
    
    # 检查test.mp4文件
    test_file = Path("test.mp4")
    if test_file.exists():
        print(f"✓ test.mp4文件存在: ({test_file.stat().st_size / 1024 / 1024:.1f} MB)")
    else:
        print("✗ test.mp4文件不存在")
        return False
    
    return True


def upload_mp4_to_temp_url():
    """将MP4文件上传到临时可访问的URL"""
    print("=== 上传test.mp4文件 ===")
    
    try:
        from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager
        
        # 创建文件上传管理器
        uploader = FileUploadManager()
        
        # 检查可用的上传器
        available_uploaders = uploader.get_available_uploaders()
        print(f"可用的上传器: {available_uploaders}")
        
        if not available_uploaders:
            print("✗ 没有可用的文件上传器")
            print("请配置OSS或使用其他方式上传文件")
            return None
        
        # 上传文件
        print("正在上传test.mp4...")
        file_url = uploader.upload("test.mp4", uploader='auto', object_name=f"test_videos/test_{int(time.time())}.mp4")
        
        print(f"✓ 文件上传成功: {file_url}")
        return file_url
        
    except Exception as e:
        print(f"✗ 文件上传失败: {e}")
        return None


def test_paraformer_with_mp4_url(mp4_url: str):
    """使用MP4 URL直接测试Paraformer"""
    print(f"=== 使用MP4文件测试Paraformer ===")
    print(f"文件URL: {mp4_url}")
    
    try:
        # 创建Paraformer识别器
        recognizer = ParaformerRecognizer(model='paraformer-v2')
        
        if not recognizer.is_available():
            print("✗ ParaformerRecognizer不可用")
            return None
        
        print("✓ ParaformerRecognizer可用")
        print(f"使用模型: {recognizer.model}")
        
        # 直接调用API进行识别
        print("开始识别MP4文件...")
        
        from dashscope.audio.asr import Transcription
        import dashscope
        
        # 设置API密钥
        dashscope.api_key = recognizer.api_key
        
        # 提交识别任务 - 直接使用MP4文件
        print("提交识别任务...")
        task_response = Transcription.async_call(
            model=recognizer.model,
            file_urls=[mp4_url],
            language_hints=['zh', 'en']
        )
        
        if task_response.status_code != 200:
            print(f"✗ 提交任务失败: {task_response.message}")
            return None
        
        print(f"✓ 任务提交成功")
        print(f"  任务ID: {task_response.output.task_id}")
        
        # 等待任务完成
        print("等待识别完成...")
        transcribe_response = Transcription.wait(task=task_response.output.task_id)
        
        if transcribe_response.status_code != 200:
            print(f"✗ 获取结果失败: {transcribe_response.message}")
            return None
        
        print("✓ 识别完成")
        
        # 解析结果
        segments = recognizer._parse_transcription_results(transcribe_response.output, 'zh')
        
        print(f"✓ 解析完成，共 {len(segments)} 个段落")
        return segments
        
    except Exception as e:
        print(f"✗ 识别失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return None


def test_with_speech_recognizer_manager():
    """通过SpeechRecognizer管理器测试"""
    print("=== 通过SpeechRecognizer管理器测试 ===")
    
    try:
        # 首先上传文件
        import time
        from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager
        
        uploader = FileUploadManager()
        if not uploader.get_available_uploaders():
            print("✗ 没有可用的文件上传器，跳过此测试")
            return None
        
        print("上传test.mp4文件...")
        file_url = uploader.upload("test.mp4", uploader='auto', object_name=f"test_videos/manager_test_{int(time.time())}.mp4")
        print(f"✓ 文件上传成功: {file_url}")
        
        # 使用SpeechRecognizer管理器
        speech_recognizer = SpeechRecognizer()
        
        # 检查可用引擎
        available_engines = speech_recognizer.get_available_engines()
        print(f"可用的识别引擎: {available_engines}")
        
        if 'paraformer' not in available_engines:
            print("✗ Paraformer引擎不可用")
            return None
        
        print("✓ Paraformer引擎可用")
        
        # 执行识别
        print("开始识别...")
        segments = speech_recognizer.recognize(
            audio_path=file_url,  # 直接使用MP4 URL
            language='zh',
            engine='paraformer'
        )
        
        print(f"✓ 识别完成，共 {len(segments)} 个段落")
        return segments
        
    except Exception as e:
        print(f"✗ 识别失败: {e}")
        return None


def display_results(segments, title="test.mp4识别结果"):
    """显示识别结果"""
    if not segments:
        print("没有识别结果")
        return
    
    print(f"\n=== {title} ===")
    print(f"总段落数: {len(segments)}")
    print()
    
    for i, segment in enumerate(segments, 1):
        print(f"段落 {i}:")
        print(f"  时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s ({segment.duration:.2f}s)")
        print(f"  文本: {segment.text}")
        print(f"  置信度: {segment.confidence:.2f}")
        if segment.speaker_id:
            print(f"  说话人: {segment.speaker_id}")
        print()
    
    # 完整文本
    full_text = ' '.join(seg.text for seg in segments)
    print("=== 完整文本 ===")
    print(full_text)
    print()
    
    # 统计信息
    if segments:
        total_duration = max(seg.end_time for seg in segments) if segments else 0
        avg_confidence = sum(seg.confidence for seg in segments) / len(segments)
        
        print("=== 统计信息 ===")
        print(f"总时长: {total_duration:.2f} 秒")
        print(f"总字数: {len(full_text)} 字符")
        print(f"平均置信度: {avg_confidence:.2f}")
        if total_duration > 0:
            print(f"识别速度: {len(full_text) / total_duration:.1f} 字符/秒")


def save_results(segments, output_file: str):
    """保存结果到文件"""
    if not segments:
        return
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("test.mp4 Paraformer语音识别结果\n")
            f.write("=" * 50 + "\n\n")
            
            for i, segment in enumerate(segments, 1):
                f.write(f"段落 {i}:\n")
                f.write(f"时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s\n")
                f.write(f"文本: {segment.text}\n")
                f.write(f"置信度: {segment.confidence:.2f}\n")
                if segment.speaker_id:
                    f.write(f"说话人: {segment.speaker_id}\n")
                f.write("\n")
            
            # 完整文本
            f.write("\n" + "=" * 50 + "\n")
            f.write("完整文本:\n")
            f.write("=" * 50 + "\n")
            full_text = ' '.join(seg.text for seg in segments)
            f.write(full_text)
        
        print(f"✓ 结果已保存到 {output_file}")
        
    except Exception as e:
        print(f"✗ 保存失败: {e}")


def main():
    """主函数"""
    logger = get_logger(__name__)
    logger.info("开始直接测试test.mp4文件")
    
    print("直接使用test.mp4测试Paraformer语音识别器")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n环境检查失败，请解决上述问题后重试")
        return
    
    print()
    
    # 方法1：上传文件并直接测试
    print("=" * 60)
    import time
    mp4_url = upload_mp4_to_temp_url()
    
    if mp4_url:
        segments1 = test_paraformer_with_mp4_url(mp4_url)
        
        if segments1:
            display_results(segments1, "直接调用ParaformerRecognizer结果")
            save_results(segments1, "test_mp4_direct_results.txt")
    
    # 方法2：通过SpeechRecognizer管理器测试
    print("\n" + "=" * 60)
    segments2 = test_with_speech_recognizer_manager()
    
    if segments2:
        display_results(segments2, "SpeechRecognizer管理器结果")
        save_results(segments2, "test_mp4_manager_results.txt")
    
    # 总结
    print("\n" + "=" * 60)
    print("=== 测试总结 ===")
    
    if segments1:
        print("✓ 直接调用ParaformerRecognizer成功识别test.mp4")
    else:
        print("✗ 直接调用ParaformerRecognizer失败")
    
    if segments2:
        print("✓ 通过SpeechRecognizer管理器成功识别test.mp4")
    else:
        print("✗ 通过SpeechRecognizer管理器识别失败")
    
    if segments1 or segments2:
        print("\n🎉 test.mp4 Paraformer识别测试成功！")
        print("✓ 证明了Paraformer可以直接处理MP4格式文件")
        print("✓ 无需提取音频，直接上传MP4文件即可识别")
    else:
        print("\n❌ test.mp4识别测试失败")
        print("可能的原因:")
        print("1. 文件上传配置问题")
        print("2. 网络连接问题")
        print("3. API配额问题")
    
    print("\n测试完成！")


if __name__ == '__main__':
    main()
