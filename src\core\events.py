"""事件系统

提供应用内的事件发布和订阅机制。
"""

import threading
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

try:
    from ..common.logging import get_logger
except ImportError:
    from common.logging import get_logger


logger = get_logger(__name__)


class EventType(Enum):
    """事件类型枚举"""
    
    # 应用级事件
    APP_STARTED = "app_started"
    APP_SHUTDOWN = "app_shutdown"
    
    # 视频处理事件
    VIDEO_LOADED = "video_loaded"
    VIDEO_PROCESSING_STARTED = "video_processing_started"
    VIDEO_PROCESSING_PROGRESS = "video_processing_progress"
    VIDEO_PROCESSING_COMPLETED = "video_processing_completed"
    VIDEO_PROCESSING_FAILED = "video_processing_failed"
    VIDEO_PROCESSING_CANCELLED = "video_processing_cancelled"
    
    # 音频处理事件
    AUDIO_EXTRACTION_STARTED = "audio_extraction_started"
    AUDIO_EXTRACTION_COMPLETED = "audio_extraction_completed"
    SPEECH_RECOGNITION_STARTED = "speech_recognition_started"
    SPEECH_RECOGNITION_COMPLETED = "speech_recognition_completed"
    
    # 解析事件
    PARSING_STARTED = "parsing_started"
    PARSING_PROGRESS = "parsing_progress"
    PARSING_COMPLETED = "parsing_completed"
    PARSING_FAILED = "parsing_failed"
    
    # UI事件
    SEGMENT_SELECTED = "segment_selected"
    PLAY_REQUESTED = "play_requested"
    PAUSE_REQUESTED = "pause_requested"
    SEEK_REQUESTED = "seek_requested"
    
    # 搜索事件
    SEARCH_STARTED = "search_started"
    SEARCH_COMPLETED = "search_completed"
    
    # 存储事件
    METADATA_SAVED = "metadata_saved"
    METADATA_LOADED = "metadata_loaded"
    EXPORT_STARTED = "export_started"
    EXPORT_COMPLETED = "export_completed"
    
    # 错误事件
    ERROR_OCCURRED = "error_occurred"
    WARNING_OCCURRED = "warning_occurred"


@dataclass
class Event:
    """事件数据类"""
    event_type: EventType
    data: Dict[str, Any]
    timestamp: datetime
    source: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._subscribers: Dict[EventType, List[Callable]] = {}
        self._lock = threading.RLock()
        self._event_history: List[Event] = []
        self._max_history = 1000
    
    def subscribe(self, event_type: EventType, callback: Callable[[Event], None]) -> None:
        """订阅事件"""
        with self._lock:
            if event_type not in self._subscribers:
                self._subscribers[event_type] = []
            
            if callback not in self._subscribers[event_type]:
                self._subscribers[event_type].append(callback)
                logger.debug(f"订阅事件: {event_type.value}")
    
    def unsubscribe(self, event_type: EventType, callback: Callable[[Event], None]) -> None:
        """取消订阅事件"""
        with self._lock:
            if event_type in self._subscribers:
                try:
                    self._subscribers[event_type].remove(callback)
                    logger.debug(f"取消订阅事件: {event_type.value}")
                except ValueError:
                    pass
    
    def publish(self, event_type: EventType, data: Dict[str, Any] = None, 
                source: str = None) -> None:
        """发布事件"""
        if data is None:
            data = {}
        
        event = Event(
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source
        )
        
        # 添加到历史记录
        with self._lock:
            self._event_history.append(event)
            if len(self._event_history) > self._max_history:
                self._event_history.pop(0)
        
        # 通知订阅者
        self._notify_subscribers(event)
    
    def _notify_subscribers(self, event: Event) -> None:
        """通知订阅者"""
        subscribers = []
        with self._lock:
            if event.event_type in self._subscribers:
                subscribers = self._subscribers[event.event_type].copy()
        
        for callback in subscribers:
            try:
                callback(event)
            except Exception as e:
                logger.error(f"事件回调执行失败: {e}")
                # 发布错误事件
                self.publish(EventType.ERROR_OCCURRED, {
                    'error': str(e),
                    'callback': str(callback),
                    'original_event': event.event_type.value
                })
    
    def get_event_history(self, event_type: Optional[EventType] = None, 
                         limit: Optional[int] = None) -> List[Event]:
        """获取事件历史"""
        with self._lock:
            events = self._event_history.copy()
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if limit:
            events = events[-limit:]
        
        return events
    
    def clear_history(self) -> None:
        """清空事件历史"""
        with self._lock:
            self._event_history.clear()
    
    def get_subscriber_count(self, event_type: EventType) -> int:
        """获取事件订阅者数量"""
        with self._lock:
            return len(self._subscribers.get(event_type, []))
    
    def get_all_event_types(self) -> List[EventType]:
        """获取所有已订阅的事件类型"""
        with self._lock:
            return list(self._subscribers.keys())


class EventHandler:
    """事件处理器基类"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self._subscribed_events: List[EventType] = []
    
    def subscribe_to_events(self) -> None:
        """订阅感兴趣的事件"""
        # 子类应该重写此方法
        pass
    
    def unsubscribe_from_events(self) -> None:
        """取消订阅所有事件"""
        for event_type in self._subscribed_events:
            self.event_bus.unsubscribe(event_type, self._handle_event)
        self._subscribed_events.clear()
    
    def _subscribe(self, event_type: EventType) -> None:
        """内部订阅方法"""
        self.event_bus.subscribe(event_type, self._handle_event)
        self._subscribed_events.append(event_type)
    
    def _handle_event(self, event: Event) -> None:
        """处理事件的统一入口"""
        method_name = f"on_{event.event_type.value}"
        handler = getattr(self, method_name, None)
        
        if handler and callable(handler):
            try:
                handler(event)
            except Exception as e:
                logger.error(f"事件处理器 {method_name} 执行失败: {e}")
        else:
            logger.warning(f"未找到事件处理器: {method_name}")


# 全局事件总线实例
_global_event_bus = None
_bus_lock = threading.Lock()


def get_event_bus() -> EventBus:
    """获取全局事件总线实例"""
    global _global_event_bus
    if _global_event_bus is None:
        with _bus_lock:
            if _global_event_bus is None:
                _global_event_bus = EventBus()
    return _global_event_bus


def publish_event(event_type: EventType, data: Dict[str, Any] = None, 
                 source: str = None) -> None:
    """发布事件的便捷函数"""
    get_event_bus().publish(event_type, data, source)


def subscribe_event(event_type: EventType, callback: Callable[[Event], None]) -> None:
    """订阅事件的便捷函数"""
    get_event_bus().subscribe(event_type, callback)


def unsubscribe_event(event_type: EventType, callback: Callable[[Event], None]) -> None:
    """取消订阅事件的便捷函数"""
    get_event_bus().unsubscribe(event_type, callback)
