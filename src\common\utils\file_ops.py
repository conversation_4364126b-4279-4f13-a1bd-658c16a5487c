"""文件操作工具

提供常用的文件操作功能。
"""

import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from typing import List, Optional, Union, Generator, Tuple
import tempfile

from ..exceptions import FileOperationError
from ..logging import get_logger


logger = get_logger(__name__)


def ensure_dir(path: Union[str, Path]) -> Path:
    """确保目录存在"""
    path = Path(path)
    try:
        path.mkdir(parents=True, exist_ok=True)
        return path
    except Exception as e:
        raise FileOperationError(f"无法创建目录 {path}: {e}")


def safe_remove(path: Union[str, Path]) -> bool:
    """安全删除文件或目录"""
    path = Path(path)
    try:
        if path.is_file():
            path.unlink()
        elif path.is_dir():
            shutil.rmtree(path)
        return True
    except Exception as e:
        logger.warning(f"删除失败 {path}: {e}")
        return False


def copy_file(src: Union[str, Path], dst: Union[str, Path], 
              overwrite: bool = False) -> bool:
    """复制文件"""
    src, dst = Path(src), Path(dst)
    
    if not src.exists():
        raise FileOperationError(f"源文件不存在: {src}")
    
    if dst.exists() and not overwrite:
        raise FileOperationError(f"目标文件已存在: {dst}")
    
    try:
        # 确保目标目录存在
        ensure_dir(dst.parent)
        shutil.copy2(src, dst)
        return True
    except Exception as e:
        raise FileOperationError(f"复制文件失败: {e}")


def move_file(src: Union[str, Path], dst: Union[str, Path], 
              overwrite: bool = False) -> bool:
    """移动文件"""
    src, dst = Path(src), Path(dst)
    
    if not src.exists():
        raise FileOperationError(f"源文件不存在: {src}")
    
    if dst.exists() and not overwrite:
        raise FileOperationError(f"目标文件已存在: {dst}")
    
    try:
        # 确保目标目录存在
        ensure_dir(dst.parent)
        shutil.move(str(src), str(dst))
        return True
    except Exception as e:
        raise FileOperationError(f"移动文件失败: {e}")


def get_file_size(path: Union[str, Path]) -> int:
    """获取文件大小（字节）"""
    path = Path(path)
    if not path.exists():
        raise FileOperationError(f"文件不存在: {path}")
    return path.stat().st_size


def get_file_hash(path: Union[str, Path], algorithm: str = 'md5') -> str:
    """计算文件哈希值"""
    path = Path(path)
    if not path.exists():
        raise FileOperationError(f"文件不存在: {path}")
    
    try:
        hash_obj = hashlib.new(algorithm)
        with open(path, 'rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except Exception as e:
        raise FileOperationError(f"计算文件哈希失败: {e}")


def get_mime_type(path: Union[str, Path]) -> Optional[str]:
    """获取文件MIME类型"""
    path = Path(path)
    mime_type, _ = mimetypes.guess_type(str(path))
    return mime_type


def is_video_file(path: Union[str, Path]) -> bool:
    """判断是否为视频文件"""
    mime_type = get_mime_type(path)
    return mime_type is not None and mime_type.startswith('video/')


def is_audio_file(path: Union[str, Path]) -> bool:
    """判断是否为音频文件"""
    mime_type = get_mime_type(path)
    return mime_type is not None and mime_type.startswith('audio/')


def find_files(directory: Union[str, Path], pattern: str = "*", 
               recursive: bool = True) -> List[Path]:
    """查找文件"""
    directory = Path(directory)
    if not directory.exists():
        return []
    
    if recursive:
        return list(directory.rglob(pattern))
    else:
        return list(directory.glob(pattern))


def get_available_filename(path: Union[str, Path]) -> Path:
    """获取可用的文件名（如果文件存在则添加数字后缀）"""
    path = Path(path)
    if not path.exists():
        return path
    
    stem = path.stem
    suffix = path.suffix
    parent = path.parent
    counter = 1
    
    while True:
        new_name = f"{stem}_{counter}{suffix}"
        new_path = parent / new_name
        if not new_path.exists():
            return new_path
        counter += 1


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def create_temp_file(suffix: str = None, prefix: str = None, 
                    dir: Union[str, Path] = None) -> Path:
    """创建临时文件"""
    try:
        fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
        os.close(fd)  # 关闭文件描述符
        return Path(path)
    except Exception as e:
        raise FileOperationError(f"创建临时文件失败: {e}")


def create_temp_dir(suffix: str = None, prefix: str = None, 
                   dir: Union[str, Path] = None) -> Path:
    """创建临时目录"""
    try:
        path = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
        return Path(path)
    except Exception as e:
        raise FileOperationError(f"创建临时目录失败: {e}")


def read_chunks(file_path: Union[str, Path], chunk_size: int = 8192) -> Generator[bytes, None, None]:
    """分块读取文件"""
    path = Path(file_path)
    if not path.exists():
        raise FileOperationError(f"文件不存在: {path}")
    
    try:
        with open(path, 'rb') as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                yield chunk
    except Exception as e:
        raise FileOperationError(f"读取文件失败: {e}")


def backup_file(path: Union[str, Path], backup_dir: Union[str, Path] = None) -> Path:
    """备份文件"""
    path = Path(path)
    if not path.exists():
        raise FileOperationError(f"文件不存在: {path}")
    
    if backup_dir is None:
        backup_dir = path.parent / "backup"
    else:
        backup_dir = Path(backup_dir)
    
    ensure_dir(backup_dir)
    
    # 生成备份文件名（添加时间戳）
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{path.stem}_{timestamp}{path.suffix}"
    backup_path = backup_dir / backup_name
    
    copy_file(path, backup_path)
    return backup_path
