"""文件上传工具

支持将本地文件上传到云存储服务，获取公网可访问的URL。
"""

import os
import tempfile
import shutil
import time
import json
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod

from ....common.exceptions import FileOperationError
from ....common.logging import get_logger
from ....common.config import get_config


logger = get_logger(__name__)


class BaseFileUploader(ABC):
    """文件上传器基类"""
    
    @abstractmethod
    def upload(self, file_path: str, object_name: Optional[str] = None) -> str:
        """上传文件并返回公网可访问的URL
        
        Args:
            file_path: 本地文件路径
            object_name: 对象名称，如果不提供则使用文件名
            
        Returns:
            str: 公网可访问的URL
            
        Raises:
            FileOperationError: 上传失败
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查上传器是否可用"""
        pass


class AliOSSUploader(BaseFileUploader):
    """阿里云OSS文件上传器"""
    
    def __init__(self, access_key_id: Optional[str] = None,
                 access_key_secret: Optional[str] = None,
                 endpoint: Optional[str] = None,
                 bucket_name: Optional[str] = None):
        """初始化OSS上传器

        Args:
            access_key_id: 访问密钥ID，如果不提供则从配置文件或环境变量获取
            access_key_secret: 访问密钥，如果不提供则从配置文件或环境变量获取
            endpoint: OSS端点，如果不提供则从配置文件或环境变量获取
            bucket_name: 存储桶名称，如果不提供则从配置文件或环境变量获取
        """
        self.access_key_id = (
            access_key_id or
            get_config('file_uploader.uploaders.oss.access_key_id', '') or
            os.getenv('OSS_ACCESS_KEY_ID')
        )
        self.access_key_secret = (
            access_key_secret or
            get_config('file_uploader.uploaders.oss.access_key_secret', '') or
            os.getenv('OSS_ACCESS_KEY_SECRET')
        )
        self.endpoint = (
            endpoint or
            get_config('file_uploader.uploaders.oss.endpoint', '') or
            os.getenv('OSS_ENDPOINT')
        )
        self.bucket_name = (
            bucket_name or
            get_config('file_uploader.uploaders.oss.bucket_name', '') or
            os.getenv('OSS_BUCKET_NAME')
        )
        self.timeout = get_config('file_uploader.uploaders.oss.timeout', 60)
        self.logger = get_logger(__name__)
    
    def is_available(self) -> bool:
        """检查OSS上传器是否可用"""
        try:
            import oss2
            return all([
                self.access_key_id,
                self.access_key_secret,
                self.endpoint,
                self.bucket_name
            ])
        except ImportError:
            return False
    
    def upload(self, file_path: str, object_name: Optional[str] = None) -> str:
        """上传文件到阿里云OSS
        
        Args:
            file_path: 本地文件路径
            object_name: 对象名称，如果不提供则使用文件名
            
        Returns:
            str: 公网可访问的URL
            
        Raises:
            FileOperationError: 上传失败
        """
        try:
            if not self.is_available():
                raise FileOperationError("OSS上传器不可用，请安装oss2并配置相关环境变量")
            
            import oss2
            
            # 创建OSS客户端
            auth = oss2.Auth(self.access_key_id, self.access_key_secret)
            bucket = oss2.Bucket(auth, self.endpoint, self.bucket_name)
            
            # 确定对象名称
            if object_name is None:
                object_name = f"audio/{Path(file_path).name}"
            
            self.logger.info(f"上传文件到OSS: {file_path} -> {object_name}")
            
            # 上传文件
            result = bucket.put_object_from_file(object_name, file_path)
            
            if result.status == 200:
                # 构造公网访问URL
                url = f"https://{self.bucket_name}.{self.endpoint.replace('https://', '').replace('http://', '')}/{object_name}"
                self.logger.info(f"文件上传成功: {url}")
                return url
            else:
                raise FileOperationError(f"文件上传失败，状态码: {result.status}")

        except Exception as e:
            self.logger.error(f"OSS文件上传失败: {e}")
            raise FileOperationError(f"OSS文件上传失败: {e}")


class LocalFileServer(BaseFileUploader):
    """本地文件服务器（用于测试）
    
    注意：这只是一个简单的实现，用于开发和测试环境。
    生产环境中应该使用真正的云存储服务。
    """
    
    def __init__(self, base_url: str = "http://localhost:8000", 
                 upload_dir: str = "uploads"):
        """初始化本地文件服务器
        
        Args:
            base_url: 服务器基础URL
            upload_dir: 上传目录
        """
        self.base_url = base_url.rstrip('/')
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        self.logger = get_logger(__name__)
    
    def is_available(self) -> bool:
        """检查本地文件服务器是否可用"""
        # 简单检查上传目录是否存在且可写
        return self.upload_dir.exists() and os.access(self.upload_dir, os.W_OK)
    
    def upload(self, file_path: str, object_name: Optional[str] = None) -> str:
        """复制文件到本地服务器目录
        
        Args:
            file_path: 本地文件路径
            object_name: 对象名称，如果不提供则使用文件名
            
        Returns:
            str: 本地服务器URL
            
        Raises:
            FileOperationError: 复制失败
        """
        try:
            if not self.is_available():
                raise FileOperationError("本地文件服务器不可用")
            
            # 确定目标文件名
            if object_name is None:
                object_name = Path(file_path).name
            
            target_path = self.upload_dir / object_name
            
            self.logger.info(f"复制文件到本地服务器: {file_path} -> {target_path}")
            
            # 复制文件
            shutil.copy2(file_path, target_path)
            
            # 构造访问URL
            url = f"{self.base_url}/{object_name}"
            self.logger.info(f"文件复制成功: {url}")
            return url
            
        except Exception as e:
            self.logger.error(f"本地文件服务器复制失败: {e}")
            raise FileOperationError(f"本地文件服务器复制失败: {e}")


class LocalHttpServerUploader(BaseFileUploader):
    """本地HTTP服务器上传器

    启动一个本地HTTP服务器来提供文件访问，适用于需要URL访问的场景。
    """

    def __init__(self, port: int = 8000):
        """初始化本地HTTP服务器上传器"""
        self.logger = get_logger(__name__)
        self.port = port
        self.server = None
        self.server_thread = None
        self.is_running = False
        self.temp_dir = Path("temp_files")
        self.temp_dir.mkdir(exist_ok=True)

    def _find_available_port(self, start_port: int = 8000) -> int:
        """查找可用端口"""
        import socket
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        raise RuntimeError("无法找到可用端口")

    def _start_server(self) -> bool:
        """启动HTTP服务器"""
        if self.is_running:
            return True

        try:
            import threading
            from http.server import HTTPServer, SimpleHTTPRequestHandler
            import os

            # 查找可用端口
            self.port = self._find_available_port(self.port)

            # 切换到临时目录
            original_dir = os.getcwd()
            os.chdir(self.temp_dir)

            # 创建HTTP服务器
            self.server = HTTPServer(('localhost', self.port), SimpleHTTPRequestHandler)

            # 恢复原目录
            os.chdir(original_dir)

            # 在单独线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()

            self.is_running = True
            self.logger.info(f"本地HTTP服务器已启动: http://localhost:{self.port}")

            import time
            time.sleep(0.5)  # 等待服务器启动

            return True

        except Exception as e:
            self.logger.error(f"启动HTTP服务器失败: {e}")
            return False

    def is_available(self) -> bool:
        """检查本地HTTP服务器上传器是否可用"""
        try:
            return self._start_server()
        except Exception:
            return False

    def upload(self, file_path: str, object_name: Optional[str] = None) -> str:
        """通过本地HTTP服务器提供文件访问

        Args:
            file_path: 本地文件路径
            object_name: 对象名称（可选）

        Returns:
            str: 本地HTTP服务器URL

        Raises:
            FileOperationError: 上传失败
        """
        try:
            if not self.is_available():
                raise FileOperationError("本地HTTP服务器不可用")

            # 检查文件是否存在
            source_file = Path(file_path)
            if not source_file.exists():
                raise FileOperationError(f"文件不存在: {file_path}")

            # 确定目标文件名
            if object_name:
                target_name = object_name
            else:
                import time
                timestamp = int(time.time())
                target_name = f"{timestamp}_{source_file.name}"

            # 复制文件到临时目录
            target_file = self.temp_dir / target_name

            import shutil
            shutil.copy2(source_file, target_file)

            # 构造URL
            url = f"http://localhost:{self.port}/{target_name}"

            self.logger.info(f"文件可通过以下URL访问: {url}")
            return url

        except Exception as e:
            self.logger.error(f"本地HTTP服务器上传失败: {e}")
            raise FileOperationError(f"本地HTTP服务器上传失败: {e}")


class CloudreveUploader(BaseFileUploader):
    """Cloudreve文件上传器

    支持Cloudreve 4.1.2版本的文件上传功能。
    """

    def __init__(self, base_url: Optional[str] = None,
                 username: Optional[str] = None,
                 password: Optional[str] = None,
                 token: Optional[str] = None):
        """初始化Cloudreve上传器

        Args:
            base_url: Cloudreve服务器地址，如果不提供则从配置文件或环境变量获取
            username: 用户名，如果不提供则从配置文件或环境变量获取
            password: 密码，如果不提供则从配置文件或环境变量获取
            token: 访问令牌，如果提供则优先使用，否则使用用户名密码登录
        """
        # 优先使用传入的参数，然后是配置文件，最后是环境变量
        self.base_url = (
            base_url or
            get_config('file_uploader.uploaders.cloudreve.base_url', '') or
            os.getenv('CLOUDREVE_BASE_URL', '')
        ).rstrip('/')

        self.username = (
            username or
            get_config('file_uploader.uploaders.cloudreve.username', '') or
            os.getenv('CLOUDREVE_USERNAME')
        )

        self.password = (
            password or
            get_config('file_uploader.uploaders.cloudreve.password', '') or
            os.getenv('CLOUDREVE_PASSWORD')
        )

        self.token = (
            token or
            get_config('file_uploader.uploaders.cloudreve.token', '') or
            os.getenv('CLOUDREVE_TOKEN')
        )

        # 其他配置项
        self.timeout = get_config('file_uploader.uploaders.cloudreve.timeout', 60)
        self.chunk_size = get_config('file_uploader.uploaders.cloudreve.chunk_size', 5242880)  # 5MB
        self.max_retries = get_config('file_uploader.uploaders.cloudreve.max_retries', 3)

        self.logger = get_logger(__name__)
        self.session = None
        self._init_session()

    def _init_session(self):
        """初始化HTTP会话"""
        try:
            import requests
            self.session = requests.Session()
            self.session.headers.update({
                'User-Agent': 'VideoReader-Cloudreve-Client/1.0',
                'Content-Type': 'application/json'
            })
        except ImportError:
            self.logger.error("需要安装requests库: pip install requests")
            self.session = None

    def is_available(self) -> bool:
        """检查Cloudreve上传器是否可用"""
        try:
            import requests
            if not self.base_url:
                return False

            # 检查是否有认证信息
            if not self.token and not (self.username and self.password):
                return False

            # 测试连接
            return self._test_connection()
        except ImportError:
            return False

    def _test_connection(self) -> bool:
        """测试与Cloudreve服务器的连接"""
        try:
            if not self.session:
                return False

            response = self.session.get(f"{self.base_url}/site/ping", timeout=10)
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"连接测试失败: {e}")
            return False

    def _authenticate(self) -> bool:
        """进行身份认证"""
        if self.token:
            # 使用提供的token
            self.session.headers.update({'Authorization': f'Bearer {self.token}'})
            return True

        if not (self.username and self.password):
            self.logger.error("缺少认证信息：需要提供token或用户名密码")
            return False

        try:
            # 使用用户名密码登录
            login_data = {
                'username': self.username,
                'password': self.password
            }

            response = self.session.post(
                f"{self.base_url}/user/session",
                json=login_data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    # 登录成功，从响应中获取token
                    token = result.get('data', {}).get('token')
                    if token:
                        self.token = token
                        self.session.headers.update({'Authorization': f'Bearer {token}'})
                        self.logger.info("Cloudreve认证成功")
                        return True

            self.logger.error(f"Cloudreve认证失败: {response.text}")
            return False

        except Exception as e:
            self.logger.error(f"Cloudreve认证异常: {e}")
            return False

    def upload(self, file_path: str, object_name: Optional[str] = None) -> str:
        """上传文件到Cloudreve

        Args:
            file_path: 本地文件路径
            object_name: 对象名称，如果不提供则使用文件名

        Returns:
            str: 公网可访问的URL

        Raises:
            FileOperationError: 上传失败
        """
        try:
            if not self.is_available():
                raise FileOperationError("Cloudreve上传器不可用，请检查配置和网络连接")

            # 进行身份认证
            if not self._authenticate():
                raise FileOperationError("Cloudreve身份认证失败")

            # 检查文件是否存在
            source_file = Path(file_path)
            if not source_file.exists():
                raise FileOperationError(f"文件不存在: {file_path}")

            # 确定目标文件名
            if object_name is None:
                object_name = source_file.name

            self.logger.info(f"开始上传文件到Cloudreve: {file_path} -> {object_name}")

            # 创建上传会话
            upload_session = self._create_upload_session(source_file, object_name)

            # 上传文件
            file_url = self._upload_file_chunks(source_file, upload_session)

            self.logger.info(f"文件上传成功: {file_url}")
            return file_url

        except Exception as e:
            self.logger.error(f"Cloudreve文件上传失败: {e}")
            raise FileOperationError(f"Cloudreve文件上传失败: {e}")

    def _create_upload_session(self, file_path: Path, object_name: str) -> Dict[str, Any]:
        """创建上传会话

        Args:
            file_path: 文件路径
            object_name: 对象名称

        Returns:
            Dict: 上传会话信息
        """
        try:
            # 计算文件大小和MD5
            file_size = file_path.stat().st_size
            file_md5 = self._calculate_md5(file_path)

            # 准备上传会话请求
            session_data = {
                'path': f'/{object_name}',
                'size': file_size,
                'name': object_name,
                'policy_id': 0,  # 使用默认存储策略
                'last_modified': int(file_path.stat().st_mtime),
                'mime_type': self._get_mime_type(file_path),
                'md5': file_md5
            }

            response = self.session.post(
                f"{self.base_url}/file/upload",
                json=session_data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {})

            raise FileOperationError(f"创建上传会话失败: {response.text}")

        except Exception as e:
            self.logger.error(f"创建上传会话失败: {e}")
            raise FileOperationError(f"创建上传会话失败: {e}")

    def _upload_file_chunks(self, file_path: Path, upload_session: Dict[str, Any]) -> str:
        """上传文件块

        Args:
            file_path: 文件路径
            upload_session: 上传会话信息

        Returns:
            str: 文件访问URL
        """
        try:
            session_id = upload_session.get('sessionID')
            if not session_id:
                raise FileOperationError("上传会话ID为空")

            chunk_size = upload_session.get('chunkSize', self.chunk_size)
            file_size = file_path.stat().st_size

            self.logger.info(f"开始分块上传，文件大小: {file_size}, 块大小: {chunk_size}")

            with open(file_path, 'rb') as f:
                chunk_index = 0
                uploaded_size = 0

                while uploaded_size < file_size:
                    # 读取文件块
                    chunk_data = f.read(chunk_size)
                    if not chunk_data:
                        break

                    # 上传文件块
                    self._upload_chunk(session_id, chunk_index, chunk_data)

                    uploaded_size += len(chunk_data)
                    chunk_index += 1

                    # 显示进度
                    progress = (uploaded_size / file_size) * 100
                    self.logger.info(f"上传进度: {progress:.1f}% ({uploaded_size}/{file_size})")

            # 完成上传
            return self._finish_upload(session_id, upload_session)

        except Exception as e:
            self.logger.error(f"分块上传失败: {e}")
            raise FileOperationError(f"分块上传失败: {e}")

    def _upload_chunk(self, session_id: str, chunk_index: int, chunk_data: bytes):
        """上传单个文件块

        Args:
            session_id: 上传会话ID
            chunk_index: 块索引
            chunk_data: 块数据
        """
        try:
            files = {
                'file': (f'chunk_{chunk_index}', chunk_data, 'application/octet-stream')
            }

            data = {
                'sessionID': session_id,
                'index': chunk_index
            }

            # 临时移除Content-Type头，让requests自动设置multipart/form-data
            original_content_type = self.session.headers.pop('Content-Type', None)

            response = self.session.post(
                f"{self.base_url}/file/upload/chunk",
                files=files,
                data=data,
                timeout=self.timeout
            )

            # 恢复Content-Type头
            if original_content_type:
                self.session.headers['Content-Type'] = original_content_type

            if response.status_code != 200:
                raise FileOperationError(f"上传块 {chunk_index} 失败: {response.text}")

            result = response.json()
            if result.get('code') != 0:
                raise FileOperationError(f"上传块 {chunk_index} 失败: {result.get('msg', 'Unknown error')}")

        except Exception as e:
            self.logger.error(f"上传块 {chunk_index} 失败: {e}")
            raise FileOperationError(f"上传块 {chunk_index} 失败: {e}")

    def _finish_upload(self, session_id: str, upload_session: Dict[str, Any]) -> str:
        """完成上传并获取文件URL

        Args:
            session_id: 上传会话ID
            upload_session: 上传会话信息

        Returns:
            str: 文件访问URL
        """
        try:
            # 发送完成上传请求
            finish_data = {
                'sessionID': session_id
            }

            response = self.session.post(
                f"{self.base_url}/file/upload/finish",
                json=finish_data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    # 构造文件访问URL
                    file_data = result.get('data', {})
                    file_id = file_data.get('id')
                    if file_id:
                        # 创建直接下载链接
                        return self._create_direct_link(file_id)
                    else:
                        # 使用默认的文件访问方式
                        file_name = upload_session.get('name', 'file')
                        return f"{self.base_url}/file/get/{file_id}/{file_name}"

            raise FileOperationError(f"完成上传失败: {response.text}")

        except Exception as e:
            self.logger.error(f"完成上传失败: {e}")
            raise FileOperationError(f"完成上传失败: {e}")

    def _create_direct_link(self, file_id: str) -> str:
        """创建直接下载链接

        Args:
            file_id: 文件ID

        Returns:
            str: 直接下载链接
        """
        try:
            link_data = {
                'id': [file_id],
                'expires': 3600 * 24 * 7  # 7天有效期
            }

            response = self.session.post(
                f"{self.base_url}/file/direct-link",
                json=link_data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    links = result.get('data', [])
                    if links and len(links) > 0:
                        return links[0].get('url', '')

            # 如果创建直接链接失败，返回默认的文件访问URL
            self.logger.warning("创建直接链接失败，使用默认访问方式")
            return f"{self.base_url}/file/get/{file_id}"

        except Exception as e:
            self.logger.warning(f"创建直接链接失败: {e}，使用默认访问方式")
            return f"{self.base_url}/file/get/{file_id}"

    def _calculate_md5(self, file_path: Path) -> str:
        """计算文件MD5值

        Args:
            file_path: 文件路径

        Returns:
            str: MD5值
        """
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _get_mime_type(self, file_path: Path) -> str:
        """获取文件MIME类型

        Args:
            file_path: 文件路径

        Returns:
            str: MIME类型
        """
        import mimetypes
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type or 'application/octet-stream'


class FileUploadManager:
    """文件上传管理器"""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.uploaders = {
            'oss': AliOSSUploader(),
            'local': LocalFileServer(),
            'http': LocalHttpServerUploader(),
            'cloudreve': CloudreveUploader(),
        }
    
    def upload(self, file_path: str, uploader: str = 'auto',
               object_name: Optional[str] = None) -> str:
        """上传文件

        Args:
            file_path: 本地文件路径
            uploader: 上传器类型，'auto'表示自动选择可用的上传器
            object_name: 对象名称

        Returns:
            str: 公网可访问的URL

        Raises:
            FileOperationError: 上传失败
        """
        try:
            if uploader == 'auto':
                # 自动选择可用的上传器，优先选择Cloudreve
                priority_order = ['cloudreve', 'oss', 'http', 'local']
                for name in priority_order:
                    if name in self.uploaders:
                        uploader_instance = self.uploaders[name]
                        if uploader_instance.is_available():
                            self.logger.info(f"使用 {name} 上传器")
                            try:
                                return uploader_instance.upload(file_path, object_name)
                            except Exception as e:
                                self.logger.warning(f"{name} 上传器失败: {e}，尝试下一个上传器")
                                continue

                raise FileOperationError("没有可用的文件上传器")

            elif uploader in self.uploaders:
                uploader_instance = self.uploaders[uploader]
                if not uploader_instance.is_available():
                    raise FileOperationError(f"上传器 {uploader} 不可用")

                return uploader_instance.upload(file_path, object_name)

            else:
                raise FileOperationError(f"不支持的上传器类型: {uploader}")

        except Exception as e:
            self.logger.error(f"文件上传失败: {e}")
            raise FileOperationError(f"文件上传失败: {e}")
    
    def get_available_uploaders(self) -> list:
        """获取可用的上传器列表"""
        available = []
        for name, uploader in self.uploaders.items():
            if uploader.is_available():
                available.append(name)
        return available
    
    def is_uploader_available(self, uploader: str) -> bool:
        """检查指定上传器是否可用"""
        if uploader not in self.uploaders:
            return False
        return self.uploaders[uploader].is_available()
