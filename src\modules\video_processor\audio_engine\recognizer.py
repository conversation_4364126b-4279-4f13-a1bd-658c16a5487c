"""语音识别器实现

支持多种语音识别引擎，包括Whisper、Azure Speech、Google Speech等。
"""

import os
import json
import tempfile
import time
import requests
from pathlib import Path
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

from .models import TranscriptSegment
from .file_uploader import FileUploadManager
from ....common.exceptions import SpeechRecognitionError
from ....common.logging import get_logger
from ....common.config import get_config


logger = get_logger(__name__)


class BaseSpeechRecognizer(ABC):
    """语音识别器基类"""
    
    @abstractmethod
    def recognize(self, audio_path: str, language: str = 'zh') -> List[TranscriptSegment]:
        """执行语音识别"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查识别器是否可用"""
        pass


class WhisperRecognizer(BaseSpeechRecognizer):
    """Whisper语音识别器"""
    
    def __init__(self, model_size: str = 'base'):
        self.model_size = model_size
        self.logger = get_logger(__name__)
        self._model = None
    
    def is_available(self) -> bool:
        """检查Whisper是否可用"""
        try:
            import whisper
            return True
        except ImportError:
            return False
    
    def recognize(self, audio_path: str, language: str = 'zh') -> List[TranscriptSegment]:
        """使用Whisper进行语音识别"""
        try:
            if not self.is_available():
                raise SpeechRecognitionError("Whisper未安装，请运行: pip install openai-whisper")
            
            import whisper
            
            self.logger.info(f"使用Whisper进行语音识别: {audio_path}")
            
            # 加载模型
            if self._model is None:
                self.logger.info(f"加载Whisper模型: {self.model_size}")
                self._model = whisper.load_model(self.model_size)
            
            # 执行识别
            result = self._model.transcribe(
                audio_path,
                language=self._convert_language_code(language),
                word_timestamps=True
            )
            
            # 转换结果
            segments = []
            for i, segment in enumerate(result['segments']):
                transcript_segment = TranscriptSegment(
                    id=i,
                    start_time=segment['start'],
                    end_time=segment['end'],
                    text=segment['text'].strip(),
                    confidence=segment.get('confidence', 0.9),
                    language=language
                )
                segments.append(transcript_segment)
            
            self.logger.info(f"Whisper识别完成，共 {len(segments)} 个段落")
            return segments
            
        except Exception as e:
            self.logger.error(f"Whisper识别失败: {e}")
            raise SpeechRecognitionError(f"Whisper识别失败: {e}")
    
    def _convert_language_code(self, language: str) -> str:
        """转换语言代码为Whisper支持的格式"""
        language_map = {
            'zh': 'zh',
            'en': 'en',
            'ja': 'ja',
            'ko': 'ko',
            'zh-cn': 'zh',
            'zh-tw': 'zh',
            'en-us': 'en',
            'en-gb': 'en'
        }
        return language_map.get(language.lower(), 'zh')


class AzureSpeechRecognizer(BaseSpeechRecognizer):
    """Azure Speech服务识别器"""
    
    def __init__(self, subscription_key: Optional[str] = None, region: Optional[str] = None):
        self.subscription_key = (
            subscription_key or
            get_config('audio.speech_engines.azure.api_key', '') or
            os.getenv('AZURE_SPEECH_KEY')
        )
        self.region = (
            region or
            get_config('audio.speech_engines.azure.region', 'eastasia') or
            os.getenv('AZURE_SPEECH_REGION', 'eastus')
        )
        self.endpoint = get_config('audio.speech_engines.azure.endpoint', '')
        self.logger = get_logger(__name__)
    
    def is_available(self) -> bool:
        """检查Azure Speech是否可用"""
        try:
            import azure.cognitiveservices.speech as speechsdk
            return self.subscription_key is not None
        except ImportError:
            return False
    
    def recognize(self, audio_path: str, language: str = 'zh') -> List[TranscriptSegment]:
        """使用Azure Speech进行语音识别"""
        try:
            if not self.is_available():
                raise SpeechRecognitionError("Azure Speech SDK未安装或未配置密钥")
            
            import azure.cognitiveservices.speech as speechsdk
            
            self.logger.info(f"使用Azure Speech进行语音识别: {audio_path}")
            
            # 配置语音服务
            speech_config = speechsdk.SpeechConfig(
                subscription=self.subscription_key,
                region=self.region
            )
            speech_config.speech_recognition_language = self._convert_language_code(language)
            
            # 配置音频输入
            audio_config = speechsdk.audio.AudioConfig(filename=audio_path)
            
            # 创建识别器
            speech_recognizer = speechsdk.SpeechRecognizer(
                speech_config=speech_config,
                audio_config=audio_config
            )
            
            # 执行识别
            result = speech_recognizer.recognize_once()
            
            if result.reason == speechsdk.ResultReason.RecognizedSpeech:
                # 简化实现：将整个音频作为一个段落
                # 实际应用中可能需要使用连续识别来获取时间戳
                segments = [TranscriptSegment(
                    id=0,
                    start_time=0.0,
                    end_time=self._get_audio_duration(audio_path),
                    text=result.text,
                    confidence=0.9,  # Azure不直接提供置信度
                    language=language
                )]
                
                self.logger.info(f"Azure Speech识别完成")
                return segments
            else:
                raise SpeechRecognitionError(f"Azure Speech识别失败: {result.reason}")

        except Exception as e:
            self.logger.error(f"Azure Speech识别失败: {e}")
            raise SpeechRecognitionError(f"Azure Speech识别失败: {e}")
    
    def _convert_language_code(self, language: str) -> str:
        """转换语言代码为Azure支持的格式"""
        language_map = {
            'zh': 'zh-CN',
            'en': 'en-US',
            'ja': 'ja-JP',
            'ko': 'ko-KR',
            'zh-cn': 'zh-CN',
            'zh-tw': 'zh-TW',
            'en-us': 'en-US',
            'en-gb': 'en-GB'
        }
        return language_map.get(language.lower(), 'zh-CN')
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            import subprocess
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                audio_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            info = json.loads(result.stdout)
            return float(info['format']['duration'])
        except:
            return 0.0


class ParaformerRecognizer(BaseSpeechRecognizer):
    """阿里云Paraformer语音识别器"""

    def __init__(self, api_key: Optional[str] = None, model: str = 'paraformer-v2'):
        """初始化Paraformer识别器

        Args:
            api_key: 阿里云API密钥，如果不提供则从配置文件或环境变量获取
            model: 使用的模型名称，默认从配置文件获取，否则使用paraformer-v2
        """
        self.api_key = (
            api_key or
            get_config('audio.speech_engines.paraformer.api_key', '') or
            os.getenv('DASHSCOPE_API_KEY')
        )
        self.model = (
            model if model != 'paraformer-v2' else
            get_config('audio.speech_engines.paraformer.model', 'paraformer-v2')
        )
        self.endpoint = get_config('audio.speech_engines.paraformer.endpoint', 'https://dashscope.aliyuncs.com')
        self.logger = get_logger(__name__)
        self.file_uploader = FileUploadManager()

        # 支持的模型列表
        self.supported_models = {
            'paraformer-v2': {
                'description': 'Paraformer最新多语种语音识别模型',
                'languages': ['zh', 'en', 'ja', 'ko', 'de', 'fr', 'ru'],
                'sample_rates': 'any',
                'features': ['punctuation', 'itn', 'hot_words', 'language_hints']
            },
            'paraformer-8k-v2': {
                'description': 'Paraformer最新中文语音识别模型(8kHz)',
                'languages': ['zh'],
                'sample_rates': [8000],
                'features': ['punctuation', 'itn', 'hot_words']
            },
            'paraformer-v1': {
                'description': 'Paraformer中英文语音识别模型',
                'languages': ['zh', 'en'],
                'sample_rates': 'any',
                'features': ['punctuation', 'itn', 'hot_words']
            },
            'paraformer-8k-v1': {
                'description': 'Paraformer中文语音识别模型(8kHz)',
                'languages': ['zh'],
                'sample_rates': [8000],
                'features': ['punctuation', 'itn', 'hot_words']
            },
            'paraformer-mtl-v1': {
                'description': 'Paraformer多语言语音识别模型',
                'languages': ['zh', 'en', 'ja', 'ko', 'es', 'id', 'fr', 'de', 'it', 'ms'],
                'sample_rates': [16000, 22050, 44100, 48000],
                'features': ['punctuation', 'itn', 'hot_words']
            }
        }

    def is_available(self) -> bool:
        """检查Paraformer是否可用"""
        try:
            # 检查是否安装了dashscope SDK
            import dashscope
            # 检查API密钥是否配置
            return self.api_key is not None
        except ImportError:
            return False

    def recognize(self, audio_path: str, language: str = 'zh') -> List[TranscriptSegment]:
        """使用Paraformer进行语音识别

        Args:
            audio_path: 音频文件路径或URL
            language: 语言代码

        Returns:
            List[TranscriptSegment]: 识别结果

        Raises:
            AudioProcessingError: 识别失败
        """
        try:
            if not self.is_available():
                raise SpeechRecognitionError("Paraformer SDK未安装或未配置API密钥，请安装dashscope并配置DASHSCOPE_API_KEY环境变量")

            from dashscope.audio.asr import Transcription
            import dashscope

            # 设置API密钥
            dashscope.api_key = self.api_key

            self.logger.info(f"使用Paraformer模型 {self.model} 进行语音识别: {audio_path}")

            # 判断是本地文件还是URL
            if self._is_url(audio_path):
                file_url = audio_path
                self.logger.info(f"使用提供的URL: {file_url}")
            else:
                # 上传本地文件到可访问的URL
                file_url = self._upload_audio_file(audio_path)

            # 准备请求参数
            params = {
                'model': self.model,
                'file_urls': [file_url]
            }

            # 根据模型添加语言提示
            if self.model == 'paraformer-v2':
                language_hints = self._convert_language_hints(language)
                if language_hints:
                    params['language_hints'] = language_hints

            # 异步提交任务
            task_response = Transcription.async_call(**params)

            if task_response.status_code != 200:
                raise SpeechRecognitionError(f"提交识别任务失败: {task_response.message}")

            # 等待任务完成
            transcribe_response = Transcription.wait(task=task_response.output.task_id)

            if transcribe_response.status_code != 200:
                raise SpeechRecognitionError(f"获取识别结果失败: {transcribe_response.message}")

            # 解析识别结果
            segments = self._parse_transcription_results(transcribe_response.output, language)

            self.logger.info(f"Paraformer识别完成，共 {len(segments)} 个段落")
            return segments

        except Exception as e:
            self.logger.error(f"Paraformer识别失败: {e}")
            raise SpeechRecognitionError(f"Paraformer识别失败: {e}")

    def _is_url(self, path: str) -> bool:
        """判断路径是否为URL"""
        return path.startswith(('http://', 'https://'))

    def _upload_audio_file(self, audio_path: str) -> str:
        """上传音频文件到可访问的URL

        Args:
            audio_path: 本地音频文件路径

        Returns:
            str: 可访问的文件URL

        Raises:
            AudioProcessingError: 上传失败
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_path):
                raise SpeechRecognitionError(f"音频文件不存在: {audio_path}")

            # 生成唯一的对象名称
            file_name = Path(audio_path).name
            timestamp = int(time.time())
            object_name = f"paraformer/{timestamp}_{file_name}"

            # 使用文件上传管理器上传文件
            url = self.file_uploader.upload(audio_path, uploader='auto', object_name=object_name)

            self.logger.info(f"音频文件上传成功: {audio_path} -> {url}")
            return url

        except Exception as e:
            self.logger.error(f"音频文件上传失败: {e}")
            raise SpeechRecognitionError(f"音频文件上传失败: {e}")

    def _convert_language_hints(self, language: str) -> List[str]:
        """转换语言代码为Paraformer支持的语言提示格式"""
        language_map = {
            'zh': ['zh'],
            'en': ['en'],
            'ja': ['ja'],
            'ko': ['ko'],
            'de': ['de'],
            'fr': ['fr'],
            'ru': ['ru'],
            'zh-cn': ['zh'],
            'zh-tw': ['zh'],
            'en-us': ['en'],
            'en-gb': ['en'],
            'yue': ['yue']  # 粤语
        }

        hints = language_map.get(language.lower(), ['zh', 'en'])
        return hints

    def _parse_transcription_results(self, output, language: str) -> List[TranscriptSegment]:
        """解析Paraformer识别结果

        Args:
            output: Paraformer API返回的结果
            language: 语言代码

        Returns:
            List[TranscriptSegment]: 转换后的识别结果
        """
        segments = []

        if not hasattr(output, 'results') or not output.results:
            return segments

        segment_id = 0

        for result in output.results:
            # 处理不同的结果格式
            if isinstance(result, dict):
                subtask_status = result.get('subtask_status', 'UNKNOWN')
                file_url = result.get('file_url', 'unknown')
                transcription_url = result.get('transcription_url', '')
            else:
                subtask_status = getattr(result, 'subtask_status', 'UNKNOWN')
                file_url = getattr(result, 'file_url', 'unknown')
                transcription_url = getattr(result, 'transcription_url', '')

            if subtask_status != 'SUCCEEDED':
                self.logger.warning(f"子任务失败: {file_url}")
                continue

            # 下载并解析识别结果JSON
            try:
                transcription_data = self._download_transcription_result(transcription_url)

                if 'transcripts' in transcription_data:
                    for transcript in transcription_data['transcripts']:
                        if 'sentences' in transcript:
                            for sentence in transcript['sentences']:
                                segment = TranscriptSegment(
                                    id=segment_id,
                                    start_time=sentence.get('begin_time', 0) / 1000.0,  # 转换为秒
                                    end_time=sentence.get('end_time', 0) / 1000.0,
                                    text=sentence.get('text', '').strip(),
                                    confidence=0.9,  # Paraformer不直接提供置信度
                                    language=language,
                                    speaker_id=str(sentence.get('speaker_id')) if 'speaker_id' in sentence else None
                                )
                                segments.append(segment)
                                segment_id += 1

            except Exception as e:
                self.logger.error(f"解析识别结果失败: {e}")
                continue

        return segments

    def _download_transcription_result(self, url: str) -> Dict[str, Any]:
        """下载识别结果JSON文件

        Args:
            url: 识别结果文件URL

        Returns:
            Dict[str, Any]: 解析后的JSON数据

        Raises:
            SpeechRecognitionError: 下载或解析失败
        """
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise SpeechRecognitionError(f"下载识别结果失败: {e}")


class SpeechRecognizer:
    """语音识别器管理类"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.recognizers = {
            'whisper': WhisperRecognizer(),
            'azure': AzureSpeechRecognizer(),
            'paraformer': ParaformerRecognizer(),
        }
    
    def recognize(self, audio_path: str, language: str = 'zh', 
                 engine: str = 'whisper') -> List[TranscriptSegment]:
        """执行语音识别
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码
            engine: 识别引擎
            
        Returns:
            List[TranscriptSegment]: 识别结果
            
        Raises:
            AudioProcessingError: 识别失败
        """
        try:
            if engine not in self.recognizers:
                raise SpeechRecognitionError(f"不支持的识别引擎: {engine}")
            
            recognizer = self.recognizers[engine]
            
            if not recognizer.is_available():
                # 尝试使用备用引擎
                for backup_engine, backup_recognizer in self.recognizers.items():
                    if backup_engine != engine and backup_recognizer.is_available():
                        self.logger.warning(f"引擎 {engine} 不可用，使用备用引擎 {backup_engine}")
                        recognizer = backup_recognizer
                        break
                else:
                    raise SpeechRecognitionError(f"识别引擎 {engine} 不可用且无可用的备用引擎")
            
            return recognizer.recognize(audio_path, language)
            
        except Exception as e:
            self.logger.error(f"语音识别失败: {e}")
            raise SpeechRecognitionError(f"语音识别失败: {e}")
    
    def get_available_engines(self) -> List[str]:
        """获取可用的识别引擎列表"""
        available = []
        for engine, recognizer in self.recognizers.items():
            if recognizer.is_available():
                available.append(engine)
        return available
    
    def is_engine_available(self, engine: str) -> bool:
        """检查指定引擎是否可用"""
        if engine not in self.recognizers:
            return False
        return self.recognizers[engine].is_available()
