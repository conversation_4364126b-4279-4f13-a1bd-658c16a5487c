#!/usr/bin/env python3
"""项目结构验证脚本

验证VideoReader项目的完整性和基本功能。
"""

import sys
import os
from pathlib import Path
import importlib.util

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def check_file_exists(file_path: Path, description: str) -> bool:
    """检查文件是否存在"""
    if file_path.exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def check_directory_exists(dir_path: Path, description: str) -> bool:
    """检查目录是否存在"""
    if dir_path.exists() and dir_path.is_dir():
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (不存在)")
        return False

def check_python_import(module_name: str, description: str) -> bool:
    """检查Python模块是否可以导入"""
    try:
        __import__(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"❌ {description}: {module_name} (导入失败: {e})")
        return False

def main():
    """主验证函数"""
    print("🔍 VideoReader 项目结构验证")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 检查项目根目录文件
    print("\n📁 项目根目录文件:")
    root_files = [
        (project_root / "README.md", "README文件"),
        (project_root / "requirements.txt", "生产依赖文件"),
        (project_root / "requirements-dev.txt", "开发依赖文件"),
        (project_root / "setup.py", "安装配置文件"),
        (project_root / "pyproject.toml", "项目配置文件"),
        (project_root / "pytest.ini", "测试配置文件"),
        (project_root / ".gitignore", "Git忽略文件"),
        (project_root / "MANIFEST.in", "包清单文件"),
    ]
    
    for file_path, description in root_files:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # 检查源代码目录结构
    print("\n📁 源代码目录结构:")
    src_dirs = [
        (src_path, "源代码根目录"),
        (src_path / "core", "核心数据层"),
        (src_path / "common", "通用基础层"),
        (src_path / "modules", "功能模块层"),
        (src_path / "modules" / "video_processor", "视频预处理模块"),
        (src_path / "modules" / "storage_engine", "存储引擎模块"),
        (src_path / "modules" / "search_engine", "搜索引擎模块"),
        (src_path / "ui", "用户界面层"),
    ]
    
    for dir_path, description in src_dirs:
        if not check_directory_exists(dir_path, description):
            all_checks_passed = False
    
    # 检查核心文件
    print("\n📄 核心源代码文件:")
    core_files = [
        (src_path / "main.py", "主程序入口"),
        (src_path / "app.py", "GUI应用入口"),
        (src_path / "cli.py", "CLI应用入口"),
        (src_path / "function_interface.py", "功能接口层"),
        (src_path / "core" / "events.py", "事件系统"),
        (src_path / "core" / "models.py", "数据模型"),
        (src_path / "common" / "config.py", "配置管理"),
        (src_path / "common" / "logging.py", "日志系统"),
        (src_path / "common" / "utils.py", "工具函数"),
        (src_path / "common" / "exceptions.py", "异常定义"),
    ]
    
    for file_path, description in core_files:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # 检查模块接口文件
    print("\n🔌 模块接口文件:")
    interface_files = [
        (src_path / "modules" / "video_processor" / "interface.py", "视频处理接口"),
        (src_path / "modules" / "storage_engine" / "interface.py", "存储引擎接口"),
        (src_path / "modules" / "search_engine" / "interface.py", "搜索引擎接口"),
        (src_path / "ui" / "interface.py", "用户界面接口"),
    ]
    
    for file_path, description in interface_files:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # 检查测试目录
    print("\n🧪 测试目录结构:")
    test_dirs = [
        (project_root / "tests", "测试根目录"),
        (project_root / "tests" / "unit", "单元测试目录"),
        (project_root / "tests" / "integration", "集成测试目录"),
    ]
    
    for dir_path, description in test_dirs:
        if not check_directory_exists(dir_path, description):
            all_checks_passed = False
    
    # 检查测试文件
    print("\n🧪 测试文件:")
    test_files = [
        (project_root / "tests" / "conftest.py", "测试配置文件"),
        (project_root / "tests" / "run_tests.py", "测试运行脚本"),
        (project_root / "tests" / "unit" / "test_common.py", "通用模块测试"),
        (project_root / "tests" / "unit" / "test_core.py", "核心模块测试"),
        (project_root / "tests" / "integration" / "test_function_interface.py", "功能接口集成测试"),
    ]
    
    for file_path, description in test_files:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # 检查文档目录
    print("\n📚 文档文件:")
    doc_files = [
        (project_root / "doc" / "模块化架构设计.md", "架构设计文档"),
        (project_root / "doc" / "api.md", "API文档"),
        (project_root / "doc" / "开发指南.md", "开发指南"),
        (project_root / "doc" / "faq.md", "常见问题"),
        (project_root / "doc" / "项目总结.md", "项目总结"),
    ]
    
    for file_path, description in doc_files:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # 尝试导入核心模块
    print("\n🐍 Python模块导入测试:")
    try:
        # 测试基础导入
        import core.events
        import core.models
        import common.config
        import common.logging
        import common.utils
        import common.exceptions
        print("✅ 核心模块导入成功")
        
        # 测试功能接口
        import function_interface
        print("✅ 功能接口模块导入成功")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        all_checks_passed = False
    
    # 检查__init__.py文件
    print("\n📦 __init__.py文件:")
    init_files = [
        src_path / "__init__.py",
        src_path / "core" / "__init__.py",
        src_path / "common" / "__init__.py",
        src_path / "modules" / "__init__.py",
        src_path / "modules" / "video_processor" / "__init__.py",
        src_path / "modules" / "storage_engine" / "__init__.py",
        src_path / "modules" / "search_engine" / "__init__.py",
        src_path / "ui" / "__init__.py",
    ]
    
    for init_file in init_files:
        if not check_file_exists(init_file, f"包初始化文件"):
            all_checks_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 所有检查通过！项目结构完整。")
        print("\n📋 下一步操作:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 运行测试: python tests/run_tests.py")
        print("3. 启动应用: python src/main.py")
        return 0
    else:
        print("⚠️  部分检查未通过，请检查缺失的文件或目录。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
