# VideoReader 架构设计文档

## 1. 系统架构图

```mermaid
graph TB
    subgraph UI["用户界面层"]
        A[main.py] --> B[app.py]
        A --> C[cli.py]
        B --> D[MainWindow]
        D --> E[VideoPlayer]
        D --> F[SegmentList]
        D --> G[TextDisplay]
        D --> H[ControlPanel]
    end

    subgraph Interface["功能接口层"]
        I[function_interface.py]
    end

    subgraph Business["业务逻辑层"]
        J[SpeechRecognition]
        K[MetadataManager]
        L[ExportManager]
        M[ParserFactory]
        M --> N[SceneChangeParser]
        M --> O[TextLengthParser]
        M --> P[TimeFixedParser]
        M --> Q[SilenceParser]
    end

    subgraph Core["核心抽象层"]
        R[BaseParser]
        S[VideoProcessor]
        T[AudioProcessor]
    end

    subgraph Utils["工具层"]
        U[FileUtils]
        V[ImageUtils]
        W[Config]
    end

    B --> I
    C --> I
    I --> J
    I --> K
    I --> L
    I --> M
    N --> R
    O --> R
    P --> R
    Q --> R
    J --> S
    J --> T
    K --> U
    L --> U
    S --> V
    T --> V

    classDef uiLayer fill:#e1f5fe
    classDef interfaceLayer fill:#f3e5f5
    classDef businessLayer fill:#e8f5e8
    classDef coreLayer fill:#fff3e0
    classDef utilsLayer fill:#fce4ec

    class A,B,C,D,E,F,G,H uiLayer
    class I interfaceLayer
    class J,K,L,M,N,O,P,Q businessLayer
    class R,S,T coreLayer
    class U,V,W utilsLayer
```

## 2. 类图设计

```mermaid
classDiagram
    class BaseParser {
        <<abstract>>
        +config dict
        +__init__(config)
        +parse(video_path, transcript) list*
        +get_config() dict*
        +validate_config(config) bool*
        +extract_segments(video_info, transcript) list*
    }

    class SceneChangeParser {
        +threshold float
        +min_length int
        +max_length int
        +parse(video_path, transcript) list
        +calculate_scene_changes(frames) list
        +merge_short_segments(segments) list
    }

    class TextLengthParser {
        +segment_length int
        +split_strategy str
        +parse(video_path, transcript) list
        +split_by_length(text, timestamps) list
        +find_sentence_boundaries(text) list
    }

    class VideoProcessor {
        +video_path str
        +video_info dict
        +load_video(path) bool
        +extract_frames(start, end) list
        +get_key_frame(start, end) Image
        +calculate_frame_diff(frame1, frame2) float
        +get_video_info() dict
    }

    class AudioProcessor {
        +audio_path str
        +extract_audio(video_path) str
        +detect_silence(audio_data) list
        +get_audio_features() dict
    }

    class SpeechRecognition {
        +engine str
        +language str
        +recognize(audio_path) dict
        +process_segments(audio_segments) list
        +save_transcript(transcript, path) bool
    }

    class MetadataManager {
        +metadata dict
        +save_metadata(data, path) bool
        +load_metadata(path) dict
        +validate_metadata(data) bool
        +update_metadata(updates) bool
    }

    class VideoReaderApp {
        +main_window MainWindow
        +current_video str
        +current_metadata dict
        +load_video(path) bool
        +parse_video(parser_type) bool
        +play_segment(segment_id) bool
        +export_data(format) bool
    }

    BaseParser <|-- SceneChangeParser
    BaseParser <|-- TextLengthParser
    VideoProcessor --> BaseParser
    AudioProcessor --> BaseParser
    SpeechRecognition --> AudioProcessor
    MetadataManager --> VideoReaderApp
    VideoReaderApp --> BaseParser
    VideoReaderApp --> VideoProcessor
    VideoReaderApp --> SpeechRecognition
```

## 3. 数据流图

```mermaid
flowchart TD
    A[用户选择视频文件] --> B[VideoProcessor加载视频]
    B --> C[AudioProcessor提取音频]
    C --> D[SpeechRecognition语音识别]
    D --> E[生成带时间戳的文本]
    E --> F[用户选择解析方式]
    F --> G[BaseParser子类解析]
    G --> H[生成视频段落]
    H --> I[MetadataManager保存元数据]
    I --> J[界面显示段落列表]
    J --> K[用户点击段落]
    K --> L[VideoProcessor提取关键帧]
    L --> M[显示图片和文本]
    M --> N[用户点击播放]
    N --> O[VideoProcessor播放视频段]

    style A fill:#ffebee
    style J fill:#e8f5e8
    style M fill:#e1f5fe
```

## 4. 模块依赖关系

```mermaid
graph LR
    subgraph External["外部依赖"]
        EXT1[FFmpeg]
        EXT2[OpenCV]
        EXT3[Whisper]
        EXT4[PyQt]
    end

    subgraph CoreModule["核心模块"]
        CORE1[VideoProcessor]
        CORE2[AudioProcessor]
        CORE3[BaseParser]
    end

    subgraph BusinessModule["业务模块"]
        BIZ1[SpeechRecognition]
        BIZ2[Parsers]
        BIZ3[MetadataManager]
    end

    subgraph UIModule["界面模块"]
        UI1[MainWindow]
        UI2[Components]
    end

    EXT1 --> CORE1
    EXT2 --> CORE1
    EXT3 --> BIZ1
    EXT4 --> UI1

    CORE1 --> BIZ2
    CORE2 --> BIZ1
    CORE3 --> BIZ2

    BIZ1 --> UI1
    BIZ2 --> UI1
    BIZ3 --> UI1
```

## 5. 线程模型设计

```mermaid
sequenceDiagram
    participant UI as UIThread
    participant FI as FunctionInterface
    participant VP as VideoProcessor
    participant SR as SpeechRecognition
    participant Parser as Parser

    UI->>FI: 请求解析视频
    FI->>VP: 加载视频文件
    VP-->>FI: 返回视频信息
    FI->>SR: 开始语音识别

    loop 进度更新
        SR-->>FI: 更新识别进度
        FI-->>UI: 返回进度信息
        UI->>UI: 更新进度条
    end

    SR-->>FI: 返回识别结果
    FI->>Parser: 开始解析分段

    loop 进度更新
        Parser-->>FI: 更新解析进度
        FI-->>UI: 返回进度信息
        UI->>UI: 更新进度条
    end

    Parser-->>FI: 返回分段结果
    FI-->>UI: 返回最终结果
    UI->>UI: 显示段落列表
```

## 6. 配置管理

### 6.1 配置文件结构
```json
{
    "app_config": {
        "theme": "light",
        "language": "zh-CN",
        "auto_save": true,
        "cache_size": 1024
    },
    "parser_configs": {
        "scene_change": {
            "threshold": 30,
            "min_length": 10,
            "max_length": 300
        },
        "text_length": {
            "segment_length": 200,
            "split_strategy": "sentence"
        }
    },
    "speech_recognition": {
        "engine": "whisper",
        "language": "zh",
        "model_size": "base"
    },
    "video_processing": {
        "key_frame_method": "middle",
        "thumbnail_size": [320, 240],
        "supported_formats": ["mp4", "avi", "mov", "mkv"]
    }
}
```

## 7. 错误处理策略

```mermaid
flowchart TD
    A[操作开始] --> B{检查输入参数}
    B -->|无效| C[返回参数错误]
    B -->|有效| D[执行操作]
    D --> E{操作是否成功}
    E -->|成功| F[返回结果]
    E -->|失败| G{错误类型}
    G -->|文件错误| H[记录日志并提示用户]
    G -->|网络错误| I[重试机制]
    G -->|系统错误| J[记录详细日志]
    H --> K[返回错误信息]
    I --> L{重试次数}
    L -->|未超限| D
    L -->|超限| K
    J --> K
```

## 8. 性能优化策略

### 8.1 内存管理
- 视频帧按需加载，不全部加载到内存
- 实现LRU缓存机制
- 及时释放不再使用的资源

### 8.2 处理优化
- 多线程处理音视频
- 分块处理大文件
- 预加载下一个段落的关键帧

### 8.3 界面优化
- 虚拟列表显示大量段落
- 延迟加载文本内容
- 异步更新界面元素

这个架构设计确保了系统的可扩展性、可维护性和高性能，同时遵循了您提出的界面与逻辑分离、模块化开发的原则。
