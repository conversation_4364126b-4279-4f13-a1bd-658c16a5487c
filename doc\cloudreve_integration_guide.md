# Cloudreve集成指南

本文档介绍如何配置和使用Cloudreve作为VideoReader项目的文件存储解决方案，特别是为阿里云Paraformer语音识别提供公网可访问的文件URL。

## 概述

Cloudreve是一个支持多种存储后端的云存储系统。在VideoReader项目中，我们使用Cloudreve作为文件上传器，将本地文件上传到Cloudreve获取公网URL，然后供阿里云Paraformer模型进行语音识别。

## 架构设计

```
本地文件 -> CloudreveUploader -> Cloudreve服务器 -> 公网URL -> 阿里云Paraformer
```

### 核心组件

1. **CloudreveUploader**: 实现了BaseFileUploader接口的Cloudreve客户端
2. **FileUploadManager**: 文件上传管理器，支持多种上传器
3. **ParaformerRecognizer**: 语音识别器，自动使用Cloudreve上传器

## 安装和配置

### 1. 安装依赖

```bash
pip install requests tomli tomli-w
```

注意：Python 3.11+ 内置了tomllib，只需要安装tomli-w用于写入TOML文件。

### 2. 部署Cloudreve服务器

#### 下载和安装

```bash
# 下载Cloudreve 4.1.2
wget https://github.com/cloudreve/Cloudreve/releases/download/4.1.2/cloudreve_4.1.2_linux_amd64.tar.gz

# 解压
tar -zxf cloudreve_4.1.2_linux_amd64.tar.gz

# 运行
./cloudreve
```

#### 基本配置

首次运行后，Cloudreve会生成管理员账户信息，请记录下来。

### 3. 配置方式

VideoReader支持多种配置方式，按优先级排序：

#### 方法1：TOML配置文件（推荐）

##### 使用配置向导
```bash
python scripts/create_config.py
```

##### 手动创建配置文件
创建或编辑 `~/.videoreader/config.toml`：

```toml
[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"
model = "paraformer-v2"
endpoint = "https://dashscope.aliyuncs.com"

[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
username = "your-username"
password = "your-password"
# 或者使用访问令牌（推荐）
token = "your-access-token"
timeout = 60
chunk_size = 5242880  # 5MB
max_retries = 3

[file_uploader.uploaders.oss]
access_key_id = "your-oss-key-id"
access_key_secret = "your-oss-key-secret"
endpoint = "your-oss-endpoint"
bucket_name = "your-bucket-name"
```

#### 方法2：环境变量

```bash
# Linux/macOS
export DASHSCOPE_API_KEY='your-dashscope-api-key'
export CLOUDREVE_BASE_URL='http://your-cloudreve-server.com:5212'
export CLOUDREVE_USERNAME='your-username'
export CLOUDREVE_PASSWORD='your-password'
# 或者使用访问令牌
export CLOUDREVE_TOKEN='your-access-token'

# Windows
set DASHSCOPE_API_KEY=your-dashscope-api-key
set CLOUDREVE_BASE_URL=http://your-cloudreve-server.com:5212
set CLOUDREVE_USERNAME=your-username
set CLOUDREVE_PASSWORD=your-password
```

#### 方法3：代码中直接传递参数

```python
from src.modules.video_processor.audio_engine.file_uploader import CloudreveUploader
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer

# 直接传递参数
uploader = CloudreveUploader(
    base_url='http://your-server:5212',
    username='your-username',
    password='your-password'
)

recognizer = ParaformerRecognizer(
    api_key='your-dashscope-api-key',
    model='paraformer-v2'
)
```

## 使用方法

### 1. 直接使用CloudreveUploader

```python
from src.modules.video_processor.audio_engine.file_uploader import CloudreveUploader

# 使用环境变量配置
uploader = CloudreveUploader()

# 或者直接传递参数
uploader = CloudreveUploader(
    base_url='http://your-cloudreve-server.com:5212',
    username='your-username',
    password='your-password'
)

# 上传文件
file_url = uploader.upload('test.mp4', 'videos/test.mp4')
print(f"文件URL: {file_url}")
```

### 2. 使用FileUploadManager

```python
from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager

manager = FileUploadManager()

# 自动选择可用的上传器（优先选择Cloudreve）
file_url = manager.upload('test.mp4', uploader='auto')

# 或者明确指定使用Cloudreve
file_url = manager.upload('test.mp4', uploader='cloudreve')
```

### 3. 与ParaformerRecognizer集成

```python
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer

# 创建识别器（会自动使用Cloudreve上传器）
recognizer = ParaformerRecognizer(model='paraformer-v2')

# 直接识别本地文件（会自动上传到Cloudreve）
segments = recognizer.recognize('test.mp4', language='zh')
```

## API参考

### CloudreveUploader类

#### 构造函数

```python
CloudreveUploader(
    base_url: Optional[str] = None,
    username: Optional[str] = None,
    password: Optional[str] = None,
    token: Optional[str] = None
)
```

#### 主要方法

- `is_available() -> bool`: 检查上传器是否可用
- `upload(file_path: str, object_name: Optional[str] = None) -> str`: 上传文件

### 上传流程

1. **身份认证**: 使用用户名密码或访问令牌进行认证
2. **创建上传会话**: 计算文件MD5，创建上传会话
3. **分块上传**: 将文件分块上传到Cloudreve
4. **完成上传**: 完成上传并获取文件URL
5. **创建直接链接**: 创建公网可访问的直接下载链接

## 测试

### 运行测试脚本

```bash
python test_cloudreve_uploader.py
```

### 查看配置示例

```bash
python cloudreve_config_example.py
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查Cloudreve服务器是否正常运行
   - 检查网络连接
   - 确认BASE_URL配置正确

2. **认证失败**
   - 检查用户名密码是否正确
   - 检查访问令牌是否有效
   - 确认用户有上传权限

3. **上传失败**
   - 检查文件是否存在
   - 检查存储空间是否足够
   - 检查文件大小是否超过限制

4. **URL访问失败**
   - 检查Cloudreve的CORS配置
   - 确认直接链接功能已启用
   - 检查文件权限设置

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 安全注意事项

1. **访问令牌**: 生产环境建议使用访问令牌而不是用户名密码
2. **HTTPS**: 生产环境建议使用HTTPS协议
3. **权限控制**: 合理设置用户权限和文件访问权限
4. **定期清理**: 定期清理临时上传的文件

## 性能优化

1. **分块大小**: 根据网络条件调整分块大小
2. **并发上传**: 对于大文件可以考虑并发上传多个块
3. **缓存**: 对于重复上传的文件可以实现缓存机制
4. **压缩**: 对于某些文件类型可以考虑压缩后上传

## 扩展功能

### 自定义配置

```python
uploader = CloudreveUploader(
    base_url='http://your-server:5212',
    username='user',
    password='pass'
)

# 自定义上传路径
file_url = uploader.upload('local_file.mp4', 'custom/path/remote_file.mp4')
```

### 批量上传

```python
files = ['file1.mp4', 'file2.mp4', 'file3.mp4']
urls = []

for file_path in files:
    url = uploader.upload(file_path)
    urls.append(url)
```

## 版本兼容性

- **Cloudreve**: 支持4.1.2及以上版本
- **Python**: 支持3.7及以上版本
- **依赖**: requests库

## 更新日志

- **v1.0.0**: 初始版本，支持基本的文件上传功能
- 支持用户名密码和访问令牌认证
- 支持分块上传大文件
- 集成到ParaformerRecognizer中
