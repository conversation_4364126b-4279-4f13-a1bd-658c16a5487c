"""视频处理对话框

独立的视频处理对话框，包含视频处理设置和进度显示。
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox,
    QProgressBar, QGroupBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QTabWidget, QWidget, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QIcon

try:
    from ...core.models import ParserType, ProcessConfig
    from ...common.logging import get_logger
except ImportError:
    try:
        # 如果相对导入失败，尝试绝对导入
        from src.core.models import ParserType, ProcessConfig
        from src.common.logging import get_logger
    except ImportError:
        # 最后尝试直接导入
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from core.models import ParserType, ProcessConfig
        from common.logging import get_logger


class VideoProcessDialog(QDialog):
    """视频处理对话框"""
    
    # 信号定义
    process_requested = Signal(object, dict)  # 请求处理视频
    cancel_requested = Signal()  # 请求取消处理
    
    def __init__(self, video_path: str = "", parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 视频信息
        self.video_path = video_path
        
        # 处理状态
        self.is_processing = False
        self.current_progress = 0.0
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()
        
        self.logger.info("视频处理对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("视频处理")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # 视频信息显示
        info_group = QGroupBox("视频信息")
        info_layout = QVBoxLayout(info_group)
        
        self.video_path_label = QLabel(self.video_path or "未选择视频文件")
        self.video_path_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; background: white; }")
        info_layout.addWidget(self.video_path_label)
        
        layout.addWidget(info_group)
        
        # 处理设置标签页
        tab_widget = QTabWidget()
        
        # 解析器设置标签页
        parser_tab = self._create_parser_tab()
        tab_widget.addTab(parser_tab, "解析器设置")
        
        # 语音识别设置标签页
        speech_tab = self._create_speech_tab()
        tab_widget.addTab(speech_tab, "语音识别")
        
        layout.addWidget(tab_widget)
        
        # 进度显示区域
        progress_group = QGroupBox("处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("就绪")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始处理")
        self.start_button.clicked.connect(self._on_start_clicked)
        button_layout.addWidget(self.start_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        button_layout.addWidget(self.cancel_button)
        
        self.close_button = QPushButton("关闭")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def _create_parser_tab(self) -> QWidget:
        """创建解析器设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 解析器选择
        parser_group = QGroupBox("解析器设置")
        parser_layout = QVBoxLayout(parser_group)
        
        # 解析器类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("解析器类型:"))
        
        self.parser_type_combo = QComboBox()
        self.parser_type_combo.addItem("场景变化分段", ParserType.SCENE_CHANGE)
        self.parser_type_combo.addItem("文本长度分段", ParserType.TEXT_LENGTH)
        self.parser_type_combo.addItem("时间固定分段", ParserType.TIME_FIXED)
        self.parser_type_combo.addItem("静音分段", ParserType.SILENCE_BASED)
        self.parser_type_combo.currentTextChanged.connect(self._on_parser_type_changed)
        type_layout.addWidget(self.parser_type_combo)
        
        parser_layout.addLayout(type_layout)
        
        # 解析器参数
        self.parser_params_widget = QWidget()
        self.parser_params_layout = QVBoxLayout(self.parser_params_widget)
        parser_layout.addWidget(self.parser_params_widget)
        
        layout.addWidget(parser_group)
        
        # 更新解析器参数显示
        self._on_parser_type_changed()
        
        layout.addStretch()
        return tab
    
    def _create_speech_tab(self) -> QWidget:
        """创建语音识别设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 语音识别设置
        speech_group = QGroupBox("语音识别设置")
        speech_layout = QVBoxLayout(speech_group)
        
        # 语言选择
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("语言:"))
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["中文", "英文", "日文", "韩文"])
        lang_layout.addWidget(self.language_combo)
        
        speech_layout.addLayout(lang_layout)
        
        # 识别引擎
        engine_layout = QHBoxLayout()
        engine_layout.addWidget(QLabel("识别引擎:"))
        
        self.speech_engine_combo = QComboBox()
        self.speech_engine_combo.addItems(["Whisper", "Azure Speech", "Google Speech"])
        engine_layout.addWidget(self.speech_engine_combo)
        
        speech_layout.addLayout(engine_layout)
        
        layout.addWidget(speech_group)
        
        layout.addStretch()
        return tab
    
    def _connect_signals(self):
        """连接信号和槽"""
        pass
    
    def _on_parser_type_changed(self):
        """解析器类型改变处理"""
        # 清除之前的参数控件
        for i in reversed(range(self.parser_params_layout.count())):
            child = self.parser_params_layout.itemAt(i)
            if child.widget():
                child.widget().setParent(None)
        
        # 根据选择的解析器类型添加相应的参数控件
        parser_type = self.parser_type_combo.currentData()
        
        if parser_type == ParserType.SCENE_CHANGE:
            self._add_scene_change_params()
        elif parser_type == ParserType.TEXT_LENGTH:
            self._add_text_length_params()
        elif parser_type == ParserType.TIME_FIXED:
            self._add_time_fixed_params()
        elif parser_type == ParserType.SILENCE_BASED:
            self._add_silence_based_params()
    
    def _add_scene_change_params(self):
        """添加场景变化解析器参数"""
        # 阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("变化阈值:"))
        
        self.scene_threshold_spinbox = QDoubleSpinBox()
        self.scene_threshold_spinbox.setRange(0.1, 1.0)
        self.scene_threshold_spinbox.setSingleStep(0.1)
        self.scene_threshold_spinbox.setValue(0.3)
        threshold_layout.addWidget(self.scene_threshold_spinbox)
        
        self.parser_params_layout.addLayout(threshold_layout)
        
        # 最小时长
        min_duration_layout = QHBoxLayout()
        min_duration_layout.addWidget(QLabel("最小段落时长(秒):"))
        
        self.scene_min_duration_spinbox = QDoubleSpinBox()
        self.scene_min_duration_spinbox.setRange(1.0, 60.0)
        self.scene_min_duration_spinbox.setValue(2.0)
        min_duration_layout.addWidget(self.scene_min_duration_spinbox)
        
        self.parser_params_layout.addLayout(min_duration_layout)
    
    def _add_text_length_params(self):
        """添加文本长度解析器参数"""
        # 最大长度
        max_length_layout = QHBoxLayout()
        max_length_layout.addWidget(QLabel("最大字符数:"))
        
        self.text_max_length_spinbox = QSpinBox()
        self.text_max_length_spinbox.setRange(50, 1000)
        self.text_max_length_spinbox.setValue(200)
        max_length_layout.addWidget(self.text_max_length_spinbox)
        
        self.parser_params_layout.addLayout(max_length_layout)
        
        # 最小长度
        min_length_layout = QHBoxLayout()
        min_length_layout.addWidget(QLabel("最小字符数:"))
        
        self.text_min_length_spinbox = QSpinBox()
        self.text_min_length_spinbox.setRange(10, 200)
        self.text_min_length_spinbox.setValue(50)
        min_length_layout.addWidget(self.text_min_length_spinbox)
        
        self.parser_params_layout.addLayout(min_length_layout)

    def _add_time_fixed_params(self):
        """添加时间固定解析器参数"""
        # 段落时长
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("段落时长(秒):"))

        self.time_duration_spinbox = QDoubleSpinBox()
        self.time_duration_spinbox.setRange(10.0, 300.0)
        self.time_duration_spinbox.setValue(30.0)
        duration_layout.addWidget(self.time_duration_spinbox)

        self.parser_params_layout.addLayout(duration_layout)

        # 重叠时长
        overlap_layout = QHBoxLayout()
        overlap_layout.addWidget(QLabel("重叠时长(秒):"))

        self.time_overlap_spinbox = QDoubleSpinBox()
        self.time_overlap_spinbox.setRange(0.0, 30.0)
        self.time_overlap_spinbox.setValue(0.0)
        overlap_layout.addWidget(self.time_overlap_spinbox)

        self.parser_params_layout.addLayout(overlap_layout)

    def _add_silence_based_params(self):
        """添加静音分段解析器参数"""
        # 静音阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("静音阈值(dB):"))

        self.silence_threshold_spinbox = QDoubleSpinBox()
        self.silence_threshold_spinbox.setRange(-60.0, -20.0)
        self.silence_threshold_spinbox.setValue(-40.0)
        threshold_layout.addWidget(self.silence_threshold_spinbox)

        self.parser_params_layout.addLayout(threshold_layout)

        # 最小静音时长
        min_silence_layout = QHBoxLayout()
        min_silence_layout.addWidget(QLabel("最小静音时长(秒):"))

        self.silence_min_duration_spinbox = QDoubleSpinBox()
        self.silence_min_duration_spinbox.setRange(0.5, 10.0)
        self.silence_min_duration_spinbox.setValue(1.0)
        min_silence_layout.addWidget(self.silence_min_duration_spinbox)

        self.parser_params_layout.addLayout(min_silence_layout)

    def _on_start_clicked(self):
        """开始处理按钮点击处理"""
        try:
            # 获取解析器类型和参数
            parser_type = self.parser_type_combo.currentData()
            config = self._get_parser_config(parser_type)

            # 发送处理请求信号
            self.process_requested.emit(parser_type, config)

            # 更新UI状态
            self.set_processing_state(True)

        except Exception as e:
            self.logger.error(f"启动处理失败: {e}")
            QMessageBox.critical(self, "错误", f"启动处理失败: {e}")

    def _on_cancel_clicked(self):
        """取消按钮点击处理"""
        if self.is_processing:
            self.cancel_requested.emit()
        else:
            self.close()

    def _get_parser_config(self, parser_type: ParserType) -> dict:
        """获取解析器配置"""
        config = {}

        if parser_type == ParserType.SCENE_CHANGE:
            config = {
                'threshold': self.scene_threshold_spinbox.value(),
                'min_duration': self.scene_min_duration_spinbox.value()
            }
        elif parser_type == ParserType.TEXT_LENGTH:
            config = {
                'max_length': self.text_max_length_spinbox.value(),
                'min_length': self.text_min_length_spinbox.value()
            }
        elif parser_type == ParserType.TIME_FIXED:
            config = {
                'duration': self.time_duration_spinbox.value(),
                'overlap': self.time_overlap_spinbox.value()
            }
        elif parser_type == ParserType.SILENCE_BASED:
            config = {
                'silence_threshold': self.silence_threshold_spinbox.value(),
                'min_silence_duration': self.silence_min_duration_spinbox.value()
            }

        # 添加语音识别配置
        config.update({
            'language': self.language_combo.currentText(),
            'speech_engine': self.speech_engine_combo.currentText()
        })

        return config

    def set_processing_state(self, is_processing: bool):
        """设置处理状态"""
        self.is_processing = is_processing
        self.start_button.setEnabled(not is_processing)

        if is_processing:
            self.cancel_button.setText("取消处理")
            self.close_button.setEnabled(False)
        else:
            self.cancel_button.setText("取消")
            self.close_button.setEnabled(True)
            self.progress_bar.setValue(0)
            self.progress_label.setText("就绪")

    def update_progress(self, progress: float, message: str = ""):
        """更新进度"""
        self.current_progress = progress
        self.progress_bar.setValue(int(progress * 100))

        if message:
            self.progress_label.setText(message)

    def set_video_path(self, video_path: str):
        """设置视频路径"""
        self.video_path = video_path
        self.video_path_label.setText(video_path)

    def get_process_config(self) -> ProcessConfig:
        """获取完整的处理配置"""
        parser_type = self.parser_type_combo.currentData()
        parser_config = self._get_parser_config(parser_type)

        return ProcessConfig(
            parser_type=parser_type,
            parser_config=parser_config,
            language=self.language_combo.currentText().lower(),
            speech_engine=self.speech_engine_combo.currentText().lower()
        )
