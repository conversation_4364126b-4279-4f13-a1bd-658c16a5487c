"""自定义异常定义

定义VideoReader应用的具体业务异常。
"""

from .base import VideoReaderError, ProcessingError, FileOperationError, ValidationError


class VideoLoadError(FileOperationError):
    """视频加载错误"""
    pass


class AudioExtractionError(ProcessingError):
    """音频提取错误"""
    pass


class SpeechRecognitionError(ProcessingError):
    """语音识别错误"""
    pass


class ParsingError(ProcessingError):
    """解析错误"""
    pass


class ProcessCancelledError(ProcessingError):
    """处理被取消错误"""
    pass


class UnsupportedFormatError(ValidationError):
    """不支持的格式错误"""
    pass


class InvalidConfigError(ValidationError):
    """无效配置错误"""
    pass


class StorageError(FileOperationError):
    """存储错误"""
    pass


class SearchIndexError(ProcessingError):
    """搜索索引错误"""
    pass


class ExportError(ProcessingError):
    """导出错误"""
    pass


class UIError(VideoReaderError):
    """界面错误"""
    pass


class ComponentError(UIError):
    """组件错误"""
    pass
