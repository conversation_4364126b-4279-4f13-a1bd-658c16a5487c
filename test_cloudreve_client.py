#!/usr/bin/env python3
"""测试Cloudreve客户端"""

import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config
from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def test_cloudreve_client():
    """测试Cloudreve客户端基本功能"""
    print("=== 测试Cloudreve客户端 ===")
    
    # 从配置文件获取连接信息
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    if not all([base_url, username, password]):
        print("❌ 配置信息不完整，请检查config.toml文件")
        return False
    
    # 检查用户名是否为email格式
    if '@' not in username:
        print(f"❌ 用户名不是email格式: {username}")
        print("请将配置文件中的username改为email格式，如: <EMAIL>")
        return False
    
    try:
        # 创建客户端
        client = CloudreveClient(base_url, username, password)
        
        # 测试服务器连接
        print("\n1. 测试服务器连接...")
        version = client.ping()
        print(f"   ✅ 服务器版本: {version}")
        
        # 测试获取验证码
        print("\n2. 测试获取验证码...")
        try:
            captcha_image, ticket = client.get_captcha()
            print(f"   ✅ 验证码获取成功，ticket: {ticket[:20]}...")
        except CloudreveError as e:
            print(f"   ⚠️ 验证码获取失败: {e}")
        
        # 测试登录
        print("\n3. 测试用户登录...")
        try:
            login_result = client.login()
            user_info = login_result['user']
            print(f"   ✅ 登录成功: {user_info['email']}")
            print(f"   用户昵称: {user_info.get('nickname', 'N/A')}")
            print(f"   用户组: {user_info.get('group', {}).get('name', 'N/A')}")
            
        except CloudreveError as e:
            if e.code == 40003:  # 需要验证码
                print("   ⚠️ 需要验证码，尝试获取验证码后重新登录...")
                try:
                    captcha_image, ticket = client.get_captcha()
                    print(f"   验证码ticket: {ticket}")
                    print("   注意: 实际使用时需要用户输入验证码")
                    # 这里无法自动输入验证码，需要手动处理
                    return False
                except CloudreveError as e2:
                    print(f"   ❌ 获取验证码失败: {e2}")
                    return False
            else:
                print(f"   ❌ 登录失败: {e}")
                return False
        
        # 测试文件上传（创建临时文件）
        print("\n4. 测试文件上传...")
        try:
            # 创建临时测试文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write("这是一个测试文件，用于验证Cloudreve上传功能。\n")
                f.write(f"创建时间: {Path().cwd()}\n")
                temp_file = f.name
            
            temp_path = Path(temp_file)
            print(f"   创建临时文件: {temp_path.name}")
            
            # 定义进度回调
            def progress_callback(uploaded, total):
                percent = (uploaded / total) * 100
                print(f"   上传进度: {percent:.1f}% ({uploaded}/{total} 字节)")
            
            # 上传文件
            result = client.upload_file(str(temp_path), progress_callback=progress_callback)
            print(f"   ✅ 文件上传成功: {result}")
            
            # 清理临时文件
            temp_path.unlink()
            
        except CloudreveError as e:
            print(f"   ❌ 文件上传失败: {e}")
            # 清理临时文件
            if 'temp_path' in locals() and temp_path.exists():
                temp_path.unlink()
        
        # 测试文件管理功能
        print("\n5. 测试文件管理功能...")
        try:
            # 获取存储信息
            storage_info = client.get_storage_info()
            print(f"   存储空间: 已用 {storage_info.get('used', 0)} / 总计 {storage_info.get('total', 0)} 字节")

            # 列出根目录文件
            file_list = client.list_files("/")
            print(f"   根目录文件数量: {len(file_list.get('items', []))}")

            # 如果有文件，测试其他功能
            files = file_list.get('items', [])
            if files:
                first_file = files[0]
                file_id = first_file.get('id')
                file_name = first_file.get('name', 'unknown')

                print(f"   测试文件: {file_name}")

                # 获取文件详细信息
                file_info = client.get_file_info(file_id)
                print(f"   文件大小: {file_info.get('size', 0)} 字节")

                # 创建下载链接
                download_url = client.create_download_url(file_id)
                print(f"   下载链接: {download_url[:50]}...")

                # 创建分享链接
                share_info = client.create_share_link([file_id], expire_days=7)
                share_key = share_info.get('key', '')
                print(f"   分享链接: {share_key}")

                # 获取分享链接列表
                share_list = client.list_share_links()
                print(f"   分享链接数量: {len(share_list.get('items', []))}")

                # 删除刚创建的分享链接
                if share_key:
                    share_id = share_info.get('id')
                    if share_id:
                        client.delete_share_link(share_id)
                        print("   ✅ 分享链接已删除")

            print("   ✅ 文件管理功能测试完成")

        except CloudreveError as e:
            print(f"   ⚠️ 文件管理功能测试失败: {e}")

        # 测试搜索功能
        print("\n6. 测试搜索功能...")
        try:
            search_results = client.search_files("test", page_size=5)
            print(f"   搜索结果数量: {len(search_results.get('items', []))}")
            print("   ✅ 搜索功能测试完成")
        except CloudreveError as e:
            print(f"   ⚠️ 搜索功能测试失败: {e}")

        # 测试登出
        print("\n7. 测试用户登出...")
        if client.logout():
            print("   ✅ 登出成功")
        else:
            print("   ⚠️ 登出失败")

        print("\n🎉 Cloudreve客户端测试完成!")
        return True
        
    except CloudreveError as e:
        print(f"\n❌ 客户端测试失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试无效服务器地址
    print("\n1. 测试无效服务器地址...")
    try:
        client = CloudreveClient("http://invalid-server.com", "<EMAIL>", "password")
        client.ping()
        print("   ❌ 应该抛出异常")
    except CloudreveError as e:
        print(f"   ✅ 正确捕获异常: {e}")
    
    # 测试无效认证信息
    print("\n2. 测试无效认证信息...")
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    if base_url:
        try:
            client = CloudreveClient(base_url, "<EMAIL>", "wrongpassword")
            client.login()
            print("   ❌ 应该抛出异常")
        except CloudreveError as e:
            print(f"   ✅ 正确捕获异常: {e}")


def main():
    """主函数"""
    print("Cloudreve客户端测试")
    print("=" * 60)
    
    # 基本功能测试
    success = test_cloudreve_client()
    
    # 错误处理测试
    test_error_handling()
    
    if success:
        print("\n✅ 所有测试通过!")
    else:
        print("\n❌ 部分测试失败，请检查配置和网络连接")


if __name__ == "__main__":
    main()
