# VideoReader 开发指南

## 1. 开发环境搭建

### 1.1 系统要求
- Python 3.8 或更高版本
- FFmpeg（用于音视频处理）
- Git（版本控制）
- 至少4GB内存
- 至少2GB可用磁盘空间

### 1.2 环境配置步骤

#### 步骤1：安装Python依赖
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖（后续创建requirements.txt后）
pip install -r requirements.txt
```

#### 步骤2：安装FFmpeg
**Windows:**
1. 从 https://ffmpeg.org/download.html 下载FFmpeg
2. 解压到C:\ffmpeg
3. 将C:\ffmpeg\bin添加到系统PATH

**macOS:**
```bash
brew install ffmpeg
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ffmpeg
```

#### 步骤3：验证安装
```bash
python --version  # 应显示Python 3.8+
ffmpeg -version   # 应显示FFmpeg版本信息
```

## 2. 项目初始化

### 2.1 创建项目结构
```bash
mkdir VideoReader
cd VideoReader

# 创建目录结构
mkdir -p src/core src/business/parsers src/ui/components src/ui/styles src/utils tests doc
```

### 2.2 初始化Git仓库
```bash
git init
# 创建.gitignore文件
echo "__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis
.DS_Store
*.egg-info/
dist/
build/" > .gitignore
```

## 3. 核心开发流程

### 3.1 开发顺序建议
1. **第一阶段：核心抽象层**
   - 实现BaseParser基类
   - 实现VideoProcessor核心类
   - 实现AudioProcessor核心类

2. **第二阶段：业务逻辑层**
   - 实现SpeechRecognition语音识别
   - 实现具体的解析器（从SceneChangeParser开始）
   - 实现MetadataManager元数据管理

3. **第三阶段：功能接口层**
   - 实现function_interface.py
   - 确保线程安全和异步处理

4. **第四阶段：命令行界面**
   - 实现cli.py
   - 测试所有核心功能

5. **第五阶段：图形界面**
   - 实现界面组件
   - 实现主窗口
   - 集成所有功能

### 3.2 开发规范

#### 代码风格
```python
# 示例：类定义
class VideoProcessor:
    """视频处理器类
    
    负责视频文件的加载、帧提取、关键帧生成等功能。
    
    Attributes:
        video_path (str): 视频文件路径
        video_info (dict): 视频信息字典
    """
    
    def __init__(self, video_path: str = None):
        """初始化视频处理器
        
        Args:
            video_path: 视频文件路径，可选
        """
        self.video_path = video_path
        self.video_info = {}
        self._cap = None
    
    def load_video(self, video_path: str) -> bool:
        """加载视频文件
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            bool: 加载成功返回True，失败返回False
            
        Raises:
            FileNotFoundError: 视频文件不存在
            ValueError: 视频格式不支持
        """
        try:
            # 实现代码
            pass
        except Exception as e:
            logger.error(f"加载视频失败: {e}")
            return False
```

#### 异常处理
```python
# 自定义异常类
class VideoReaderError(Exception):
    """VideoReader基础异常类"""
    pass

class VideoLoadError(VideoReaderError):
    """视频加载异常"""
    pass

class ParseError(VideoReaderError):
    """解析异常"""
    pass

# 使用示例
def load_video(self, path: str) -> bool:
    try:
        if not os.path.exists(path):
            raise VideoLoadError(f"视频文件不存在: {path}")
        # 加载逻辑
        return True
    except VideoLoadError:
        raise  # 重新抛出自定义异常
    except Exception as e:
        raise VideoLoadError(f"加载视频时发生未知错误: {e}")
```

#### 日志记录
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('videoreader.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 使用示例
def parse_video(self, video_path: str) -> dict:
    logger.info(f"开始解析视频: {video_path}")
    try:
        # 解析逻辑
        result = {}
        logger.info(f"视频解析完成，共生成{len(result)}个段落")
        return result
    except Exception as e:
        logger.error(f"视频解析失败: {e}")
        raise
```

## 4. 测试开发

### 4.1 测试文件结构
```
tests/
├── __init__.py
├── test_core/
│   ├── __init__.py
│   ├── test_base_parser.py
│   ├── test_video_processor.py
│   └── test_audio_processor.py
├── test_business/
│   ├── __init__.py
│   ├── test_speech_recognition.py
│   ├── test_parsers/
│   │   ├── test_scene_change_parser.py
│   │   └── test_text_length_parser.py
│   └── test_metadata_manager.py
├── test_ui/
│   ├── __init__.py
│   └── test_components/
└── test_data/
    ├── sample_video.mp4
    ├── sample_audio.wav
    └── sample_metadata.json
```

### 4.2 测试示例
```python
# tests/test_core/test_video_processor.py
import unittest
import os
from unittest.mock import patch, MagicMock
from src.core.video_processor import VideoProcessor

class TestVideoProcessor(unittest.TestCase):
    
    def setUp(self):
        """测试前准备"""
        self.processor = VideoProcessor()
        self.test_video_path = "tests/test_data/sample_video.mp4"
    
    def test_load_video_success(self):
        """测试成功加载视频"""
        with patch('cv2.VideoCapture') as mock_cap:
            mock_cap.return_value.isOpened.return_value = True
            mock_cap.return_value.get.return_value = 30.0
            
            result = self.processor.load_video(self.test_video_path)
            self.assertTrue(result)
            self.assertEqual(self.processor.video_path, self.test_video_path)
    
    def test_load_video_file_not_found(self):
        """测试加载不存在的视频文件"""
        with self.assertRaises(FileNotFoundError):
            self.processor.load_video("nonexistent.mp4")
    
    def test_extract_key_frame(self):
        """测试提取关键帧"""
        # 模拟视频已加载
        self.processor.video_path = self.test_video_path
        
        with patch.object(self.processor, '_extract_frame') as mock_extract:
            mock_extract.return_value = MagicMock()  # 模拟图像对象
            
            frame = self.processor.get_key_frame(0, 10)
            self.assertIsNotNone(frame)
            mock_extract.assert_called_once()

if __name__ == '__main__':
    unittest.main()
```

### 4.3 运行测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试文件
python -m pytest tests/test_core/test_video_processor.py

# 运行测试并生成覆盖率报告
python -m pytest tests/ --cov=src --cov-report=html
```

## 5. 调试技巧

### 5.1 日志调试
```python
# 在关键位置添加调试日志
logger.debug(f"处理帧数: {frame_count}")
logger.debug(f"当前时间戳: {timestamp}")
logger.debug(f"解析参数: {parser_config}")
```

### 5.2 断点调试
```python
# 使用pdb进行调试
import pdb
pdb.set_trace()  # 在此处设置断点

# 或使用更现代的debugpy（VS Code支持）
import debugpy
debugpy.breakpoint()
```

### 5.3 性能分析
```python
import cProfile
import pstats

def profile_function():
    # 需要分析的代码
    pass

# 性能分析
cProfile.run('profile_function()', 'profile_stats')
stats = pstats.Stats('profile_stats')
stats.sort_stats('cumulative').print_stats(10)
```

## 6. 版本控制规范

### 6.1 分支策略
- `main`: 主分支，保持稳定
- `develop`: 开发分支，集成新功能
- `feature/功能名`: 功能分支
- `bugfix/问题描述`: 修复分支

### 6.2 提交信息规范
```
类型(范围): 简短描述

详细描述（可选）

类型包括：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

示例：
feat(parser): 添加场景变化解析器
fix(ui): 修复视频播放器暂停按钮问题
docs(readme): 更新安装说明
```

## 7. 常见问题解决

### 7.1 FFmpeg相关问题
**问题**: 找不到FFmpeg
**解决**: 确保FFmpeg在系统PATH中，或在代码中指定FFmpeg路径

**问题**: 视频格式不支持
**解决**: 检查FFmpeg编译选项，确保包含所需的编解码器

### 7.2 内存问题
**问题**: 处理大视频文件时内存不足
**解决**: 
- 分块处理视频
- 及时释放不用的资源
- 使用生成器而不是列表

### 7.3 界面响应问题
**问题**: 界面在处理时卡顿
**解决**:
- 使用多线程处理耗时操作
- 定期更新进度条
- 使用异步操作

## 8. 部署准备

### 8.1 依赖整理
```bash
# 生成requirements.txt
pip freeze > requirements.txt

# 或使用pipreqs（只包含项目实际使用的包）
pip install pipreqs
pipreqs . --force
```

### 8.2 打包配置
```python
# setup.py示例
from setuptools import setup, find_packages

setup(
    name="videoreader",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "opencv-python>=4.5.0",
        "PyQt6>=6.0.0",
        "openai-whisper>=20230314",
        # 其他依赖
    ],
    entry_points={
        'console_scripts': [
            'videoreader=src.main:main',
        ],
    },
)
```

这个开发指南为VideoReader项目提供了完整的开发流程和规范，确保代码质量和开发效率。
