#!/usr/bin/env python3
"""调试您的Cloudreve服务器"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def debug_cloudreve_server():
    """调试Cloudreve服务器"""
    print("=== 调试您的Cloudreve服务器 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    print(f"服务器: {base_url}")
    print(f"用户名: {username}")
    print(f"密码: {'已配置' if password else '未配置'}")
    
    try:
        import requests
        
        # 1. 检查服务器响应
        print(f"\n1. 检查服务器基本响应")
        response = requests.get(base_url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        # 查看HTML中是否有API信息
        html = response.text
        if 'api' in html.lower():
            print("HTML中包含'api'关键字，可能有API配置信息")
            
        # 2. 尝试不同的API路径
        print(f"\n2. 测试API路径")
        api_paths = [
            "/api/v3/user/session",
            "/api/user/session", 
            "/user/session",
            "/login",
            "/api/v3/site/config"
        ]
        
        for path in api_paths:
            url = f"{base_url}{path}"
            try:
                resp = requests.get(url, timeout=5)
                print(f"{path}: {resp.status_code} - {resp.text[:50]}")
            except:
                print(f"{path}: 连接失败")
        
        # 3. 尝试登录
        print(f"\n3. 尝试登录")
        login_url = f"{base_url}/api/v3/user/session"
        
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        login_data = {
            'userName': username,
            'Password': password
        }
        
        print(f"登录URL: {login_url}")
        print(f"登录数据: {login_data}")
        
        response = session.post(login_url, json=login_data, timeout=30)
        print(f"登录响应状态: {response.status_code}")
        print(f"登录响应头: {dict(response.headers)}")
        print(f"登录响应内容: {response.text}")
        
        # 4. 如果是HTML响应，查找真正的API端点
        if 'html' in response.headers.get('content-type', '').lower():
            print(f"\n4. 服务器返回HTML，查找真正的API端点")
            
            # 检查是否有反向代理
            if 'nginx' in response.headers.get('server', '').lower():
                print("检测到nginx，可能有反向代理配置问题")
            
            # 尝试直接访问可能的API端口
            import urllib.parse
            parsed = urllib.parse.urlparse(base_url)
            
            # 尝试常见的API端口
            api_ports = [5212, 5213, 8080, 3000]
            for port in api_ports:
                if port != parsed.port:
                    api_url = f"{parsed.scheme}://{parsed.hostname}:{port}"
                    try:
                        test_resp = requests.get(f"{api_url}/api/v3/site/config", timeout=5)
                        if test_resp.status_code != 404:
                            print(f"可能的API端点: {api_url} (状态: {test_resp.status_code})")
                    except:
                        pass
        
    except ImportError:
        print("需要安装requests库")
    except Exception as e:
        print(f"调试异常: {e}")


def main():
    debug_cloudreve_server()


if __name__ == "__main__":
    main()
