"""文本长度解析器

基于文本长度来分割段落。
"""

from typing import List, Dict, Any
import re

from .base import BaseParser
from ....core.models import VideoInfo, SegmentInfo, ParserType
from ..audio_engine.models import TranscriptSegment
from ....common.exceptions import ProcessingError


class TextLengthParser(BaseParser):
    """文本长度解析器
    
    根据文本字符数或词数来分割段落，优先在句子边界分割。
    """
    
    def __init__(self):
        super().__init__(ParserType.TEXT_LENGTH)
    
    def _do_parse(self, video_info: VideoInfo, transcript: List[TranscriptSegment],
                 config: Dict[str, Any]) -> List[SegmentInfo]:
        """执行文本长度解析"""
        try:
            # 获取配置参数
            max_length = config.get('max_length', 200)
            min_length = config.get('min_length', 50)
            prefer_sentence_boundary = config.get('prefer_sentence_boundary', True)
            sentence_endings = config.get('sentence_endings', ['。', '！', '？', '.', '!', '?'])
            overlap_chars = config.get('overlap_chars', 10)
            
            self.logger.info(f"文本长度解析参数: max_length={max_length}, min_length={min_length}")
            
            # 合并所有转录文本
            full_text = ' '.join([seg.text for seg in transcript])
            
            # 创建字符到时间的映射
            char_to_time_map = self._create_char_time_mapping(transcript, full_text)
            
            # 分割文本
            text_segments = self._split_text_by_length(
                full_text, max_length, min_length, 
                prefer_sentence_boundary, sentence_endings, overlap_chars
            )
            
            # 转换为段落信息
            segments = self._create_segments_from_text(
                text_segments, char_to_time_map, transcript
            )
            
            return segments
            
        except Exception as e:
            self.logger.error(f"文本长度解析失败: {e}")
            raise ProcessingError(f"文本长度解析失败: {e}")
    
    def _create_char_time_mapping(self, transcript: List[TranscriptSegment], 
                                 full_text: str) -> Dict[int, float]:
        """创建字符位置到时间的映射"""
        char_to_time = {}
        current_pos = 0
        
        for segment in transcript:
            segment_text = segment.text
            segment_start = current_pos
            segment_end = current_pos + len(segment_text)
            
            # 为该段落的每个字符分配时间
            for i in range(len(segment_text)):
                char_pos = segment_start + i
                # 线性插值计算时间
                time_ratio = i / len(segment_text) if len(segment_text) > 0 else 0
                char_time = segment.start_time + time_ratio * segment.duration
                char_to_time[char_pos] = char_time
            
            current_pos = segment_end + 1  # +1 for space
        
        return char_to_time
    
    def _split_text_by_length(self, text: str, max_length: int, min_length: int,
                             prefer_sentence_boundary: bool, sentence_endings: List[str],
                             overlap_chars: int) -> List[Dict[str, Any]]:
        """按长度分割文本"""
        segments = []
        current_pos = 0
        
        while current_pos < len(text):
            if self.should_cancel():
                break
            
            # 确定段落结束位置
            end_pos = min(current_pos + max_length, len(text))
            
            if prefer_sentence_boundary and end_pos < len(text):
                # 寻找最近的句子边界
                best_end = end_pos
                for i in range(end_pos, max(current_pos + min_length, end_pos - 50), -1):
                    if i < len(text) and text[i] in sentence_endings:
                        best_end = i + 1
                        break
                end_pos = best_end
            
            # 提取段落文本
            segment_text = text[current_pos:end_pos].strip()
            
            if len(segment_text) >= min_length or end_pos >= len(text):
                segments.append({
                    'start_pos': current_pos,
                    'end_pos': end_pos,
                    'text': segment_text,
                    'length': len(segment_text)
                })
            
            # 移动到下一个位置（考虑重叠）
            current_pos = max(end_pos - overlap_chars, current_pos + min_length)
        
        return segments
    
    def _create_segments_from_text(self, text_segments: List[Dict[str, Any]],
                                  char_to_time_map: Dict[int, float],
                                  transcript: List[TranscriptSegment]) -> List[SegmentInfo]:
        """从文本段落创建段落信息"""
        segments = []
        
        for i, text_seg in enumerate(text_segments):
            start_pos = text_seg['start_pos']
            end_pos = text_seg['end_pos']
            text = text_seg['text']
            
            # 计算时间范围
            start_time = char_to_time_map.get(start_pos, 0.0)
            end_time = char_to_time_map.get(end_pos - 1, start_time + 30.0)
            
            # 确保时间顺序正确
            if end_time <= start_time:
                end_time = start_time + max(30.0, len(text) * 0.1)
            
            # 获取相关的转录段落
            related_transcript = self._find_transcript_segments_in_range(
                transcript, start_time, end_time
            )
            
            # 生成摘要
            summary = self._generate_summary(text)
            
            # 计算置信度
            confidence = self._calculate_confidence(related_transcript)
            
            # 创建段落信息
            segment = SegmentInfo(
                id=i,
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                text=text,
                summary=summary,
                confidence=confidence,
                key_frame_path="",
                thumbnail_path="",
                metadata={
                    'parser_type': self.parser_type.value,
                    'text_length': len(text),
                    'char_start': start_pos,
                    'char_end': end_pos
                }
            )
            
            segments.append(segment)
        
        return segments
