"""视频引擎数据模型

定义视频处理相关的数据结构。
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
import numpy as np
from pathlib import Path


@dataclass
class FrameInfo:
    """视频帧信息"""
    frame_index: int
    timestamp: float
    width: int
    height: int
    channels: int
    data: Optional[np.ndarray] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def shape(self) -> tuple:
        """帧的形状"""
        return (self.height, self.width, self.channels)
    
    @property
    def size(self) -> int:
        """帧的像素数量"""
        return self.width * self.height
    
    @property
    def aspect_ratio(self) -> float:
        """宽高比"""
        return self.width / self.height if self.height > 0 else 1.0


@dataclass
class SceneChangeInfo:
    """场景变化信息"""
    timestamp: float
    frame_index: int
    confidence: float
    change_type: str
    similarity_score: float
    previous_frame_index: int
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def frame_gap(self) -> int:
        """与前一帧的间隔"""
        return self.frame_index - self.previous_frame_index


@dataclass
class VideoAnalysisResult:
    """视频分析结果"""
    video_path: str
    total_frames: int
    duration: float
    fps: float
    scene_changes: List[SceneChangeInfo]
    key_frames: List[FrameInfo]
    analysis_time: float
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def scene_count(self) -> int:
        """场景数量"""
        return len(self.scene_changes) + 1  # 场景变化点数量 + 1
    
    @property
    def average_scene_duration(self) -> float:
        """平均场景时长"""
        if self.scene_count <= 1:
            return self.duration
        return self.duration / self.scene_count
    
    @property
    def key_frame_count(self) -> int:
        """关键帧数量"""
        return len(self.key_frames)


@dataclass
class VideoProcessingConfig:
    """视频处理配置"""
    # 帧提取配置
    extract_key_frames: bool = True
    key_frame_interval: float = 10.0  # 关键帧间隔（秒）
    frame_size: Optional[tuple] = None  # 输出帧大小
    
    # 场景检测配置
    scene_detection_enabled: bool = True
    scene_threshold: float = 0.3
    scene_min_duration: float = 2.0
    
    # 分析配置
    analysis_frame_skip: int = 5  # 分析时跳帧数
    use_gpu: bool = False
    
    # 输出配置
    output_dir: Optional[str] = None
    save_frames: bool = False
    frame_format: str = 'jpg'
    frame_quality: int = 85
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.key_frame_interval <= 0:
            return False
        
        if not 0.0 <= self.scene_threshold <= 1.0:
            return False
        
        if self.scene_min_duration < 0:
            return False
        
        if self.analysis_frame_skip < 1:
            return False
        
        if self.frame_format not in ['jpg', 'png', 'bmp']:
            return False
        
        if not 1 <= self.frame_quality <= 100:
            return False
        
        return True


@dataclass
class FrameExtractionRequest:
    """帧提取请求"""
    video_path: str
    timestamps: List[float]
    output_size: Optional[tuple] = None
    output_format: str = 'rgb'
    normalize: bool = False
    
    def validate(self) -> bool:
        """验证请求有效性"""
        if not Path(self.video_path).exists():
            return False
        
        if not self.timestamps:
            return False
        
        if any(t < 0 for t in self.timestamps):
            return False
        
        if self.output_format not in ['rgb', 'bgr', 'gray']:
            return False
        
        return True


@dataclass
class BatchFrameResult:
    """批量帧提取结果"""
    request: FrameExtractionRequest
    frames: List[np.ndarray]
    timestamps: List[float]
    success_count: int
    failed_indices: List[int]
    processing_time: float
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = len(self.request.timestamps)
        return self.success_count / total if total > 0 else 0.0


# 默认配置
DEFAULT_VIDEO_PROCESSING_CONFIG = VideoProcessingConfig(
    extract_key_frames=True,
    key_frame_interval=10.0,
    frame_size=(640, 480),
    scene_detection_enabled=True,
    scene_threshold=0.3,
    scene_min_duration=2.0,
    analysis_frame_skip=5,
    use_gpu=False,
    save_frames=False,
    frame_format='jpg',
    frame_quality=85
)


# 预设配置
FAST_PROCESSING_CONFIG = VideoProcessingConfig(
    extract_key_frames=True,
    key_frame_interval=30.0,
    frame_size=(320, 240),
    scene_detection_enabled=False,
    analysis_frame_skip=10,
    use_gpu=False,
    save_frames=False
)

HIGH_QUALITY_CONFIG = VideoProcessingConfig(
    extract_key_frames=True,
    key_frame_interval=5.0,
    frame_size=(1280, 720),
    scene_detection_enabled=True,
    scene_threshold=0.2,
    scene_min_duration=1.0,
    analysis_frame_skip=2,
    use_gpu=True,
    save_frames=True,
    frame_format='png',
    frame_quality=95
)
