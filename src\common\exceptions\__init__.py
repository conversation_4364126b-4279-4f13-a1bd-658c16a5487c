"""异常定义模块

导出所有异常类供其他模块使用。
"""

from .base import (
    VideoReaderError,
    ConfigurationError,
    ValidationError,
    ProcessingError,
    FileOperationError,
    NetworkError
)

from .custom import (
    VideoLoadError,
    AudioExtractionError,
    SpeechRecognitionError,
    ParsingError,
    ProcessCancelledError,
    UnsupportedFormatError,
    InvalidConfigError,
    StorageError,
    SearchIndexError,
    ExportError,
    UIError,
    ComponentError
)

__all__ = [
    # 基础异常
    'VideoReaderError',
    'ConfigurationError',
    'ValidationError',
    'ProcessingError',
    'FileOperationError',
    'NetworkError',

    # 自定义异常
    'VideoLoadError',
    'AudioExtractionError',
    'SpeechRecognitionError',
    'ParsingError',
    'ProcessCancelledError',
    'UnsupportedFormatError',
    'InvalidConfigError',
    'StorageError',
    'SearchIndexError',
    'ExportError',
    'UIError',
    'ComponentError'
]
