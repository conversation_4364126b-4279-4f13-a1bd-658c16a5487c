/* VideoReader 主样式表 */

/* 全局样式 */
QMainWindow {
    background-color: #f5f5f5;
    color: #333333;
    font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
    font-size: 12px;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenuBar::item:pressed {
    background-color: #bbdefb;
}

/* 菜单样式 */
QMenu {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 24px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenu::separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 4px 0;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #e0e0e0;
    padding: 4px;
}

QStatusBar QLabel {
    color: #666666;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    text-align: center;
    background-color: #f5f5f5;
    height: 20px;
}

QProgressBar::chunk {
    background-color: #4caf50;
    border-radius: 3px;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #e0e0e0;
    width: 2px;
    height: 2px;
}

QSplitter::handle:hover {
    background-color: #bdbdbd;
}

/* 按钮样式 */
QPushButton {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #f5f5f5;
    border-color: #bdbdbd;
}

QPushButton:pressed {
    background-color: #eeeeee;
}

QPushButton:disabled {
    background-color: #f5f5f5;
    color: #bdbdbd;
    border-color: #e0e0e0;
}

/* 主要按钮样式 */
QPushButton.primary {
    background-color: #1976d2;
    color: #ffffff;
    border-color: #1976d2;
}

QPushButton.primary:hover {
    background-color: #1565c0;
}

QPushButton.primary:pressed {
    background-color: #0d47a1;
}

/* 危险按钮样式 */
QPushButton.danger {
    background-color: #f44336;
    color: #ffffff;
    border-color: #f44336;
}

QPushButton.danger:hover {
    background-color: #d32f2f;
}

QPushButton.danger:pressed {
    background-color: #b71c1c;
}

/* 输入框样式 */
QLineEdit {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    background-color: #ffffff;
    selection-background-color: #bbdefb;
}

QLineEdit:focus {
    border-color: #1976d2;
    outline: none;
}

QLineEdit:disabled {
    background-color: #f5f5f5;
    color: #bdbdbd;
}

/* 文本编辑器样式 */
QTextEdit {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
    selection-background-color: #bbdefb;
    padding: 8px;
}

QTextEdit:focus {
    border-color: #1976d2;
}

/* 列表样式 */
QListWidget {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
    alternate-background-color: #f9f9f9;
    outline: none;
}

QListWidget::item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
}

QListWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QListWidget::item:hover {
    background-color: #f5f5f5;
}

/* 表格样式 */
QTableWidget {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
    gridline-color: #f0f0f0;
    selection-background-color: #e3f2fd;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QHeaderView::section {
    background-color: #f5f5f5;
    border: none;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px;
    font-weight: 500;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-bottom: none;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    border-bottom: 2px solid #1976d2;
}

QTabBar::tab:hover {
    background-color: #eeeeee;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #f5f5f5;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #f5f5f5;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 组合框样式 */
QComboBox {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    background-color: #ffffff;
    min-width: 120px;
}

QComboBox:hover {
    border-color: #bdbdbd;
}

QComboBox:focus {
    border-color: #1976d2;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(:/icons/arrow_down.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #ffffff;
    selection-background-color: #e3f2fd;
}

/* 复选框样式 */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #bdbdbd;
    border-radius: 3px;
    background-color: #ffffff;
}

QCheckBox::indicator:checked {
    background-color: #1976d2;
    border-color: #1976d2;
    image: url(:/icons/check.png);
}

QCheckBox::indicator:hover {
    border-color: #757575;
}

/* 单选按钮样式 */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #bdbdbd;
    border-radius: 8px;
    background-color: #ffffff;
}

QRadioButton::indicator:checked {
    background-color: #1976d2;
    border-color: #1976d2;
}

QRadioButton::indicator:checked::after {
    content: "";
    width: 6px;
    height: 6px;
    border-radius: 3px;
    background-color: #ffffff;
    margin: 4px;
}

/* 滑块样式 */
QSlider::groove:horizontal {
    border: 1px solid #e0e0e0;
    height: 4px;
    background-color: #f5f5f5;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background-color: #1976d2;
    border: 1px solid #1976d2;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #1565c0;
}

/* 工具提示样式 */
QToolTip {
    background-color: #424242;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 11px;
}
