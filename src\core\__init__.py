"""核心数据模型层

包含事件系统和核心数据模型。
"""

from .events import (
    EventType, Event, EventBus, EventHandler,
    get_event_bus, publish_event, subscribe_event, unsubscribe_event
)

from .models import (
    ProcessingStatus, ParserType, ExportFormat,
    VideoInfo, AudioInfo, TranscriptSegment, SegmentInfo,
    ProcessConfig, ProcessResult, SearchQuery, SearchResult,
    ApplicationState, MetadataInfo, ExportOptions
)

__all__ = [
    # 事件系统
    'EventType', 'Event', 'EventBus', 'EventHandler',
    'get_event_bus', 'publish_event', 'subscribe_event', 'unsubscribe_event',

    # 数据模型
    'ProcessingStatus', 'ParserType', 'ExportFormat',
    'VideoInfo', 'AudioInfo', 'TranscriptSegment', 'SegmentInfo',
    'ProcessConfig', 'ProcessResult', 'SearchQuery', 'SearchResult',
    'ApplicationState', 'MetadataInfo', 'ExportOptions'
]
