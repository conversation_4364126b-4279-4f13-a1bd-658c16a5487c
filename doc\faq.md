# VideoReader 常见问题解答 (FAQ)

## 目录

1. [安装和环境问题](#1-安装和环境问题)
2. [使用问题](#2-使用问题)
3. [性能问题](#3-性能问题)
4. [功能问题](#4-功能问题)
5. [错误和故障排除](#5-错误和故障排除)
6. [开发相关问题](#6-开发相关问题)

## 1. 安装和环境问题

### Q1.1: 支持哪些操作系统？
**A:** VideoReader支持以下操作系统：
- Windows 10 或更高版本
- macOS 10.14 或更高版本
- Ubuntu 18.04 或更高版本
- 其他主流Linux发行版

### Q1.2: Python版本要求是什么？
**A:** 需要Python 3.8或更高版本。推荐使用Python 3.9或3.10以获得最佳性能。

### Q1.3: 如何安装FFmpeg？
**A:** 
- **Windows**: 从[FFmpeg官网](https://ffmpeg.org/download.html)下载，解压后将bin目录添加到系统PATH
- **macOS**: 使用Homebrew: `brew install ffmpeg`
- **Linux**: 使用包管理器: `sudo apt install ffmpeg` (Ubuntu/Debian)

### Q1.4: PyQt5安装失败怎么办？
**A:** 尝试以下解决方案：
```bash
# 方案1: 升级pip
pip install --upgrade pip
pip install PyQt5

# 方案2: 使用conda
conda install pyqt

# 方案3: 使用预编译包
pip install PyQt5 --find-links https://download.qt.io/snapshots/ci/pyqt/5.15/
```

### Q1.5: 虚拟环境创建失败？
**A:** 
```bash
# 确保使用正确的Python版本
python3.9 -m venv venv

# 如果仍然失败，尝试
python -m pip install --upgrade pip
python -m pip install virtualenv
virtualenv venv
```

## 2. 使用问题

### Q2.1: 支持哪些视频格式？
**A:** 支持大多数常见视频格式：
- MP4 (推荐)
- AVI
- MOV
- MKV
- WMV
- FLV
- WebM

### Q2.2: 视频文件大小有限制吗？
**A:** 理论上没有硬性限制，但建议：
- 单个文件不超过2GB
- 时长不超过4小时
- 对于更大的文件，建议先进行分割

### Q2.3: 如何选择合适的解析器？
**A:** 根据视频类型选择：
- **场景变化分段**: 适用于教学视频、演示视频
- **文本长度分段**: 适用于讲座、访谈
- **时间固定分段**: 适用于新闻、短视频
- **静音分段**: 适用于会议录音、课程视频

### Q2.4: 语音识别准确率如何提高？
**A:** 
- 确保音频质量良好，噪音较少
- 选择正确的语言设置
- 对于中文，使用普通话效果最佳
- 避免多人同时说话的场景

### Q2.5: 处理结果可以编辑吗？
**A:** 是的，可以：
- 在GUI界面中直接编辑文本内容
- 调整段落的时间范围
- 添加或删除段落
- 修改后会自动保存到元数据文件

## 3. 性能问题

### Q3.1: 视频处理速度很慢怎么办？
**A:** 优化建议：
- 关闭其他占用CPU的程序
- 使用SSD存储视频文件
- 调整解析器参数（如降低场景变化阈值）
- 考虑使用GPU加速（如果支持）

### Q3.2: 内存使用过高？
**A:** 
- 处理完成后及时关闭视频文件
- 避免同时处理多个大文件
- 定期清理缓存文件
- 重启应用程序

### Q3.3: 程序响应缓慢？
**A:** 
- 确保系统有足够的可用内存（建议8GB+）
- 检查磁盘空间是否充足
- 更新到最新版本
- 重启计算机

## 4. 功能问题

### Q4.1: 如何导出字幕文件？
**A:** 
1. 完成视频处理后
2. 选择"文件" -> "导出"
3. 选择SRT或VTT格式
4. 指定保存位置

### Q4.2: 搜索功能不准确？
**A:** 
- 使用更具体的关键词
- 检查是否开启了大小写敏感
- 尝试使用正则表达式搜索
- 确保语音识别质量良好

### Q4.3: 缩略图不显示？
**A:** 
- 检查视频文件是否损坏
- 确保有足够的磁盘空间
- 重新生成缩略图
- 检查图片查看器设置

### Q4.4: 如何批量处理视频？
**A:** 使用CLI模式：
```bash
# 处理单个文件
python src/main.py --cli --input video.mp4

# 使用脚本批量处理
for file in *.mp4; do
    python src/main.py --cli --input "$file" --output "${file%.mp4}.txt"
done
```

## 5. 错误和故障排除

### Q5.1: "找不到FFmpeg"错误？
**A:** 
1. 确认FFmpeg已正确安装
2. 检查PATH环境变量
3. 重启终端/命令提示符
4. 尝试指定FFmpeg完整路径

### Q5.2: "视频加载失败"错误？
**A:** 
- 检查视频文件是否存在且未损坏
- 确认视频格式受支持
- 检查文件权限
- 尝试使用其他视频播放器测试文件

### Q5.3: "语音识别失败"错误？
**A:** 
- 确认视频包含音频轨道
- 检查音频格式是否支持
- 确保网络连接正常（如使用在线服务）
- 尝试降低音频质量要求

### Q5.4: 界面无法启动？
**A:** 
- 检查PyQt5是否正确安装
- 尝试使用CLI模式
- 查看错误日志
- 重新安装GUI依赖

### Q5.5: 程序崩溃怎么办？
**A:** 
1. 查看日志文件（通常在用户目录下）
2. 记录崩溃时的操作步骤
3. 收集系统信息
4. 提交Bug报告

## 6. 开发相关问题

### Q6.1: 如何添加新的解析器？
**A:** 
1. 继承BaseParser类
2. 实现parse方法
3. 在解析器工厂中注册
4. 添加相应的测试

### Q6.2: 如何自定义界面主题？
**A:** 
1. 修改`src/ui/styles/main.qss`文件
2. 使用Qt样式表语法
3. 重启应用查看效果

### Q6.3: 如何贡献代码？
**A:** 
1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request
5. 等待代码审查

### Q6.4: 如何报告Bug？
**A:** 
1. 在GitHub上创建Issue
2. 提供详细的错误描述
3. 包含复现步骤
4. 附上日志文件和系统信息

## 故障排除检查清单

当遇到问题时，请按以下顺序检查：

### 基础检查
- [ ] Python版本是否为3.8+
- [ ] 所有依赖是否正确安装
- [ ] FFmpeg是否在PATH中
- [ ] 磁盘空间是否充足
- [ ] 内存是否充足

### 文件检查
- [ ] 视频文件是否存在
- [ ] 文件格式是否支持
- [ ] 文件是否损坏
- [ ] 文件权限是否正确

### 配置检查
- [ ] 配置文件是否正确
- [ ] 语言设置是否匹配
- [ ] 解析器参数是否合理
- [ ] 输出目录是否可写

### 环境检查
- [ ] 虚拟环境是否激活
- [ ] 环境变量是否正确
- [ ] 防火墙是否阻止
- [ ] 杀毒软件是否干扰

## 获取更多帮助

如果以上FAQ没有解决您的问题，可以通过以下方式获取帮助：

1. **查看文档**: 阅读完整的用户手册和开发指南
2. **搜索Issues**: 在GitHub仓库中搜索类似问题
3. **提交Issue**: 创建新的Issue描述您的问题
4. **社区讨论**: 参与社区讨论获取帮助
5. **联系开发者**: 通过邮件联系开发团队

## 常用命令参考

```bash
# 基本使用
python src/main.py                    # 启动GUI
python src/main.py --cli              # 启动CLI
python src/main.py --help             # 查看帮助

# CLI处理
python src/main.py --cli --input video.mp4 --parser scene_change
python src/main.py --cli --input video.mp4 --export-format json

# 测试和调试
python tests/run_tests.py             # 运行测试
python src/main.py --debug            # 调试模式
python -c "import cv2; print(cv2.__version__)"  # 检查OpenCV版本
```

---

**注意**: 本FAQ会持续更新，如果您有新的问题或建议，欢迎提交Issue或Pull Request。
