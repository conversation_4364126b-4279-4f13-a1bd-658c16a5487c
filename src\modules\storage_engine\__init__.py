"""存储引擎模块

负责元数据管理、缓存管理和数据导出功能。
"""

from .interface import (
    StorageEngineInterface as StorageEngine,
    MetadataManagerInterface as MetadataManager,
    CacheManagerInterface as CacheManager,
    ExportManagerInterface as ExportManager
)
from .metadata import MetadataManager as MetadataManagerImpl
from .cache import CacheManager as CacheManagerImpl
from .export import DataExporter
from core.models import MetadataInfo, ExportFormat, ExportOptions

# 创建默认实例的工厂函数
def create_metadata_manager() -> MetadataManager:
    """创建元数据管理器实例"""
    return MetadataManagerImpl()

def create_cache_manager() -> CacheManager:
    """创建缓存管理器实例"""
    return CacheManagerImpl()

def create_data_exporter() -> DataExporter:
    """创建数据导出器实例"""
    return DataExporter()

__all__ = [
    'StorageEngine',
    'MetadataManager',
    'CacheManager',
    'ExportManager',
    'MetadataManagerImpl',
    'CacheManagerImpl',
    'DataExporter',
    'MetadataInfo',
    'ExportFormat',
    'ExportOptions',
    'create_metadata_manager',
    'create_cache_manager',
    'create_data_exporter'
]
