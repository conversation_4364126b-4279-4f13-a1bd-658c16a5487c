"""用户界面模块

负责GUI界面的显示和用户交互。
"""

from .interface import (
    UIManagerInterface as UIManager,
    UIEvent, UIEventType,
    VideoPlayerInterface as VideoPlayer,
    SegmentListInterface as SegmentList,
    TextViewerInterface as TextViewer,
    ControlPanelInterface as ControlPanel
)
try:
    from .main_window import MainWindow
    PYSIDE_AVAILABLE = True
    print("PySide6 GUI界面可用")
except ImportError as e:
    PYSIDE_AVAILABLE = False
    print(f"PySide6导入失败: {e}")
    # 创建一个简单的替代主窗口类
    class MainWindow(UIManager):
        def __init__(self):
            print("警告: PySide6不可用，使用简化的界面模式")

        def initialize(self) -> bool:
            print("简化界面初始化完成")
            return True

        def show_main_window(self) -> None:
            print("显示主窗口（简化模式）")

        def hide_main_window(self) -> None:
            pass

        def close_application(self) -> None:
            pass

        def register_event_handler(self, event_type, handler) -> None:
            pass

        def unregister_event_handler(self, event_type, handler) -> None:
            pass

        def emit_event(self, event) -> None:
            pass

        def update_progress(self, progress: float, message: str = "") -> None:
            print(f"进度: {progress:.1%} - {message}")

        def show_error(self, message: str, details: str = "") -> None:
            print(f"错误: {message}")
            if details:
                print(f"详情: {details}")

        def show_warning(self, message: str, details: str = "") -> None:
            print(f"警告: {message}")

        def show_info(self, message: str, details: str = "") -> None:
            print(f"信息: {message}")

        def ask_confirmation(self, message: str, title: str = "确认") -> bool:
            return True

        def select_file(self, title: str = "选择文件", file_filter: str = "") -> str:
            return ""

        def select_directory(self, title: str = "选择目录") -> str:
            return ""

# 创建默认实例的工厂函数
def create_main_window() -> UIManager:
    """创建主窗口实例"""
    return MainWindow()

__all__ = [
    'UIManager',
    'UIEvent',
    'UIEventType',
    'VideoPlayer',
    'SegmentList',
    'TextViewer',
    'ControlPanel',
    'MainWindow',
    'create_main_window'
]
