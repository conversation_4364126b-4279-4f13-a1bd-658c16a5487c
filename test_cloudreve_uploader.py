#!/usr/bin/env python3
"""Cloudreve上传器测试脚本

测试Cloudreve文件上传功能和ParaformerRecognizer集成。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.file_uploader import CloudreveUploader, FileUploadManager
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer
from src.common.logging import get_logger

logger = get_logger(__name__)


def test_cloudreve_connection():
    """测试Cloudreve连接"""
    print("=== 测试Cloudreve连接 ===")
    
    try:
        # 创建Cloudreve上传器
        uploader = CloudreveUploader()
        
        if uploader.is_available():
            print("✓ Cloudreve上传器可用")
            return True
        else:
            print("✗ Cloudreve上传器不可用")
            print("请检查以下环境变量配置：")
            print("- CLOUDREVE_BASE_URL: Cloudreve服务器地址")
            print("- CLOUDREVE_USERNAME: 用户名")
            print("- CLOUDREVE_PASSWORD: 密码")
            print("或者：")
            print("- CLOUDREVE_TOKEN: 访问令牌")
            return False
            
    except Exception as e:
        print(f"✗ Cloudreve连接测试失败: {e}")
        return False


def test_cloudreve_upload():
    """测试Cloudreve文件上传"""
    print("\n=== 测试Cloudreve文件上传 ===")
    
    # 检查测试文件
    test_file = "test.mp4"
    if not os.path.exists(test_file):
        print(f"✗ 测试文件不存在: {test_file}")
        print("请确保项目根目录下有test.mp4文件")
        return None
    
    try:
        # 创建Cloudreve上传器
        uploader = CloudreveUploader()
        
        if not uploader.is_available():
            print("✗ Cloudreve上传器不可用，跳过上传测试")
            return None
        
        print(f"开始上传文件: {test_file}")
        
        # 上传文件
        file_url = uploader.upload(test_file, f"test_videos/test_{int(__import__('time').time())}.mp4")
        
        print(f"✓ 文件上传成功: {file_url}")
        return file_url
        
    except Exception as e:
        print(f"✗ 文件上传失败: {e}")
        return None


def test_file_upload_manager():
    """测试文件上传管理器"""
    print("\n=== 测试文件上传管理器 ===")
    
    try:
        # 创建文件上传管理器
        manager = FileUploadManager()
        
        # 检查可用的上传器
        available_uploaders = manager.get_available_uploaders()
        print(f"可用的上传器: {available_uploaders}")
        
        if 'cloudreve' in available_uploaders:
            print("✓ Cloudreve上传器在管理器中可用")
            
            # 测试自动选择上传器
            test_file = "test.mp4"
            if os.path.exists(test_file):
                print(f"使用自动选择模式上传文件: {test_file}")
                file_url = manager.upload(test_file, uploader='auto')
                print(f"✓ 自动选择上传成功: {file_url}")
                return file_url
            else:
                print(f"✗ 测试文件不存在: {test_file}")
                return None
        else:
            print("✗ Cloudreve上传器在管理器中不可用")
            return None
            
    except Exception as e:
        print(f"✗ 文件上传管理器测试失败: {e}")
        return None


def test_paraformer_with_cloudreve():
    """测试ParaformerRecognizer使用Cloudreve上传器"""
    print("\n=== 测试ParaformerRecognizer使用Cloudreve上传器 ===")
    
    try:
        # 创建ParaformerRecognizer
        recognizer = ParaformerRecognizer(model='paraformer-v2')
        
        if not recognizer.is_available():
            print("✗ ParaformerRecognizer不可用")
            print("请检查DASHSCOPE_API_KEY环境变量")
            return None
        
        print("✓ ParaformerRecognizer可用")
        
        # 检查文件上传管理器中的Cloudreve上传器
        if recognizer.file_uploader.is_uploader_available('cloudreve'):
            print("✓ ParaformerRecognizer可以使用Cloudreve上传器")
            
            # 测试文件识别（如果有测试文件）
            test_file = "test.mp4"
            if os.path.exists(test_file):
                print(f"开始识别文件: {test_file}")
                print("注意：这将使用Cloudreve上传器上传文件，然后调用阿里云Paraformer进行识别")
                
                # 这里只是演示，实际识别需要有效的API密钥
                # segments = recognizer.recognize(test_file, language='zh')
                # print(f"✓ 识别完成，共 {len(segments)} 个段落")
                print("（由于需要有效的API密钥，此处跳过实际识别）")
                return True
            else:
                print(f"✗ 测试文件不存在: {test_file}")
                return None
        else:
            print("✗ ParaformerRecognizer无法使用Cloudreve上传器")
            return None
            
    except Exception as e:
        print(f"✗ ParaformerRecognizer测试失败: {e}")
        return None


def show_configuration_guide():
    """显示配置指南"""
    print("\n=== Cloudreve配置指南 ===")
    print("要使用Cloudreve上传器，有以下几种配置方式（按优先级排序）：")
    print()
    print("方法1：使用TOML配置文件（推荐）")
    print("运行配置向导：python scripts/create_config.py")
    print("或手动编辑配置文件：~/.videoreader/config.toml")
    print("[file_uploader.uploaders.cloudreve]")
    print("base_url = 'http://your-cloudreve-server.com:5212'")
    print("username = 'your-username'")
    print("password = 'your-password'")
    print("# 或者使用访问令牌")
    print("# token = 'your-access-token'")
    print()
    print("方法2：使用环境变量")
    print("export CLOUDREVE_BASE_URL='http://your-cloudreve-server.com'")
    print("export CLOUDREVE_USERNAME='your-username'")
    print("export CLOUDREVE_PASSWORD='your-password'")
    print("# 或者")
    print("export CLOUDREVE_TOKEN='your-access-token'")
    print()
    print("方法3：在代码中直接传递参数")
    print("uploader = CloudreveUploader(")
    print("    base_url='http://your-cloudreve-server.com',")
    print("    username='your-username',")
    print("    password='your-password'")
    print(")")
    print()
    print("Windows用户环境变量设置：")
    print("set CLOUDREVE_BASE_URL=http://your-cloudreve-server.com")
    print("set CLOUDREVE_USERNAME=your-username")
    print("set CLOUDREVE_PASSWORD=your-password")


def main():
    """主函数"""
    print("Cloudreve上传器测试")
    print("=" * 50)
    
    # 显示配置指南
    show_configuration_guide()
    
    # 测试连接
    connection_ok = test_cloudreve_connection()
    
    if connection_ok:
        # 测试上传
        test_cloudreve_upload()
        
        # 测试文件上传管理器
        test_file_upload_manager()
        
        # 测试ParaformerRecognizer集成
        test_paraformer_with_cloudreve()
    
    print("\n=== 测试完成 ===")
    print("如果遇到问题，请检查：")
    print("1. Cloudreve服务器是否正常运行")
    print("2. 网络连接是否正常")
    print("3. 认证信息是否正确")
    print("4. 是否安装了requests库: pip install requests")


if __name__ == "__main__":
    main()
