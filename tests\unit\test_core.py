"""核心模块单元测试

测试core模块的事件系统和数据模型。
"""

import pytest
from datetime import datetime
from unittest.mock import Mock

from src.core.events import EventBus, Event, EventType, EventHandler
from src.core.models import (
    VideoInfo, SegmentInfo, ProcessResult, ProcessConfig, ParserType,
    ApplicationState, SearchQuery, SearchResult, ExportOptions, ExportFormat
)


class TestEventSystem:
    """事件系统测试"""
    
    def test_event_creation(self):
        """测试事件创建"""
        event = Event(
            event_type=EventType.VIDEO_LOADED,
            data={'file_path': '/test/video.mp4'},
            source='test'
        )
        
        assert event.event_type == EventType.VIDEO_LOADED
        assert event.data['file_path'] == '/test/video.mp4'
        assert event.source == 'test'
        assert isinstance(event.timestamp, datetime)
    
    def test_event_bus_subscribe_publish(self):
        """测试事件总线订阅和发布"""
        event_bus = EventBus()
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
        
        # 订阅事件
        event_bus.subscribe(EventType.VIDEO_LOADED, event_handler)
        
        # 发布事件
        event_bus.publish(EventType.VIDEO_LOADED, {'test': 'data'})
        
        # 验证事件被接收
        assert len(received_events) == 1
        assert received_events[0].event_type == EventType.VIDEO_LOADED
        assert received_events[0].data['test'] == 'data'
    
    def test_event_bus_unsubscribe(self):
        """测试事件取消订阅"""
        event_bus = EventBus()
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
        
        # 订阅和取消订阅
        event_bus.subscribe(EventType.VIDEO_LOADED, event_handler)
        event_bus.unsubscribe(EventType.VIDEO_LOADED, event_handler)
        
        # 发布事件
        event_bus.publish(EventType.VIDEO_LOADED, {'test': 'data'})
        
        # 验证事件未被接收
        assert len(received_events) == 0
    
    def test_event_bus_multiple_subscribers(self):
        """测试多个订阅者"""
        event_bus = EventBus()
        received_events1 = []
        received_events2 = []
        
        def handler1(event):
            received_events1.append(event)
        
        def handler2(event):
            received_events2.append(event)
        
        # 订阅同一事件
        event_bus.subscribe(EventType.VIDEO_LOADED, handler1)
        event_bus.subscribe(EventType.VIDEO_LOADED, handler2)
        
        # 发布事件
        event_bus.publish(EventType.VIDEO_LOADED, {'test': 'data'})
        
        # 验证两个处理器都收到事件
        assert len(received_events1) == 1
        assert len(received_events2) == 1
    
    def test_event_handler_base_class(self):
        """测试事件处理器基类"""
        event_bus = EventBus()
        
        class TestHandler(EventHandler):
            def __init__(self, event_bus):
                super().__init__(event_bus)
                self.received_events = []
            
            def subscribe_to_events(self):
                self._subscribe(EventType.VIDEO_LOADED)
            
            def on_video_loaded(self, event):
                self.received_events.append(event)
        
        handler = TestHandler(event_bus)
        handler.subscribe_to_events()
        
        # 发布事件
        event_bus.publish(EventType.VIDEO_LOADED, {'test': 'data'})
        
        # 验证处理器收到事件
        assert len(handler.received_events) == 1
        assert handler.received_events[0].event_type == EventType.VIDEO_LOADED
    
    def test_event_history(self):
        """测试事件历史"""
        event_bus = EventBus()
        
        # 发布多个事件
        event_bus.publish(EventType.VIDEO_LOADED, {'file': 'video1.mp4'})
        event_bus.publish(EventType.VIDEO_PROCESSING_STARTED, {})
        event_bus.publish(EventType.VIDEO_PROCESSING_COMPLETED, {})
        
        # 获取所有历史
        history = event_bus.get_event_history()
        assert len(history) == 3
        
        # 获取特定类型的历史
        video_events = event_bus.get_event_history(EventType.VIDEO_LOADED)
        assert len(video_events) == 1
        assert video_events[0].data['file'] == 'video1.mp4'
        
        # 获取限制数量的历史
        limited_history = event_bus.get_event_history(limit=2)
        assert len(limited_history) == 2


class TestDataModels:
    """数据模型测试"""
    
    def test_video_info_model(self):
        """测试视频信息模型"""
        video_info = VideoInfo(
            file_path="/test/video.mp4",
            file_name="video.mp4",
            file_size=1024 * 1024,
            duration=120.0,
            width=1920,
            height=1080,
            fps=30.0,
            codec="h264",
            has_audio=True,
            audio_codec="aac"
        )
        
        assert video_info.file_name == "video.mp4"
        assert video_info.duration == 120.0
        assert video_info.width == 1920
        assert video_info.height == 1080
        assert video_info.has_audio == True
    
    def test_segment_info_model(self):
        """测试段落信息模型"""
        segment = SegmentInfo(
            id=1,
            start_time=10.0,
            end_time=30.0,
            text="这是一个测试段落",
            confidence=0.95
        )
        
        assert segment.id == 1
        assert segment.duration == 20.0  # end_time - start_time
        assert segment.word_count == 4  # "这是一个测试段落" 分词后的数量
        assert segment.confidence == 0.95
    
    def test_process_config_validation(self):
        """测试处理配置验证"""
        # 有效配置
        valid_config = ProcessConfig(
            parser_type=ParserType.SCENE_CHANGE,
            parser_config={"threshold": 0.3}
        )
        assert valid_config.validate() == True
        
        # 无效配置 - 缺少必需参数
        invalid_config = ProcessConfig(
            parser_type=ParserType.SCENE_CHANGE,
            parser_config={}  # 缺少threshold
        )
        assert invalid_config.validate() == False
    
    def test_process_result_properties(self, sample_video_info, sample_segments):
        """测试处理结果属性"""
        result = ProcessResult(
            video_info=sample_video_info,
            segments=sample_segments,
            parser_type=ParserType.SCENE_CHANGE,
            parser_config={"threshold": 0.3},
            processing_time=45.5
        )
        
        assert result.total_duration == sample_video_info.duration
        assert result.total_segments == len(sample_segments)
        assert result.total_text_length > 0
        assert result.average_segment_duration > 0
    
    def test_search_query_validation(self):
        """测试搜索查询验证"""
        # 有效查询
        valid_query = SearchQuery(
            query="测试关键词",
            time_start=10.0,
            time_end=60.0
        )
        assert valid_query.validate() == True
        
        # 无效查询 - 空查询
        invalid_query1 = SearchQuery(query="")
        assert invalid_query1.validate() == False
        
        # 无效查询 - 时间范围错误
        invalid_query2 = SearchQuery(
            query="测试",
            time_start=60.0,
            time_end=10.0  # 结束时间小于开始时间
        )
        assert invalid_query2.validate() == False
    
    def test_application_state(self, sample_video_info, sample_process_result):
        """测试应用状态"""
        state = ApplicationState()
        
        # 测试初始状态
        assert state.current_video is None
        assert state.current_process_result is None
        assert len(state.recent_files) == 0
        
        # 测试设置状态
        state.current_video = sample_video_info
        state.current_process_result = sample_process_result
        
        assert state.current_video == sample_video_info
        assert state.current_process_result == sample_process_result
        
        # 测试最近文件
        state.add_recent_file("/test/video1.mp4")
        state.add_recent_file("/test/video2.mp4")
        state.add_recent_file("/test/video1.mp4")  # 重复文件
        
        assert len(state.recent_files) == 2
        assert state.recent_files[0] == "/test/video1.mp4"  # 最新的在前面
        assert state.recent_files[1] == "/test/video2.mp4"
        
        # 测试根据时间查找段落
        segment = state.find_segment_by_time(45.0)  # 在第二个段落范围内
        assert segment is not None
        assert segment.id == 1
        
        # 测试重置状态
        state.reset()
        assert state.current_video is None
        assert state.current_process_result is None
    
    def test_export_options_validation(self):
        """测试导出选项验证"""
        # 有效选项
        valid_options = ExportOptions(
            format=ExportFormat.TXT,
            output_path="/test/output.txt",
            include_timestamps=True
        )
        assert valid_options.validate() == True
        
        # 无效选项 - 空输出路径
        invalid_options1 = ExportOptions(
            format=ExportFormat.TXT,
            output_path=""
        )
        assert invalid_options1.validate() == False
        
        # 无效选项 - 错误的段落范围
        invalid_options2 = ExportOptions(
            format=ExportFormat.TXT,
            output_path="/test/output.txt",
            segment_range=(10, 5)  # 开始大于结束
        )
        assert invalid_options2.validate() == False


class TestModelIntegration:
    """模型集成测试"""
    
    def test_video_processing_workflow(self, sample_video_info):
        """测试视频处理工作流"""
        # 创建处理配置
        config = ProcessConfig(
            parser_type=ParserType.SCENE_CHANGE,
            parser_config={"threshold": 0.3},
            language="zh",
            speech_engine="whisper"
        )
        
        # 创建段落
        segments = [
            SegmentInfo(
                id=0,
                start_time=0.0,
                end_time=30.0,
                text="第一个段落",
                confidence=0.95
            ),
            SegmentInfo(
                id=1,
                start_time=30.0,
                end_time=60.0,
                text="第二个段落",
                confidence=0.92
            )
        ]
        
        # 创建处理结果
        result = ProcessResult(
            video_info=sample_video_info,
            segments=segments,
            parser_type=config.parser_type,
            parser_config=config.parser_config,
            processing_time=30.0
        )
        
        # 验证结果
        assert result.total_segments == 2
        assert result.total_duration == sample_video_info.duration
        assert result.average_segment_duration == 30.0
    
    def test_search_workflow(self, sample_segments):
        """测试搜索工作流"""
        # 创建搜索查询
        query = SearchQuery(
            query="测试",
            max_results=10
        )
        
        # 模拟搜索结果
        matching_segments = [seg for seg in sample_segments if "测试" in seg.text]
        
        search_results = []
        for segment in matching_segments:
            result = SearchResult(
                segment=segment,
                matches=[],
                relevance_score=0.8
            )
            search_results.append(result)
        
        # 验证搜索结果
        assert len(search_results) > 0
        for result in search_results:
            assert "测试" in result.segment.text
            assert result.relevance_score > 0


@pytest.mark.unit
class TestEventIntegration:
    """事件系统集成测试"""
    
    def test_complete_event_workflow(self):
        """测试完整的事件工作流"""
        event_bus = EventBus()
        workflow_events = []
        
        def workflow_handler(event):
            workflow_events.append(event.event_type)
        
        # 订阅所有相关事件
        events_to_track = [
            EventType.VIDEO_LOADED,
            EventType.VIDEO_PROCESSING_STARTED,
            EventType.VIDEO_PROCESSING_PROGRESS,
            EventType.VIDEO_PROCESSING_COMPLETED
        ]
        
        for event_type in events_to_track:
            event_bus.subscribe(event_type, workflow_handler)
        
        # 模拟完整工作流
        event_bus.publish(EventType.VIDEO_LOADED, {'file': 'test.mp4'})
        event_bus.publish(EventType.VIDEO_PROCESSING_STARTED, {})
        event_bus.publish(EventType.VIDEO_PROCESSING_PROGRESS, {'progress': 0.5})
        event_bus.publish(EventType.VIDEO_PROCESSING_COMPLETED, {})
        
        # 验证事件顺序
        expected_order = [
            EventType.VIDEO_LOADED,
            EventType.VIDEO_PROCESSING_STARTED,
            EventType.VIDEO_PROCESSING_PROGRESS,
            EventType.VIDEO_PROCESSING_COMPLETED
        ]
        
        assert workflow_events == expected_order
