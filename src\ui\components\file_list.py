"""文件列表组件

显示加载的视频文件和元信息文件，用不同背景色区分处理状态。
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QGroupBox, QMenu, QMessageBox
)
from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QColor, QBrush
from pathlib import Path
from typing import List, Dict, Optional
from enum import Enum

try:
    from ...common.logging import get_logger
except ImportError:
    try:
        from src.common.logging import get_logger
    except ImportError:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from common.logging import get_logger


class FileType(Enum):
    """文件类型枚举"""
    VIDEO = "video"
    METADATA = "metadata"


class FileStatus(Enum):
    """文件状态枚举"""
    UNPROCESSED = "unprocessed"  # 未处理的视频文件
    PROCESSED = "processed"      # 已处理的视频文件
    METADATA_ONLY = "metadata_only"  # 仅元信息文件


class FileItem:
    """文件项数据类"""
    
    def __init__(self, file_path: str, file_type: FileType, status: FileStatus):
        self.file_path = file_path
        self.file_type = file_type
        self.status = status
        self.name = Path(file_path).name
        self.metadata_path = None  # 关联的元信息文件路径
        
    def __str__(self):
        return f"FileItem({self.name}, {self.file_type.value}, {self.status.value})"


class FileListWidget(QWidget):
    """文件列表组件"""
    
    # 信号定义
    file_selected = Signal(str)  # 文件被选中
    file_double_clicked = Signal(str)  # 文件被双击
    process_requested = Signal(str)  # 请求处理文件
    remove_requested = Signal(str)  # 请求移除文件
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 文件数据
        self.file_items: Dict[str, FileItem] = {}  # 文件路径 -> FileItem
        self.current_selected_file = None
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()
        
        self.logger.info("文件列表组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 文件列表组
        file_group = QGroupBox("文件列表")
        file_layout = QVBoxLayout(file_group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_video_button = QPushButton("添加视频")
        self.add_video_button.clicked.connect(self._on_add_video_clicked)
        toolbar_layout.addWidget(self.add_video_button)
        
        self.add_metadata_button = QPushButton("添加元信息")
        self.add_metadata_button.clicked.connect(self._on_add_metadata_clicked)
        toolbar_layout.addWidget(self.add_metadata_button)
        
        toolbar_layout.addStretch()
        
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self._on_clear_clicked)
        toolbar_layout.addWidget(self.clear_button)
        
        file_layout.addLayout(toolbar_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.file_list.customContextMenuRequested.connect(self._show_context_menu)
        self.file_list.itemSelectionChanged.connect(self._on_selection_changed)
        self.file_list.itemDoubleClicked.connect(self._on_item_double_clicked)
        file_layout.addWidget(self.file_list)
        
        layout.addWidget(file_group)
    
    def _connect_signals(self):
        """连接信号和槽"""
        pass
    
    def _on_add_video_clicked(self):
        """添加视频按钮点击处理"""
        from PySide6.QtWidgets import QFileDialog
        
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "选择视频文件",
            "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;所有文件 (*)"
        )
        
        for file_path in file_paths:
            self.add_video_file(file_path)
    
    def _on_add_metadata_clicked(self):
        """添加元信息按钮点击处理"""
        from PySide6.QtWidgets import QFileDialog
        
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "选择元信息文件",
            "",
            "JSON文件 (*.json);;所有文件 (*)"
        )
        
        for file_path in file_paths:
            self.add_metadata_file(file_path)
    
    def _on_clear_clicked(self):
        """清空按钮点击处理"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有文件吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.clear_files()
    
    def _on_selection_changed(self):
        """选择改变处理"""
        current_item = self.file_list.currentItem()
        if current_item:
            file_path = current_item.data(Qt.UserRole)
            self.current_selected_file = file_path
            self.file_selected.emit(file_path)
        else:
            self.current_selected_file = None
    
    def _on_item_double_clicked(self, item: QListWidgetItem):
        """列表项双击处理"""
        file_path = item.data(Qt.UserRole)
        if file_path:
            self.file_double_clicked.emit(file_path)
    
    def _show_context_menu(self, position):
        """显示右键菜单"""
        item = self.file_list.itemAt(position)
        if not item:
            return
        
        file_path = item.data(Qt.UserRole)
        file_item = self.file_items.get(file_path)
        if not file_item:
            return
        
        menu = QMenu(self)
        
        # 根据文件类型和状态添加菜单项
        if file_item.file_type == FileType.VIDEO:
            if file_item.status == FileStatus.UNPROCESSED:
                process_action = menu.addAction("处理视频")
                process_action.triggered.connect(lambda: self.process_requested.emit(file_path))
            
            open_action = menu.addAction("打开文件位置")
            open_action.triggered.connect(lambda: self._open_file_location(file_path))
        
        elif file_item.file_type == FileType.METADATA:
            load_action = menu.addAction("加载元信息")
            load_action.triggered.connect(lambda: self.file_double_clicked.emit(file_path))
            
            open_action = menu.addAction("打开文件位置")
            open_action.triggered.connect(lambda: self._open_file_location(file_path))
        
        menu.addSeparator()
        
        remove_action = menu.addAction("移除")
        remove_action.triggered.connect(lambda: self.remove_file(file_path))
        
        menu.exec(self.file_list.mapToGlobal(position))
    
    def _open_file_location(self, file_path: str):
        """打开文件位置"""
        import subprocess
        import platform
        
        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", "/select,", file_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", "-R", file_path])
            else:  # Linux
                subprocess.run(["xdg-open", str(Path(file_path).parent)])
        except Exception as e:
            self.logger.error(f"打开文件位置失败: {e}")
    

    
    def _create_list_item(self, file_item: FileItem) -> QListWidgetItem:
        """创建列表项"""
        item = QListWidgetItem(file_item.name)
        item.setData(Qt.UserRole, file_item.file_path)
        
        # 设置图标和背景色
        if file_item.file_type == FileType.VIDEO:
            if file_item.status == FileStatus.UNPROCESSED:
                # 未处理的视频文件 - 浅红色背景
                item.setBackground(QBrush(QColor(255, 240, 240)))
                item.setToolTip("未处理的视频文件")
            elif file_item.status == FileStatus.PROCESSED:
                # 已处理的视频文件 - 浅绿色背景
                item.setBackground(QBrush(QColor(240, 255, 240)))
                item.setToolTip("已处理的视频文件")
        elif file_item.file_type == FileType.METADATA:
            # 元信息文件 - 浅蓝色背景
            item.setBackground(QBrush(QColor(240, 248, 255)))
            item.setToolTip("元信息文件")
        
        return item
    
    def add_video_file(self, file_path: str, status: FileStatus = FileStatus.UNPROCESSED):
        """添加视频文件"""
        if file_path in self.file_items:
            self.logger.warning(f"文件已存在: {file_path}")
            return
        
        file_item = FileItem(file_path, FileType.VIDEO, status)
        self.file_items[file_path] = file_item
        
        # 添加到列表
        list_item = self._create_list_item(file_item)
        self.file_list.addItem(list_item)
        
        self.logger.info(f"添加视频文件: {file_item.name}")
    
    def add_metadata_file(self, file_path: str):
        """添加元信息文件"""
        if file_path in self.file_items:
            self.logger.warning(f"文件已存在: {file_path}")
            return
        
        file_item = FileItem(file_path, FileType.METADATA, FileStatus.METADATA_ONLY)
        self.file_items[file_path] = file_item
        
        # 添加到列表
        list_item = self._create_list_item(file_item)
        self.file_list.addItem(list_item)
        
        self.logger.info(f"添加元信息文件: {file_item.name}")
    
    def remove_file(self, file_path: str):
        """移除文件"""
        if file_path not in self.file_items:
            return
        
        # 从数据中移除
        file_item = self.file_items.pop(file_path)
        
        # 从列表中移除
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.data(Qt.UserRole) == file_path:
                self.file_list.takeItem(i)
                break
        
        self.logger.info(f"移除文件: {file_item.name}")
        self.remove_requested.emit(file_path)
    
    def clear_files(self):
        """清空所有文件"""
        self.file_items.clear()
        self.file_list.clear()
        self.current_selected_file = None
        self.logger.info("清空所有文件")
    
    def update_file_status(self, file_path: str, status: FileStatus, metadata_path: str = None):
        """更新文件状态"""
        file_item = self.file_items.get(file_path)
        if not file_item:
            return
        
        file_item.status = status
        if metadata_path:
            file_item.metadata_path = metadata_path
        
        # 更新列表项显示
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.data(Qt.UserRole) == file_path:
                # 重新创建列表项以更新背景色
                new_item = self._create_list_item(file_item)
                self.file_list.takeItem(i)
                self.file_list.insertItem(i, new_item)
                break
        

        
        self.logger.info(f"更新文件状态: {file_item.name} -> {status.value}")
    
    def get_selected_file(self) -> Optional[str]:
        """获取当前选中的文件"""
        return self.current_selected_file
    
    def get_all_files(self) -> List[FileItem]:
        """获取所有文件"""
        return list(self.file_items.values())
    
    def get_video_files(self, status: FileStatus = None) -> List[FileItem]:
        """获取视频文件"""
        files = [item for item in self.file_items.values() if item.file_type == FileType.VIDEO]
        if status:
            files = [item for item in files if item.status == status]
        return files
    
    def get_metadata_files(self) -> List[FileItem]:
        """获取元信息文件"""
        return [item for item in self.file_items.values() if item.file_type == FileType.METADATA]
