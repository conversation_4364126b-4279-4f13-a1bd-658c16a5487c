#!/usr/bin/env python3
"""VideoReader 安装脚本

自动化安装VideoReader项目的依赖和配置。
"""

import sys
import os
import subprocess
import platform
from pathlib import Path

def run_command(command, description="", check=True):
    """运行命令并处理结果"""
    print(f"🔄 {description}")
    print(f"   命令: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=check,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - 成功")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()}")
        else:
            print(f"❌ {description} - 失败")
            if result.stderr.strip():
                print(f"   错误: {result.stderr.strip()}")
        
        return result.returncode == 0
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - 失败: {e}")
        return False
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_pip():
    """检查pip是否可用"""
    return run_command("pip --version", "检查pip", check=False)

def upgrade_pip():
    """升级pip"""
    return run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "升级pip"
    )

def install_requirements(requirements_file):
    """安装依赖"""
    if not Path(requirements_file).exists():
        print(f"❌ 依赖文件不存在: {requirements_file}")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r {requirements_file}",
        f"安装依赖 ({requirements_file})"
    )

def check_optional_dependencies():
    """检查可选依赖"""
    optional_deps = {
        "PyQt5": "GUI支持",
        "opencv-python": "视频处理",
        "whisper": "语音识别",
        "ffmpeg-python": "多媒体处理"
    }
    
    print("\n🔍 检查可选依赖:")
    for package, description in optional_deps.items():
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package} ({description}) - 已安装")
        except ImportError:
            print(f"⚠️  {package} ({description}) - 未安装")

def setup_development_environment():
    """设置开发环境"""
    print("\n🛠️  设置开发环境:")
    
    # 安装开发依赖
    if Path("requirements-dev.txt").exists():
        if install_requirements("requirements-dev.txt"):
            print("✅ 开发依赖安装完成")
        else:
            print("❌ 开发依赖安装失败")
            return False
    
    # 安装pre-commit钩子
    if run_command("pre-commit install", "安装pre-commit钩子", check=False):
        print("✅ pre-commit钩子安装完成")
    else:
        print("⚠️  pre-commit钩子安装失败（可选）")
    
    return True

def create_config_directory():
    """创建配置目录"""
    config_dir = Path.home() / ".videoreader"
    
    if not config_dir.exists():
        try:
            config_dir.mkdir(parents=True)
            print(f"✅ 创建配置目录: {config_dir}")
        except Exception as e:
            print(f"❌ 创建配置目录失败: {e}")
            return False
    else:
        print(f"✅ 配置目录已存在: {config_dir}")
    
    # 创建子目录
    subdirs = ["cache", "logs", "metadata", "temp"]
    for subdir in subdirs:
        subdir_path = config_dir / subdir
        if not subdir_path.exists():
            try:
                subdir_path.mkdir()
                print(f"✅ 创建子目录: {subdir_path}")
            except Exception as e:
                print(f"❌ 创建子目录失败 {subdir}: {e}")
    
    return True

def check_system_dependencies():
    """检查系统依赖"""
    print("\n🔍 检查系统依赖:")
    
    system_deps = {
        "ffmpeg": "多媒体处理",
        "git": "版本控制"
    }
    
    for command, description in system_deps.items():
        if run_command(f"{command} --version", f"检查{command} ({description})", check=False):
            print(f"✅ {command} 可用")
        else:
            print(f"⚠️  {command} 不可用 - {description}")

def main():
    """主安装函数"""
    print("🚀 VideoReader 安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查pip
    if not check_pip():
        print("❌ pip不可用，请先安装pip")
        return 1
    
    # 升级pip
    if not upgrade_pip():
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装基础依赖
    print("\n📦 安装基础依赖:")
    if not install_requirements("requirements.txt"):
        print("❌ 基础依赖安装失败")
        return 1
    
    # 检查可选依赖
    check_optional_dependencies()
    
    # 询问是否安装开发环境
    if len(sys.argv) > 1 and "--dev" in sys.argv:
        if not setup_development_environment():
            print("❌ 开发环境设置失败")
            return 1
    
    # 创建配置目录
    if not create_config_directory():
        print("❌ 配置目录创建失败")
        return 1
    
    # 检查系统依赖
    check_system_dependencies()
    
    # 验证安装
    print("\n🔍 验证安装:")
    project_root = Path(__file__).parent.parent
    verify_script = project_root / "scripts" / "verify_project.py"
    
    if verify_script.exists():
        if run_command(f"{sys.executable} {verify_script}", "验证项目结构", check=False):
            print("✅ 项目验证通过")
        else:
            print("⚠️  项目验证失败")
    
    # 安装完成
    print("\n" + "=" * 50)
    print("🎉 VideoReader 安装完成！")
    print("\n📋 下一步操作:")
    print("1. 运行测试: python tests/run_tests.py")
    print("2. 启动GUI: python src/main.py")
    print("3. 启动CLI: python src/main.py --cli")
    print("4. 查看帮助: python src/main.py --help")
    
    if "--dev" in sys.argv:
        print("\n🛠️  开发环境提示:")
        print("- 代码格式化: black src/")
        print("- 代码检查: flake8 src/")
        print("- 类型检查: mypy src/")
        print("- 运行测试: pytest tests/")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 安装过程中发生异常: {e}")
        sys.exit(1)
