"""解析引擎数据模型

定义解析相关的数据结构。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from enum import Enum

from ....core.models import ParserType


@dataclass
class ParserConfig:
    """解析器配置"""
    parser_type: ParserType
    config: Dict[str, Any] = field(default_factory=dict)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        self.config[key] = value
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.parser_type == ParserType.SCENE_CHANGE:
            return self._validate_scene_change_config()
        elif self.parser_type == ParserType.TEXT_LENGTH:
            return self._validate_text_length_config()
        elif self.parser_type == ParserType.TIME_FIXED:
            return self._validate_time_fixed_config()
        elif self.parser_type == ParserType.SILENCE_BASED:
            return self._validate_silence_based_config()
        return False
    
    def _validate_scene_change_config(self) -> bool:
        """验证场景变化解析器配置"""
        threshold = self.get('threshold', 0.3)
        min_duration = self.get('min_duration', 2.0)
        
        return (isinstance(threshold, (int, float)) and 0.0 <= threshold <= 1.0 and
                isinstance(min_duration, (int, float)) and min_duration > 0)
    
    def _validate_text_length_config(self) -> bool:
        """验证文本长度解析器配置"""
        max_length = self.get('max_length', 200)
        min_length = self.get('min_length', 50)
        
        return (isinstance(max_length, int) and max_length > 0 and
                isinstance(min_length, int) and min_length > 0 and
                min_length <= max_length)
    
    def _validate_time_fixed_config(self) -> bool:
        """验证时间固定解析器配置"""
        duration = self.get('duration', 30.0)
        overlap = self.get('overlap', 0.0)
        
        return (isinstance(duration, (int, float)) and duration > 0 and
                isinstance(overlap, (int, float)) and 0 <= overlap < duration)
    
    def _validate_silence_based_config(self) -> bool:
        """验证静音分段解析器配置"""
        silence_threshold = self.get('silence_threshold', -40.0)
        min_silence_duration = self.get('min_silence_duration', 1.0)
        min_segment_duration = self.get('min_segment_duration', 5.0)
        
        return (isinstance(silence_threshold, (int, float)) and
                isinstance(min_silence_duration, (int, float)) and min_silence_duration > 0 and
                isinstance(min_segment_duration, (int, float)) and min_segment_duration > 0)


@dataclass
class ParseResult:
    """解析结果"""
    segments: List['SegmentInfo']
    parser_type: ParserType
    config: ParserConfig
    processing_time: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def total_segments(self) -> int:
        """总段落数"""
        return len(self.segments)
    
    @property
    def total_duration(self) -> float:
        """总时长"""
        if not self.segments:
            return 0.0
        return max(seg.end_time for seg in self.segments)
    
    @property
    def average_segment_duration(self) -> float:
        """平均段落时长"""
        if not self.segments:
            return 0.0
        return sum(seg.duration for seg in self.segments) / len(self.segments)


@dataclass
class SceneChangePoint:
    """场景变化点"""
    timestamp: float
    confidence: float
    frame_index: int
    similarity_score: float = 0.0
    
    def __str__(self) -> str:
        return f"SceneChange(t={self.timestamp:.2f}s, conf={self.confidence:.3f})"


@dataclass
class TextSegmentInfo:
    """文本段落信息"""
    start_index: int
    end_index: int
    text: str
    word_count: int
    char_count: int
    
    @property
    def length(self) -> int:
        """段落长度（字符数）"""
        return self.char_count


@dataclass
class TimeSegmentInfo:
    """时间段落信息"""
    start_time: float
    end_time: float
    duration: float
    overlap_start: float = 0.0
    overlap_end: float = 0.0
    
    @property
    def effective_duration(self) -> float:
        """有效时长（去除重叠部分）"""
        return self.duration - self.overlap_start - self.overlap_end


@dataclass
class SilenceSegmentInfo:
    """静音段落信息"""
    start_time: float
    end_time: float
    duration: float
    confidence: float
    threshold_db: float
    
    def __str__(self) -> str:
        return f"Silence({self.start_time:.2f}s-{self.end_time:.2f}s, {self.duration:.2f}s)"


# 默认配置
DEFAULT_SCENE_CHANGE_CONFIG = {
    'threshold': 0.3,           # 场景变化阈值
    'min_duration': 2.0,        # 最小段落时长（秒）
    'max_duration': 300.0,      # 最大段落时长（秒）
    'frame_skip': 1,            # 帧跳跃间隔
    'use_histogram': True,      # 是否使用直方图比较
    'use_edge_detection': False # 是否使用边缘检测
}

DEFAULT_TEXT_LENGTH_CONFIG = {
    'max_length': 200,          # 最大字符数
    'min_length': 50,           # 最小字符数
    'prefer_sentence_boundary': True,  # 优先在句子边界分割
    'sentence_endings': ['。', '！', '？', '.', '!', '?'],  # 句子结束符
    'overlap_chars': 10         # 重叠字符数
}

DEFAULT_TIME_FIXED_CONFIG = {
    'duration': 30.0,           # 固定时长（秒）
    'overlap': 0.0,             # 重叠时长（秒）
    'align_to_speech': True,    # 是否对齐到语音边界
    'min_speech_ratio': 0.3     # 最小语音比例
}

DEFAULT_SILENCE_BASED_CONFIG = {
    'silence_threshold': -40.0,     # 静音阈值（dB）
    'min_silence_duration': 1.0,    # 最小静音时长（秒）
    'min_segment_duration': 5.0,    # 最小段落时长（秒）
    'max_segment_duration': 300.0,  # 最大段落时长（秒）
    'merge_short_segments': True,   # 是否合并短段落
    'silence_padding': 0.1          # 静音填充时长（秒）
}

# 配置映射
DEFAULT_CONFIGS = {
    ParserType.SCENE_CHANGE: DEFAULT_SCENE_CHANGE_CONFIG,
    ParserType.TEXT_LENGTH: DEFAULT_TEXT_LENGTH_CONFIG,
    ParserType.TIME_FIXED: DEFAULT_TIME_FIXED_CONFIG,
    ParserType.SILENCE_BASED: DEFAULT_SILENCE_BASED_CONFIG
}
