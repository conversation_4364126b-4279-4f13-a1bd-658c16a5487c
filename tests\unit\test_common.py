"""通用模块单元测试

测试common模块的各个组件功能。
"""

import pytest
import json
import tempfile
from pathlib import Path
from datetime import datetime

from src.common.config import ConfigManager, get_config, set_config
from src.common.logging import get_logger
from src.common.utils import (
    format_duration, parse_time_string, ensure_dir, 
    get_file_hash, ProgressTracker, Cache
)
from src.common.exceptions import (
    VideoReaderError, ConfigurationError, ValidationError
)


class TestConfigManager:
    """配置管理器测试"""
    
    def test_config_manager_initialization(self, temp_dir):
        """测试配置管理器初始化"""
        config_file = temp_dir / "test_config.json"
        manager = ConfigManager(config_file)
        
        assert manager.config_file == config_file
        assert config_file.exists()  # 应该创建默认配置文件
    
    def test_get_set_config(self, temp_dir):
        """测试配置的获取和设置"""
        config_file = temp_dir / "test_config.json"
        manager = ConfigManager(config_file)
        
        # 测试设置和获取
        manager.set("test.key", "test_value")
        assert manager.get("test.key") == "test_value"
        
        # 测试默认值
        assert manager.get("nonexistent.key", "default") == "default"
        
        # 测试嵌套键
        manager.set("nested.deep.key", 42)
        assert manager.get("nested.deep.key") == 42
    
    def test_config_persistence(self, temp_dir):
        """测试配置持久化"""
        config_file = temp_dir / "test_config.json"
        
        # 创建第一个管理器并设置值
        manager1 = ConfigManager(config_file)
        manager1.set("persistent.key", "persistent_value")
        manager1.save()
        
        # 创建第二个管理器并验证值
        manager2 = ConfigManager(config_file)
        assert manager2.get("persistent.key") == "persistent_value"
    
    def test_config_validation(self, temp_dir):
        """测试配置验证"""
        config_file = temp_dir / "test_config.json"
        manager = ConfigManager(config_file)
        
        # 验证应该通过
        assert manager.validate() == True


class TestLogging:
    """日志系统测试"""
    
    def test_logger_creation(self):
        """测试日志器创建"""
        logger = get_logger("test_logger")
        assert logger.name == "test_logger"
    
    def test_logger_levels(self):
        """测试日志级别"""
        logger = get_logger("test_levels")
        
        # 测试不同级别的日志
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")
        
        # 这里主要测试不会抛出异常


class TestUtils:
    """工具函数测试"""
    
    def test_format_duration(self):
        """测试时长格式化"""
        assert format_duration(30) == "30.0秒"
        assert format_duration(90) == "1分30.0秒"
        assert format_duration(3661) == "1小时1分1.0秒"
    
    def test_parse_time_string(self):
        """测试时间字符串解析"""
        assert parse_time_string("30") == 30.0
        assert parse_time_string("1:30") == 90.0
        assert parse_time_string("1:01:30") == 3690.0
        assert parse_time_string("1h30m45s") == 5445.0
    
    def test_ensure_dir(self, temp_dir):
        """测试目录创建"""
        test_dir = temp_dir / "test" / "nested" / "dir"
        result = ensure_dir(test_dir)
        
        assert result == test_dir
        assert test_dir.exists()
        assert test_dir.is_dir()
    
    def test_get_file_hash(self, temp_dir):
        """测试文件哈希计算"""
        test_file = temp_dir / "test.txt"
        test_content = "Hello, World!"
        test_file.write_text(test_content, encoding='utf-8')
        
        hash_value = get_file_hash(test_file)
        assert len(hash_value) == 32  # MD5哈希长度
        
        # 相同内容应该产生相同哈希
        hash_value2 = get_file_hash(test_file)
        assert hash_value == hash_value2
    
    def test_progress_tracker(self):
        """测试进度跟踪器"""
        progress_values = []
        
        def callback(progress, message):
            progress_values.append((progress, message))
        
        tracker = ProgressTracker(total=100, callback=callback)
        
        tracker.update(25, "25% complete")
        tracker.update(25, "50% complete")
        tracker.set_progress(75, "75% complete")
        tracker.update(25, "100% complete")
        
        assert len(progress_values) == 4
        assert progress_values[0] == (0.25, "25% complete")
        assert progress_values[1] == (0.5, "50% complete")
        assert progress_values[2] == (0.75, "75% complete")
        assert progress_values[3] == (1.0, "100% complete")
        
        assert tracker.is_complete()
    
    def test_cache(self):
        """测试缓存"""
        cache = Cache(max_size=3)
        
        # 测试基本操作
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        assert cache.get("key1") == "value1"
        assert cache.get("key2") == "value2"
        assert cache.get("nonexistent") is None
        
        # 测试大小限制
        cache.set("key3", "value3")
        cache.set("key4", "value4")  # 应该移除最旧的key1
        
        assert cache.get("key1") is None
        assert cache.get("key2") == "value2"
        assert cache.get("key3") == "value3"
        assert cache.get("key4") == "value4"
        
        # 测试删除
        cache.delete("key2")
        assert cache.get("key2") is None
        
        # 测试清空
        cache.clear()
        assert cache.size() == 0


class TestExceptions:
    """异常类测试"""
    
    def test_video_reader_error(self):
        """测试基础异常"""
        error = VideoReaderError("Test error", "TEST001", "Test details")
        
        assert str(error) == "[TEST001] Test error"
        assert error.message == "Test error"
        assert error.error_code == "TEST001"
        assert error.details == "Test details"
        
        error_dict = error.to_dict()
        assert error_dict["error_type"] == "VideoReaderError"
        assert error_dict["message"] == "Test error"
        assert error_dict["error_code"] == "TEST001"
        assert error_dict["details"] == "Test details"
    
    def test_configuration_error(self):
        """测试配置错误"""
        error = ConfigurationError("Config error")
        assert isinstance(error, VideoReaderError)
        assert str(error) == "Config error"
    
    def test_validation_error(self):
        """测试验证错误"""
        error = ValidationError("Validation failed")
        assert isinstance(error, VideoReaderError)
        assert str(error) == "Validation failed"


class TestValidators:
    """验证器测试"""
    
    def test_validate_file_path(self, temp_dir):
        """测试文件路径验证"""
        from src.common.utils import validate_file_path
        
        # 创建测试文件
        test_file = temp_dir / "test.mp4"
        test_file.write_text("test content")
        
        # 测试存在的文件
        assert validate_file_path(test_file, must_exist=True) == True
        
        # 测试不存在的文件
        with pytest.raises(ValidationError):
            validate_file_path(temp_dir / "nonexistent.mp4", must_exist=True)
        
        # 测试文件扩展名
        assert validate_file_path(test_file, must_exist=True, 
                                allowed_extensions=['.mp4']) == True
        
        with pytest.raises(ValidationError):
            validate_file_path(test_file, must_exist=True, 
                             allowed_extensions=['.avi'])
    
    def test_validate_numeric_range(self):
        """测试数值范围验证"""
        from src.common.utils import validate_numeric_range
        
        # 测试有效范围
        assert validate_numeric_range(5, min_val=0, max_val=10) == True
        assert validate_numeric_range(0, min_val=0, max_val=10) == True
        assert validate_numeric_range(10, min_val=0, max_val=10) == True
        
        # 测试无效范围
        with pytest.raises(ValidationError):
            validate_numeric_range(-1, min_val=0, max_val=10)
        
        with pytest.raises(ValidationError):
            validate_numeric_range(11, min_val=0, max_val=10)
    
    def test_validator_chain(self):
        """测试验证器链式调用"""
        from src.common.utils import Validator
        
        # 测试成功的验证链
        result = (Validator("test_string", "test_field")
                 .required()
                 .type_of(str)
                 .min_length(5)
                 .max_length(20))
        
        assert result.value == "test_string"
        
        # 测试失败的验证链
        with pytest.raises(ValidationError):
            (Validator("", "empty_field")
             .required())
        
        with pytest.raises(ValidationError):
            (Validator("short", "short_field")
             .min_length(10))


@pytest.mark.unit
class TestIntegration:
    """通用模块集成测试"""
    
    def test_config_and_logging_integration(self, temp_dir):
        """测试配置和日志的集成"""
        config_file = temp_dir / "integration_config.json"
        manager = ConfigManager(config_file)
        
        # 设置日志配置
        manager.set("logging.level", "DEBUG")
        manager.set("logging.file", "test.log")
        manager.save()
        
        # 获取配置
        log_level = manager.get("logging.level")
        log_file = manager.get("logging.file")
        
        assert log_level == "DEBUG"
        assert log_file == "test.log"
        
        # 测试日志器
        logger = get_logger("integration_test")
        logger.info("Integration test message")
    
    def test_utils_and_exceptions_integration(self, temp_dir):
        """测试工具和异常的集成"""
        from src.common.utils import validate_file_path
        
        # 测试文件验证异常
        nonexistent_file = temp_dir / "nonexistent.txt"
        
        try:
            validate_file_path(nonexistent_file, must_exist=True)
            assert False, "应该抛出异常"
        except ValidationError as e:
            assert isinstance(e, VideoReaderError)
            assert "不存在" in str(e)
