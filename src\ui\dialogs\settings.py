"""设置对话框

提供应用程序设置配置的对话框。
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QLabel,
    QPushButton, QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
    QComboBox, QGroupBox, QFileDialog, QMessageBox, QSlider,
    QTextEdit, QListWidget, QListWidgetItem, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon
from typing import Dict, Any, Optional

try:
    from ...common.config import get_config, set_config, save_config
    from ...common.logging import get_logger
except ImportError:
    try:
        # 如果相对导入失败，尝试绝对导入
        from src.common.config import get_config, set_config, save_config
        from src.common.logging import get_logger
    except ImportError:
        # 最后尝试直接导入
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from common.config import get_config, set_config, save_config
        from common.logging import get_logger


logger = get_logger(__name__)


class SettingsDialog(QDialog):
    """设置对话框"""
    
    # 信号定义
    settings_changed = Signal(dict)  # 设置改变
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 设置数据
        self.settings = {}
        self.original_settings = {}
        
        # 初始化UI
        self._init_ui()
        self._load_settings()
        self._connect_signals()
        
        self.logger.info("设置对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 通用设置标签页
        general_tab = self._create_general_tab()
        tab_widget.addTab(general_tab, "通用")
        
        # 视频处理设置标签页
        video_tab = self._create_video_tab()
        tab_widget.addTab(video_tab, "视频处理")
        
        # 音频设置标签页
        audio_tab = self._create_audio_tab()
        tab_widget.addTab(audio_tab, "音频")
        
        # 界面设置标签页
        ui_tab = self._create_ui_tab()
        tab_widget.addTab(ui_tab, "界面")
        
        # 高级设置标签页
        advanced_tab = self._create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.reset_button = QPushButton("重置默认")
        self.reset_button.clicked.connect(self._on_reset_clicked)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self._on_ok_clicked)
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
    
    def _create_general_tab(self) -> QWidget:
        """创建通用设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 语言设置
        language_group = QGroupBox("语言设置")
        language_layout = QVBoxLayout(language_group)
        
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("界面语言:"))
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English", "日本語"])
        lang_layout.addWidget(self.language_combo)
        
        language_layout.addLayout(lang_layout)
        layout.addWidget(language_group)
        
        # 文件路径设置
        paths_group = QGroupBox("文件路径")
        paths_layout = QVBoxLayout(paths_group)
        
        # 输出目录
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("默认输出目录:"))
        
        self.output_dir_edit = QLineEdit()
        output_layout.addWidget(self.output_dir_edit)
        
        self.output_browse_button = QPushButton("浏览...")
        self.output_browse_button.clicked.connect(self._browse_output_dir)
        output_layout.addWidget(self.output_browse_button)
        
        paths_layout.addLayout(output_layout)
        
        # 缓存目录
        cache_layout = QHBoxLayout()
        cache_layout.addWidget(QLabel("缓存目录:"))
        
        self.cache_dir_edit = QLineEdit()
        cache_layout.addWidget(self.cache_dir_edit)
        
        self.cache_browse_button = QPushButton("浏览...")
        self.cache_browse_button.clicked.connect(self._browse_cache_dir)
        cache_layout.addWidget(self.cache_browse_button)
        
        paths_layout.addLayout(cache_layout)
        
        layout.addWidget(paths_group)
        
        # 启动设置
        startup_group = QGroupBox("启动设置")
        startup_layout = QVBoxLayout(startup_group)
        
        self.auto_load_recent_checkbox = QCheckBox("启动时自动加载最近文件")
        startup_layout.addWidget(self.auto_load_recent_checkbox)
        
        self.check_updates_checkbox = QCheckBox("启动时检查更新")
        startup_layout.addWidget(self.check_updates_checkbox)
        
        layout.addWidget(startup_group)
        
        layout.addStretch()
        return tab
    
    def _create_video_tab(self) -> QWidget:
        """创建视频处理设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 默认解析器设置
        parser_group = QGroupBox("默认解析器")
        parser_layout = QVBoxLayout(parser_group)
        
        parser_type_layout = QHBoxLayout()
        parser_type_layout.addWidget(QLabel("默认解析器:"))
        
        self.default_parser_combo = QComboBox()
        self.default_parser_combo.addItems([
            "场景变化分段", "文本长度分段", "时间固定分段", "静音分段"
        ])
        parser_type_layout.addWidget(self.default_parser_combo)
        
        parser_layout.addLayout(parser_type_layout)
        layout.addWidget(parser_group)
        
        # 性能设置
        performance_group = QGroupBox("性能设置")
        performance_layout = QVBoxLayout(performance_group)
        
        # 线程数
        thread_layout = QHBoxLayout()
        thread_layout.addWidget(QLabel("处理线程数:"))
        
        self.thread_count_spinbox = QSpinBox()
        self.thread_count_spinbox.setRange(1, 16)
        thread_layout.addWidget(self.thread_count_spinbox)
        
        performance_layout.addLayout(thread_layout)
        
        # GPU加速
        self.enable_gpu_checkbox = QCheckBox("启用GPU加速（如果可用）")
        performance_layout.addWidget(self.enable_gpu_checkbox)
        
        layout.addWidget(performance_group)
        
        # 缓存设置
        cache_group = QGroupBox("缓存设置")
        cache_layout = QVBoxLayout(cache_group)
        
        self.enable_cache_checkbox = QCheckBox("启用处理结果缓存")
        cache_layout.addWidget(self.enable_cache_checkbox)
        
        # 缓存大小限制
        cache_size_layout = QHBoxLayout()
        cache_size_layout.addWidget(QLabel("缓存大小限制(MB):"))
        
        self.cache_size_spinbox = QSpinBox()
        self.cache_size_spinbox.setRange(100, 10000)
        self.cache_size_spinbox.setSuffix(" MB")
        cache_size_layout.addWidget(self.cache_size_spinbox)
        
        cache_layout.addLayout(cache_size_layout)
        
        # 缓存过期时间
        cache_expire_layout = QHBoxLayout()
        cache_expire_layout.addWidget(QLabel("缓存过期时间(天):"))
        
        self.cache_expire_spinbox = QSpinBox()
        self.cache_expire_spinbox.setRange(1, 365)
        self.cache_expire_spinbox.setSuffix(" 天")
        cache_expire_layout.addWidget(self.cache_expire_spinbox)
        
        cache_layout.addLayout(cache_expire_layout)
        
        layout.addWidget(cache_group)
        
        layout.addStretch()
        return tab
    
    def _create_audio_tab(self) -> QWidget:
        """创建音频设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 语音识别设置
        speech_group = QGroupBox("语音识别")
        speech_layout = QVBoxLayout(speech_group)
        
        # 默认引擎
        engine_layout = QHBoxLayout()
        engine_layout.addWidget(QLabel("默认识别引擎:"))
        
        self.speech_engine_combo = QComboBox()
        self.speech_engine_combo.addItems(["Whisper", "Azure Speech", "Google Speech"])
        engine_layout.addWidget(self.speech_engine_combo)
        
        speech_layout.addLayout(engine_layout)
        
        # 默认语言
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("默认语言:"))
        
        self.speech_language_combo = QComboBox()
        self.speech_language_combo.addItems(["中文", "英文", "日文", "韩文"])
        lang_layout.addWidget(self.speech_language_combo)
        
        speech_layout.addLayout(lang_layout)
        
        # Whisper模型大小
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Whisper模型大小:"))
        
        self.whisper_model_combo = QComboBox()
        self.whisper_model_combo.addItems(["tiny", "base", "small", "medium", "large"])
        model_layout.addWidget(self.whisper_model_combo)
        
        speech_layout.addLayout(model_layout)
        
        layout.addWidget(speech_group)
        
        # 音频处理设置
        audio_group = QGroupBox("音频处理")
        audio_layout = QVBoxLayout(audio_group)
        
        # 采样率
        sample_rate_layout = QHBoxLayout()
        sample_rate_layout.addWidget(QLabel("采样率:"))
        
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["16000", "22050", "44100", "48000"])
        sample_rate_layout.addWidget(self.sample_rate_combo)
        
        audio_layout.addLayout(sample_rate_layout)
        
        # 声道数
        channels_layout = QHBoxLayout()
        channels_layout.addWidget(QLabel("声道数:"))
        
        self.channels_combo = QComboBox()
        self.channels_combo.addItems(["1 (单声道)", "2 (立体声)"])
        channels_layout.addWidget(self.channels_combo)
        
        audio_layout.addLayout(channels_layout)
        
        layout.addWidget(audio_group)
        
        layout.addStretch()
        return tab
    
    def _create_ui_tab(self) -> QWidget:
        """创建界面设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 外观设置
        appearance_group = QGroupBox("外观")
        appearance_layout = QVBoxLayout(appearance_group)
        
        # 主题
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("主题:"))
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "浅色"])
        theme_layout.addWidget(self.theme_combo)
        
        appearance_layout.addLayout(theme_layout)
        
        # 字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(8, 24)
        self.font_size_spinbox.setSuffix(" pt")
        font_size_layout.addWidget(self.font_size_spinbox)
        
        appearance_layout.addLayout(font_size_layout)
        
        layout.addWidget(appearance_group)
        
        # 行为设置
        behavior_group = QGroupBox("行为")
        behavior_layout = QVBoxLayout(behavior_group)
        
        self.auto_play_checkbox = QCheckBox("选择段落时自动播放")
        behavior_layout.addWidget(self.auto_play_checkbox)
        
        self.show_tooltips_checkbox = QCheckBox("显示工具提示")
        behavior_layout.addWidget(self.show_tooltips_checkbox)
        
        self.confirm_exit_checkbox = QCheckBox("退出时确认")
        behavior_layout.addWidget(self.confirm_exit_checkbox)
        
        layout.addWidget(behavior_group)
        
        layout.addStretch()
        return tab
    
    def _create_advanced_tab(self) -> QWidget:
        """创建高级设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 日志设置
        logging_group = QGroupBox("日志")
        logging_layout = QVBoxLayout(logging_group)
        
        # 日志级别
        log_level_layout = QHBoxLayout()
        log_level_layout.addWidget(QLabel("日志级别:"))
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        log_level_layout.addWidget(self.log_level_combo)
        
        logging_layout.addLayout(log_level_layout)
        
        # 日志文件
        self.enable_log_file_checkbox = QCheckBox("保存日志到文件")
        logging_layout.addWidget(self.enable_log_file_checkbox)
        
        layout.addWidget(logging_group)
        
        # 调试设置
        debug_group = QGroupBox("调试")
        debug_layout = QVBoxLayout(debug_group)
        
        self.debug_mode_checkbox = QCheckBox("启用调试模式")
        debug_layout.addWidget(self.debug_mode_checkbox)
        
        self.verbose_output_checkbox = QCheckBox("详细输出")
        debug_layout.addWidget(self.verbose_output_checkbox)
        
        layout.addWidget(debug_group)
        
        # 实验性功能
        experimental_group = QGroupBox("实验性功能")
        experimental_layout = QVBoxLayout(experimental_group)
        
        self.experimental_features_checkbox = QCheckBox("启用实验性功能")
        experimental_layout.addWidget(self.experimental_features_checkbox)
        
        layout.addWidget(experimental_group)
        
        layout.addStretch()
        return tab
    
    def _connect_signals(self):
        """连接信号和槽"""
        pass
    
    def _load_settings(self):
        """加载设置"""
        try:
            # 加载各种设置
            self.output_dir_edit.setText(get_config('paths.output_dir', './output'))
            self.cache_dir_edit.setText(get_config('paths.cache_dir', './cache'))
            
            self.thread_count_spinbox.setValue(get_config('processing.thread_count', 4))
            self.enable_cache_checkbox.setChecked(get_config('cache.enabled', True))
            self.cache_size_spinbox.setValue(get_config('cache.max_size_mb', 1000))
            self.cache_expire_spinbox.setValue(get_config('cache.max_age_days', 30))
            
            # 保存原始设置用于比较
            self.original_settings = self._get_current_settings()
            
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
    
    def _get_current_settings(self) -> Dict[str, Any]:
        """获取当前设置"""
        return {
            'paths.output_dir': self.output_dir_edit.text(),
            'paths.cache_dir': self.cache_dir_edit.text(),
            'processing.thread_count': self.thread_count_spinbox.value(),
            'cache.enabled': self.enable_cache_checkbox.isChecked(),
            'cache.max_size_mb': self.cache_size_spinbox.value(),
            'cache.max_age_days': self.cache_expire_spinbox.value(),
            # 添加其他设置...
        }
    
    def _browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir_edit.setText(dir_path)
    
    def _browse_cache_dir(self):
        """浏览缓存目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择缓存目录")
        if dir_path:
            self.cache_dir_edit.setText(dir_path)
    
    def _on_reset_clicked(self):
        """重置按钮点击处理"""
        reply = QMessageBox.question(
            self, "确认重置", "确定要重置所有设置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self._reset_to_defaults()
    
    def _reset_to_defaults(self):
        """重置为默认设置"""
        self.output_dir_edit.setText('./output')
        self.cache_dir_edit.setText('./cache')
        self.thread_count_spinbox.setValue(4)
        self.enable_cache_checkbox.setChecked(True)
        self.cache_size_spinbox.setValue(1000)
        self.cache_expire_spinbox.setValue(30)
        # 重置其他设置...
    
    def _on_ok_clicked(self):
        """确定按钮点击处理"""
        try:
            # 获取当前设置
            current_settings = self._get_current_settings()
            
            # 保存设置
            for key, value in current_settings.items():
                set_config(key, value)
            
            save_config()
            
            # 检查是否有更改
            if current_settings != self.original_settings:
                self.settings_changed.emit(current_settings)
            
            self.accept()
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")
    
    @staticmethod
    def show_settings(parent=None) -> Optional[Dict[str, Any]]:
        """显示设置对话框的静态方法"""
        dialog = SettingsDialog(parent)
        if dialog.exec_() == QDialog.Accepted:
            return dialog._get_current_settings()
        return None
