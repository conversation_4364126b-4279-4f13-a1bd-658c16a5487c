"""导出对话框

提供数据导出配置和执行的对话框。
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton,
    QLineEdit, QComboBox, QGroupBox, QFileDialog, QMessageBox,
    QCheckBox, QTextEdit, QProgressBar, QTabWidget, QSpinBox,
    QListWidget, QListWidgetItem, QFrame, QSplitter
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtGui import QFont, QIcon
from typing import Dict, Any, Optional, List
from pathlib import Path

from ....core.models import ProcessResult, ExportFormat, ExportOptions, SegmentInfo
from ....common.logging import get_logger


logger = get_logger(__name__)


class ExportWorker(QThread):
    """导出工作线程"""
    
    progress_updated = Signal(int, str)  # 进度更新
    export_completed = Signal(bool, str)  # 导出完成
    
    def __init__(self, process_result: ProcessResult, options: ExportOptions):
        super().__init__()
        self.process_result = process_result
        self.options = options
        self.logger = get_logger(__name__)
    
    def run(self):
        """执行导出"""
        try:
            self.progress_updated.emit(10, "准备导出...")
            
            # 这里应该调用实际的导出功能
            # 暂时使用模拟导出
            self._simulate_export()
            
            self.export_completed.emit(True, "导出成功")
            
        except Exception as e:
            self.logger.error(f"导出失败: {e}")
            self.export_completed.emit(False, f"导出失败: {e}")
    
    def _simulate_export(self):
        """模拟导出过程"""
        import time
        
        steps = [
            (20, "正在处理段落数据..."),
            (40, "正在生成文本内容..."),
            (60, "正在格式化输出..."),
            (80, "正在写入文件..."),
            (100, "导出完成")
        ]
        
        for progress, message in steps:
            if self.isInterruptionRequested():
                return
            
            self.progress_updated.emit(progress, message)
            time.sleep(0.5)  # 模拟处理时间


class ExportDialog(QDialog):
    """导出对话框"""
    
    def __init__(self, process_result: ProcessResult, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.process_result = process_result
        
        # 导出工作线程
        self.export_worker = None
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()
        self._load_default_settings()
        
        self.logger.info("导出对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("导出数据")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 基本设置标签页
        basic_tab = self._create_basic_tab()
        tab_widget.addTab(basic_tab, "基本设置")
        
        # 高级选项标签页
        advanced_tab = self._create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级选项")
        
        # 预览标签页
        preview_tab = self._create_preview_tab()
        tab_widget.addTab(preview_tab, "预览")
        
        layout.addWidget(tab_widget)
        
        # 进度区域
        progress_group = QGroupBox("导出进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("就绪")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(progress_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.preview_button = QPushButton("预览")
        self.preview_button.clicked.connect(self._on_preview_clicked)
        button_layout.addWidget(self.preview_button)
        
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self._on_cancel_clicked)
        button_layout.addWidget(self.cancel_button)
        
        self.export_button = QPushButton("导出")
        self.export_button.clicked.connect(self._on_export_clicked)
        self.export_button.setDefault(True)
        button_layout.addWidget(self.export_button)
        
        layout.addLayout(button_layout)
    
    def _create_basic_tab(self) -> QWidget:
        """创建基本设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 导出格式
        format_group = QGroupBox("导出格式")
        format_layout = QVBoxLayout(format_group)
        
        format_combo_layout = QHBoxLayout()
        format_combo_layout.addWidget(QLabel("格式:"))
        
        self.format_combo = QComboBox()
        self.format_combo.addItem("文本文件 (.txt)", ExportFormat.TXT)
        self.format_combo.addItem("JSON文件 (.json)", ExportFormat.JSON)
        self.format_combo.addItem("CSV文件 (.csv)", ExportFormat.CSV)
        self.format_combo.addItem("SRT字幕 (.srt)", ExportFormat.SRT)
        self.format_combo.addItem("VTT字幕 (.vtt)", ExportFormat.VTT)
        self.format_combo.addItem("Word文档 (.docx)", ExportFormat.DOCX)
        self.format_combo.addItem("PDF文档 (.pdf)", ExportFormat.PDF)
        self.format_combo.currentTextChanged.connect(self._on_format_changed)
        format_combo_layout.addWidget(self.format_combo)
        
        format_layout.addLayout(format_combo_layout)
        layout.addWidget(format_group)
        
        # 输出文件
        output_group = QGroupBox("输出文件")
        output_layout = QVBoxLayout(output_group)
        
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("文件路径:"))
        
        self.output_path_edit = QLineEdit()
        file_layout.addWidget(self.output_path_edit)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self._on_browse_clicked)
        file_layout.addWidget(self.browse_button)
        
        output_layout.addLayout(file_layout)
        layout.addWidget(output_group)
        
        # 内容选项
        content_group = QGroupBox("内容选项")
        content_layout = QVBoxLayout(content_group)
        
        self.include_summary_checkbox = QCheckBox("包含段落摘要")
        self.include_summary_checkbox.setChecked(True)
        content_layout.addWidget(self.include_summary_checkbox)
        
        self.include_metadata_checkbox = QCheckBox("包含元数据信息")
        content_layout.addWidget(self.include_metadata_checkbox)
        
        self.include_timestamps_checkbox = QCheckBox("包含时间戳")
        self.include_timestamps_checkbox.setChecked(True)
        content_layout.addWidget(self.include_timestamps_checkbox)
        
        self.include_confidence_checkbox = QCheckBox("包含置信度")
        content_layout.addWidget(self.include_confidence_checkbox)
        
        layout.addWidget(content_group)
        
        layout.addStretch()
        return tab
    
    def _create_advanced_tab(self) -> QWidget:
        """创建高级选项标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 段落过滤
        filter_group = QGroupBox("段落过滤")
        filter_layout = QVBoxLayout(filter_group)
        
        # 时长过滤
        duration_layout = QHBoxLayout()
        self.filter_by_duration_checkbox = QCheckBox("按时长过滤:")
        duration_layout.addWidget(self.filter_by_duration_checkbox)
        
        duration_layout.addWidget(QLabel("最小时长(秒):"))
        self.min_duration_spinbox = QSpinBox()
        self.min_duration_spinbox.setRange(0, 3600)
        self.min_duration_spinbox.setValue(0)
        duration_layout.addWidget(self.min_duration_spinbox)
        
        duration_layout.addWidget(QLabel("最大时长(秒):"))
        self.max_duration_spinbox = QSpinBox()
        self.max_duration_spinbox.setRange(0, 3600)
        self.max_duration_spinbox.setValue(300)
        duration_layout.addWidget(self.max_duration_spinbox)
        
        filter_layout.addLayout(duration_layout)
        
        # 置信度过滤
        confidence_layout = QHBoxLayout()
        self.filter_by_confidence_checkbox = QCheckBox("按置信度过滤:")
        confidence_layout.addWidget(self.filter_by_confidence_checkbox)
        
        confidence_layout.addWidget(QLabel("最小置信度:"))
        self.min_confidence_spinbox = QSpinBox()
        self.min_confidence_spinbox.setRange(0, 100)
        self.min_confidence_spinbox.setValue(0)
        self.min_confidence_spinbox.setSuffix("%")
        confidence_layout.addWidget(self.min_confidence_spinbox)
        
        filter_layout.addLayout(confidence_layout)
        
        layout.addWidget(filter_group)
        
        # 文本处理
        text_group = QGroupBox("文本处理")
        text_layout = QVBoxLayout(text_group)
        
        self.remove_empty_lines_checkbox = QCheckBox("移除空行")
        self.remove_empty_lines_checkbox.setChecked(True)
        text_layout.addWidget(self.remove_empty_lines_checkbox)
        
        self.trim_whitespace_checkbox = QCheckBox("去除首尾空格")
        self.trim_whitespace_checkbox.setChecked(True)
        text_layout.addWidget(self.trim_whitespace_checkbox)
        
        self.normalize_punctuation_checkbox = QCheckBox("标准化标点符号")
        text_layout.addWidget(self.normalize_punctuation_checkbox)
        
        layout.addWidget(text_group)
        
        # 格式化选项
        format_group = QGroupBox("格式化选项")
        format_layout = QVBoxLayout(format_group)
        
        # 编码
        encoding_layout = QHBoxLayout()
        encoding_layout.addWidget(QLabel("文件编码:"))
        
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["UTF-8", "GBK", "ASCII"])
        encoding_layout.addWidget(self.encoding_combo)
        
        format_layout.addLayout(encoding_layout)
        
        # 换行符
        newline_layout = QHBoxLayout()
        newline_layout.addWidget(QLabel("换行符:"))
        
        self.newline_combo = QComboBox()
        self.newline_combo.addItems(["系统默认", "LF (Unix)", "CRLF (Windows)", "CR (Mac)"])
        newline_layout.addWidget(self.newline_combo)
        
        format_layout.addLayout(newline_layout)
        
        layout.addWidget(format_group)
        
        layout.addStretch()
        return tab
    
    def _create_preview_tab(self) -> QWidget:
        """创建预览标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 预览区域
        preview_group = QGroupBox("导出预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setFont(QFont("Consolas", 10))
        preview_layout.addWidget(self.preview_text)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.stats_label = QLabel("统计信息将在预览后显示")
        stats_layout.addWidget(self.stats_label)
        
        stats_layout.addStretch()
        preview_layout.addLayout(stats_layout)
        
        layout.addWidget(preview_group)
        
        return tab
    
    def _connect_signals(self):
        """连接信号和槽"""
        pass
    
    def _load_default_settings(self):
        """加载默认设置"""
        # 设置默认输出路径
        if self.process_result and self.process_result.video_info:
            video_name = Path(self.process_result.video_info.file_name).stem
            default_path = f"./output/{video_name}.txt"
            self.output_path_edit.setText(default_path)
    
    def _on_format_changed(self):
        """格式改变处理"""
        # 更新文件扩展名
        current_path = self.output_path_edit.text()
        if current_path:
            path = Path(current_path)
            format_ext = self.format_combo.currentData().value
            new_path = path.with_suffix(f".{format_ext}")
            self.output_path_edit.setText(str(new_path))
    
    def _on_browse_clicked(self):
        """浏览按钮点击处理"""
        format_data = self.format_combo.currentData()
        format_name = self.format_combo.currentText()
        
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            self, f"保存{format_name}", self.output_path_edit.text(),
            f"{format_name} (*{format_data.value})"
        )
        
        if file_path:
            self.output_path_edit.setText(file_path)
    
    def _on_preview_clicked(self):
        """预览按钮点击处理"""
        try:
            # 生成预览内容
            preview_content = self._generate_preview()
            self.preview_text.setPlainText(preview_content)
            
            # 更新统计信息
            self._update_preview_stats(preview_content)
            
        except Exception as e:
            self.logger.error(f"生成预览失败: {e}")
            QMessageBox.warning(self, "警告", f"生成预览失败: {e}")
    
    def _generate_preview(self) -> str:
        """生成预览内容"""
        if not self.process_result:
            return "无数据可预览"
        
        # 获取过滤后的段落
        filtered_segments = self._get_filtered_segments()
        
        if not filtered_segments:
            return "没有符合条件的段落"
        
        # 根据格式生成预览
        format_type = self.format_combo.currentData()
        
        if format_type == ExportFormat.TXT:
            return self._generate_txt_preview(filtered_segments)
        elif format_type == ExportFormat.JSON:
            return self._generate_json_preview(filtered_segments)
        elif format_type == ExportFormat.CSV:
            return self._generate_csv_preview(filtered_segments)
        elif format_type == ExportFormat.SRT:
            return self._generate_srt_preview(filtered_segments)
        else:
            return f"格式 {format_type.value} 的预览暂不支持"
    
    def _get_filtered_segments(self) -> List[SegmentInfo]:
        """获取过滤后的段落"""
        segments = self.process_result.segments
        
        # 应用过滤条件
        if self.filter_by_duration_checkbox.isChecked():
            min_duration = self.min_duration_spinbox.value()
            max_duration = self.max_duration_spinbox.value()
            segments = [s for s in segments if min_duration <= s.duration <= max_duration]
        
        if self.filter_by_confidence_checkbox.isChecked():
            min_confidence = self.min_confidence_spinbox.value() / 100.0
            segments = [s for s in segments if s.confidence >= min_confidence]
        
        return segments
    
    def _generate_txt_preview(self, segments: List[SegmentInfo]) -> str:
        """生成TXT格式预览"""
        lines = []
        
        # 头部信息
        lines.append(f"视频文件: {self.process_result.video_info.file_name}")
        lines.append(f"段落数量: {len(segments)}")
        lines.append("=" * 50)
        lines.append("")
        
        # 段落内容（只显示前3个）
        for i, segment in enumerate(segments[:3]):
            lines.append(f"段落 {segment.id + 1}")
            lines.append(f"时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s")
            
            if self.include_summary_checkbox.isChecked() and segment.summary:
                lines.append(f"摘要: {segment.summary}")
            
            lines.append(f"内容: {segment.text}")
            lines.append("-" * 30)
            lines.append("")
        
        if len(segments) > 3:
            lines.append(f"... 还有 {len(segments) - 3} 个段落")
        
        return "\n".join(lines)
    
    def _generate_json_preview(self, segments: List[SegmentInfo]) -> str:
        """生成JSON格式预览"""
        import json
        
        data = {
            "video_info": {
                "file_name": self.process_result.video_info.file_name,
                "duration": self.process_result.video_info.duration
            },
            "segments": [
                {
                    "id": seg.id,
                    "start_time": seg.start_time,
                    "end_time": seg.end_time,
                    "text": seg.text
                }
                for seg in segments[:3]  # 只显示前3个
            ]
        }
        
        if len(segments) > 3:
            data["note"] = f"预览只显示前3个段落，实际共有{len(segments)}个段落"
        
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _generate_csv_preview(self, segments: List[SegmentInfo]) -> str:
        """生成CSV格式预览"""
        lines = []
        
        # 表头
        headers = ["ID", "开始时间", "结束时间", "时长", "文本"]
        if self.include_summary_checkbox.isChecked():
            headers.append("摘要")
        if self.include_confidence_checkbox.isChecked():
            headers.append("置信度")
        
        lines.append(",".join(headers))
        
        # 数据行（只显示前3个）
        for segment in segments[:3]:
            row = [
                str(segment.id),
                f"{segment.start_time:.2f}",
                f"{segment.end_time:.2f}",
                f"{segment.duration:.2f}",
                f'"{segment.text}"'  # 用引号包围文本
            ]
            
            if self.include_summary_checkbox.isChecked():
                row.append(f'"{segment.summary or ""}"')
            
            if self.include_confidence_checkbox.isChecked():
                row.append(f"{segment.confidence:.2f}")
            
            lines.append(",".join(row))
        
        if len(segments) > 3:
            lines.append(f"# ... 还有 {len(segments) - 3} 个段落")
        
        return "\n".join(lines)
    
    def _generate_srt_preview(self, segments: List[SegmentInfo]) -> str:
        """生成SRT格式预览"""
        lines = []
        
        # SRT格式（只显示前3个）
        for i, segment in enumerate(segments[:3], 1):
            start_time = self._seconds_to_srt_time(segment.start_time)
            end_time = self._seconds_to_srt_time(segment.end_time)
            
            lines.append(str(i))
            lines.append(f"{start_time} --> {end_time}")
            lines.append(segment.text)
            lines.append("")
        
        if len(segments) > 3:
            lines.append(f"... 还有 {len(segments) - 3} 个字幕条目")
        
        return "\n".join(lines)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _update_preview_stats(self, content: str):
        """更新预览统计信息"""
        filtered_segments = self._get_filtered_segments()
        total_segments = len(self.process_result.segments)
        
        stats = f"总段落: {total_segments} | 过滤后: {len(filtered_segments)} | "
        stats += f"预览字符数: {len(content)}"
        
        self.stats_label.setText(stats)
    
    def _on_export_clicked(self):
        """导出按钮点击处理"""
        try:
            # 验证输出路径
            output_path = self.output_path_edit.text().strip()
            if not output_path:
                QMessageBox.warning(self, "警告", "请指定输出文件路径")
                return
            
            # 创建导出选项
            options = self._create_export_options()
            
            # 开始导出
            self._start_export(options)
            
        except Exception as e:
            self.logger.error(f"导出失败: {e}")
            QMessageBox.critical(self, "错误", f"导出失败: {e}")
    
    def _create_export_options(self) -> ExportOptions:
        """创建导出选项"""
        return ExportOptions(
            format=self.format_combo.currentData(),
            output_path=self.output_path_edit.text(),
            include_summary=self.include_summary_checkbox.isChecked(),
            include_metadata=self.include_metadata_checkbox.isChecked(),
            include_timestamps=self.include_timestamps_checkbox.isChecked(),
            include_confidence=self.include_confidence_checkbox.isChecked(),
            encoding=self.encoding_combo.currentText(),
            filter_by_duration=self.filter_by_duration_checkbox.isChecked(),
            min_duration=self.min_duration_spinbox.value(),
            max_duration=self.max_duration_spinbox.value(),
            filter_by_confidence=self.filter_by_confidence_checkbox.isChecked(),
            min_confidence=self.min_confidence_spinbox.value() / 100.0
        )
    
    def _start_export(self, options: ExportOptions):
        """开始导出"""
        # 禁用按钮
        self.export_button.setEnabled(False)
        self.cancel_button.setText("中止")
        
        # 创建并启动工作线程
        self.export_worker = ExportWorker(self.process_result, options)
        self.export_worker.progress_updated.connect(self._on_progress_updated)
        self.export_worker.export_completed.connect(self._on_export_completed)
        self.export_worker.start()
    
    def _on_progress_updated(self, progress: int, message: str):
        """进度更新处理"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(message)
    
    def _on_export_completed(self, success: bool, message: str):
        """导出完成处理"""
        # 恢复按钮状态
        self.export_button.setEnabled(True)
        self.cancel_button.setText("取消")
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.accept()
        else:
            QMessageBox.critical(self, "失败", message)
    
    def _on_cancel_clicked(self):
        """取消按钮点击处理"""
        if self.export_worker and self.export_worker.isRunning():
            # 中止导出
            self.export_worker.requestInterruption()
            self.export_worker.wait()
        
        self.reject()
    
    @staticmethod
    def export_data(process_result: ProcessResult, parent=None) -> bool:
        """显示导出对话框的静态方法"""
        dialog = ExportDialog(process_result, parent)
        return dialog.exec_() == QDialog.Accepted
