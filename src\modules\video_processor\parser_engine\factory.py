"""解析器工厂

负责创建和管理不同类型的解析器。
"""

from typing import Dict, Type, List
from ....core.models import ParserType
from ....common.exceptions import ProcessingError
from ....common.logging import get_logger
from .base import BaseParser


logger = get_logger(__name__)


class ParserFactory:
    """解析器工厂类
    
    负责创建和管理不同类型的解析器实例。
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._parsers: Dict[ParserType, Type[BaseParser]] = {}
        self._register_default_parsers()
    
    def _register_default_parsers(self) -> None:
        """注册默认的解析器"""
        try:
            # 延迟导入避免循环依赖
            from .scene_parser import SceneChangeParser
            from .text_parser import TextLengthParser
            from .time_parser import TimeFixedParser
            from .silence_parser import SilenceBasedParser
            
            self.register_parser(ParserType.SCENE_CHANGE, SceneChangeParser)
            self.register_parser(ParserType.TEXT_LENGTH, TextLengthParser)
            self.register_parser(ParserType.TIME_FIXED, TimeFixedParser)
            self.register_parser(ParserType.SILENCE_BASED, SilenceBasedParser)
            
            self.logger.info("默认解析器注册完成")
            
        except ImportError as e:
            self.logger.warning(f"部分解析器注册失败: {e}")
    
    def register_parser(self, parser_type: ParserType, parser_class: Type[BaseParser]) -> None:
        """注册解析器
        
        Args:
            parser_type: 解析器类型
            parser_class: 解析器类
        """
        if not issubclass(parser_class, BaseParser):
            raise ValueError(f"解析器类必须继承自BaseParser: {parser_class}")
        
        self._parsers[parser_type] = parser_class
        self.logger.debug(f"注册解析器: {parser_type.value} -> {parser_class.__name__}")
    
    def create_parser(self, parser_type: ParserType) -> BaseParser:
        """创建解析器实例
        
        Args:
            parser_type: 解析器类型
            
        Returns:
            BaseParser: 解析器实例
            
        Raises:
            ProcessingError: 不支持的解析器类型
        """
        if parser_type not in self._parsers:
            available_types = list(self._parsers.keys())
            raise ProcessingError(
                f"不支持的解析器类型: {parser_type.value}. "
                f"可用类型: {[t.value for t in available_types]}"
            )
        
        parser_class = self._parsers[parser_type]
        
        try:
            parser = parser_class()
            self.logger.debug(f"创建解析器: {parser_type.value}")
            return parser
            
        except Exception as e:
            self.logger.error(f"创建解析器失败: {parser_type.value}, 错误: {e}")
            raise ProcessingError(f"创建解析器失败: {e}")
    
    def get_available_parsers(self) -> List[ParserType]:
        """获取可用的解析器类型列表
        
        Returns:
            List[ParserType]: 可用的解析器类型
        """
        return list(self._parsers.keys())
    
    def is_parser_available(self, parser_type: ParserType) -> bool:
        """检查解析器是否可用
        
        Args:
            parser_type: 解析器类型
            
        Returns:
            bool: 是否可用
        """
        return parser_type in self._parsers
    
    def get_parser_info(self, parser_type: ParserType) -> Dict[str, str]:
        """获取解析器信息
        
        Args:
            parser_type: 解析器类型
            
        Returns:
            Dict[str, str]: 解析器信息
        """
        if parser_type not in self._parsers:
            return {}
        
        parser_class = self._parsers[parser_type]
        
        return {
            'type': parser_type.value,
            'name': parser_class.__name__,
            'description': parser_class.__doc__ or '',
            'module': parser_class.__module__
        }
    
    def get_all_parser_info(self) -> Dict[ParserType, Dict[str, str]]:
        """获取所有解析器信息
        
        Returns:
            Dict[ParserType, Dict[str, str]]: 所有解析器信息
        """
        info = {}
        for parser_type in self._parsers:
            info[parser_type] = self.get_parser_info(parser_type)
        return info


# 全局工厂实例
_parser_factory = None


def get_parser_factory() -> ParserFactory:
    """获取解析器工厂实例（单例模式）"""
    global _parser_factory
    if _parser_factory is None:
        _parser_factory = ParserFactory()
    return _parser_factory


def create_parser(parser_type: ParserType) -> BaseParser:
    """创建解析器实例的便捷函数"""
    factory = get_parser_factory()
    return factory.create_parser(parser_type)


def get_available_parsers() -> List[ParserType]:
    """获取可用解析器类型的便捷函数"""
    factory = get_parser_factory()
    return factory.get_available_parsers()
