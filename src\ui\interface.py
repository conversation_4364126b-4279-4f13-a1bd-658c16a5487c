"""界面模块对外接口

定义用户界面模块的统一接口，负责GUI界面的显示和用户交互。
"""

from abc import ABC, abstractmethod
from typing import Callable, Any, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

try:
    from ..core.models import VideoInfo, ProcessResult, SegmentInfo, SearchResult
    from ..common.exceptions import UIError
except ImportError:
    try:
        from src.core.models import VideoInfo, ProcessResult, SegmentInfo, SearchResult
        from src.common.exceptions import UIError
    except ImportError:
        # 最后尝试直接导入
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from core.models import VideoInfo, ProcessResult, SegmentInfo, SearchResult
        from common.exceptions import UIError


class UIEventType(Enum):
    """UI事件类型枚举"""
    
    # 文件操作事件
    FILE_OPEN_REQUESTED = "file_open_requested"
    FILE_OPENED = "file_opened"
    FILE_SAVE_REQUESTED = "file_save_requested"
    FILE_SAVED = "file_saved"
    FILE_SELECTED = "file_selected"
    FILE_REMOVED = "file_removed"
    METADATA_LOAD_REQUESTED = "metadata_load_requested"
    
    # 视频处理事件
    PROCESS_START_REQUESTED = "process_start_requested"
    PROCESS_STARTED = "process_started"
    PROCESS_PROGRESS_UPDATED = "process_progress_updated"
    PROCESS_COMPLETED = "process_completed"
    PROCESS_CANCELLED = "process_cancelled"
    PROCESS_CANCEL_REQUESTED = "process_cancel_requested"
    
    # 播放控制事件
    PLAY_REQUESTED = "play_requested"
    PAUSE_REQUESTED = "pause_requested"
    STOP_REQUESTED = "stop_requested"
    SEEK_REQUESTED = "seek_requested"
    VOLUME_CHANGED = "volume_changed"
    
    # 段落操作事件
    SEGMENT_SELECTED = "segment_selected"
    SEGMENT_EDITED = "segment_edited"
    SEGMENT_DELETED = "segment_deleted"
    
    # 搜索事件
    SEARCH_REQUESTED = "search_requested"
    SEARCH_COMPLETED = "search_completed"
    SEARCH_CLEARED = "search_cleared"
    
    # 导出事件
    EXPORT_REQUESTED = "export_requested"
    EXPORT_COMPLETED = "export_completed"
    
    # 设置事件
    SETTINGS_OPENED = "settings_opened"
    SETTINGS_CHANGED = "settings_changed"
    
    # 窗口事件
    WINDOW_CLOSED = "window_closed"
    WINDOW_MINIMIZED = "window_minimized"
    WINDOW_MAXIMIZED = "window_maximized"


@dataclass
class UIEvent:
    """UI事件数据类"""
    event_type: UIEventType
    data: Dict[str, Any]
    source: Optional[str] = None
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """获取事件数据"""
        return self.data.get(key, default)


class UIManagerInterface(ABC):
    """界面管理器接口"""
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化界面
        
        Returns:
            bool: 是否初始化成功
            
        Raises:
            UIError: 初始化失败
        """
        pass
    
    @abstractmethod
    def show_main_window(self) -> None:
        """显示主窗口"""
        pass
    
    @abstractmethod
    def hide_main_window(self) -> None:
        """隐藏主窗口"""
        pass
    
    @abstractmethod
    def close_application(self) -> None:
        """关闭应用程序"""
        pass
    
    @abstractmethod
    def register_event_handler(self, event_type: UIEventType, handler: Callable[[UIEvent], None]) -> None:
        """注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        pass
    
    @abstractmethod
    def unregister_event_handler(self, event_type: UIEventType, handler: Callable[[UIEvent], None]) -> None:
        """取消注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        pass
    
    @abstractmethod
    def emit_event(self, event: UIEvent) -> None:
        """发送事件
        
        Args:
            event: UI事件
        """
        pass
    
    @abstractmethod
    def update_progress(self, progress: float, message: str = "") -> None:
        """更新进度
        
        Args:
            progress: 进度值 (0.0-1.0)
            message: 进度消息
        """
        pass
    
    @abstractmethod
    def show_error(self, message: str, details: str = "") -> None:
        """显示错误信息
        
        Args:
            message: 错误消息
            details: 错误详情
        """
        pass
    
    @abstractmethod
    def show_warning(self, message: str, details: str = "") -> None:
        """显示警告信息
        
        Args:
            message: 警告消息
            details: 警告详情
        """
        pass
    
    @abstractmethod
    def show_info(self, message: str, details: str = "") -> None:
        """显示信息
        
        Args:
            message: 信息内容
            details: 详细信息
        """
        pass
    
    @abstractmethod
    def ask_confirmation(self, message: str, title: str = "确认") -> bool:
        """询问确认
        
        Args:
            message: 确认消息
            title: 对话框标题
            
        Returns:
            bool: 用户是否确认
        """
        pass
    
    @abstractmethod
    def select_file(self, title: str = "选择文件", file_filter: str = "") -> Optional[str]:
        """选择文件
        
        Args:
            title: 对话框标题
            file_filter: 文件过滤器
            
        Returns:
            Optional[str]: 选择的文件路径，如果取消则返回None
        """
        pass
    
    @abstractmethod
    def select_directory(self, title: str = "选择目录") -> Optional[str]:
        """选择目录
        
        Args:
            title: 对话框标题
            
        Returns:
            Optional[str]: 选择的目录路径，如果取消则返回None
        """
        pass


class VideoPlayerInterface(ABC):
    """视频播放器接口"""
    
    @abstractmethod
    def load_video(self, video_path: str) -> bool:
        """加载视频"""
        pass
    
    @abstractmethod
    def play(self) -> None:
        """播放"""
        pass
    
    @abstractmethod
    def pause(self) -> None:
        """暂停"""
        pass
    
    @abstractmethod
    def stop(self) -> None:
        """停止"""
        pass
    
    @abstractmethod
    def seek(self, timestamp: float) -> None:
        """跳转到指定时间"""
        pass
    
    @abstractmethod
    def set_volume(self, volume: float) -> None:
        """设置音量 (0.0-1.0)"""
        pass
    
    @abstractmethod
    def get_position(self) -> float:
        """获取当前播放位置"""
        pass
    
    @abstractmethod
    def get_duration(self) -> float:
        """获取视频总时长"""
        pass
    
    @abstractmethod
    def is_playing(self) -> bool:
        """是否正在播放"""
        pass


class SegmentListInterface(ABC):
    """段落列表接口"""
    
    @abstractmethod
    def set_segments(self, segments: List[SegmentInfo]) -> None:
        """设置段落列表"""
        pass
    
    @abstractmethod
    def add_segment(self, segment: SegmentInfo) -> None:
        """添加段落"""
        pass
    
    @abstractmethod
    def remove_segment(self, segment_id: int) -> None:
        """移除段落"""
        pass
    
    @abstractmethod
    def update_segment(self, segment: SegmentInfo) -> None:
        """更新段落"""
        pass
    
    @abstractmethod
    def select_segment(self, segment_id: int) -> None:
        """选择段落"""
        pass
    
    @abstractmethod
    def get_selected_segment(self) -> Optional[SegmentInfo]:
        """获取选中的段落"""
        pass
    
    @abstractmethod
    def clear_selection(self) -> None:
        """清除选择"""
        pass
    
    @abstractmethod
    def filter_segments(self, filter_text: str) -> None:
        """过滤段落"""
        pass


class TextViewerInterface(ABC):
    """文本查看器接口"""
    
    @abstractmethod
    def set_text(self, text: str) -> None:
        """设置文本内容"""
        pass
    
    @abstractmethod
    def get_text(self) -> str:
        """获取文本内容"""
        pass
    
    @abstractmethod
    def set_editable(self, editable: bool) -> None:
        """设置是否可编辑"""
        pass
    
    @abstractmethod
    def highlight_text(self, start: int, end: int) -> None:
        """高亮文本"""
        pass
    
    @abstractmethod
    def clear_highlights(self) -> None:
        """清除高亮"""
        pass
    
    @abstractmethod
    def search_text(self, query: str, case_sensitive: bool = False) -> List[tuple]:
        """搜索文本"""
        pass


class ControlPanelInterface(ABC):
    """控制面板接口"""
    
    @abstractmethod
    def set_video_info(self, video_info: VideoInfo) -> None:
        """设置视频信息"""
        pass
    
    @abstractmethod
    def set_process_config(self, config: Dict[str, Any]) -> None:
        """设置处理配置"""
        pass
    
    @abstractmethod
    def get_process_config(self) -> Dict[str, Any]:
        """获取处理配置"""
        pass
    
    @abstractmethod
    def enable_controls(self, enabled: bool) -> None:
        """启用/禁用控件"""
        pass
    
    @abstractmethod
    def show_progress(self, visible: bool) -> None:
        """显示/隐藏进度条"""
        pass


# 导出的接口类型别名
UIManager = UIManagerInterface
VideoPlayer = VideoPlayerInterface
SegmentList = SegmentListInterface
TextViewer = TextViewerInterface
ControlPanel = ControlPanelInterface
