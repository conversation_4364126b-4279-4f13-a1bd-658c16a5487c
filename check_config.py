#!/usr/bin/env python3
"""检查配置文件"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config_manager
import shutil

def main():
    config_manager = get_config_manager()
    print(f"配置文件路径: {config_manager.config_file}")
    print(f"配置文件存在: {config_manager.config_file.exists()}")
    
    # 如果用户目录的配置文件不存在，复制项目根目录的配置文件
    if not config_manager.config_file.exists():
        project_config = Path("config.toml")
        if project_config.exists():
            print(f"复制配置文件: {project_config} -> {config_manager.config_file}")
            config_manager.config_file.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(project_config, config_manager.config_file)
            print("✅ 配置文件已复制")
        else:
            print("❌ 项目根目录的config.toml不存在")
            return
    
    # 重新加载配置
    config_manager.reload()
    
    # 检查Cloudreve配置
    print("\nCloudreve配置:")
    cloudreve_config = config_manager.get('file_uploader.uploaders.cloudreve', {})
    for key, value in cloudreve_config.items():
        if key == 'password':
            print(f"  {key}: {'已配置' if value else '未配置'}")
        else:
            print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
