"""VideoReader GUI应用启动器

负责启动和管理GUI界面应用。
"""

import sys
import os
import signal
from pathlib import Path
from typing import Optional

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt, QTimer
    from PySide6.QtGui import QIcon
    PYSIDE_AVAILABLE = True
except ImportError:
    PYSIDE_AVAILABLE = False

from function_interface import FunctionInterface
from common.logging import get_logger
from common.config import get_config
from common.exceptions import VideoReaderError, UIError


logger = get_logger(__name__)


class VideoReaderApp:
    """VideoReader GUI应用类"""
    
    def __init__(self, args):
        self.args = args
        self.logger = get_logger(__name__)
        self.qt_app: Optional[QApplication] = None
        self.function_interface: Optional[FunctionInterface] = None
        self.main_window = None
        self._shutdown_in_progress = False  # 防止重复关闭

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def initialize(self) -> bool:
        """初始化应用"""
        try:
            # 如果PySide6可用，创建Qt应用
            if PYSIDE_AVAILABLE:
                self.qt_app = QApplication(sys.argv)
                self.qt_app.setApplicationName("VideoReader")
                self.qt_app.setApplicationVersion("1.0.0")
                self.qt_app.setOrganizationName("VideoReader Team")

                # 设置应用图标
                self._set_application_icon()

                # 设置样式
                self._load_stylesheet()
            else:
                self.logger.info("PySide6不可用，使用Tkinter界面")
            
            # 创建功能接口
            self.function_interface = FunctionInterface()
            
            # 初始化功能接口
            if not self.function_interface.initialize("gui"):
                raise VideoReaderError("功能接口初始化失败")
            
            # 获取主窗口
            self.main_window = self.function_interface._ui_manager

            # 设置进度和错误回调
            self.function_interface.progress_callback = self._on_progress_update
            self.function_interface.error_callback = self._on_error_occurred

            # 如果是PySide6版本，设置Qt应用退出处理
            if PYSIDE_AVAILABLE and self.qt_app:
                self.qt_app.aboutToQuit.connect(self._on_application_quit)

                # 设置定时器处理Ctrl+C
                self.timer = QTimer()
                self.timer.timeout.connect(lambda: None)
                self.timer.start(100)
            
            self.logger.info("GUI应用初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"GUI应用初始化失败: {e}")
            self._show_error("初始化失败", str(e))
            return False
    
    def run(self) -> int:
        """运行应用"""
        try:
            if not self.initialize():
                return 1
            
            # 启动应用
            self.function_interface.start_application("gui")
            
            # 如果命令行指定了输入文件，自动加载
            if hasattr(self.args, 'input') and self.args.input:
                self.function_interface.load_video(self.args.input)
            
            self.logger.info("GUI应用开始运行")

            # 根据界面类型进入事件循环
            if PYSIDE_AVAILABLE and self.qt_app:
                # 进入Qt事件循环
                return self.qt_app.exec_()
            else:
                # 进入Tkinter事件循环
                if hasattr(self.main_window, 'run_main_loop'):
                    self.main_window.run_main_loop()
                return 0
            
        except Exception as e:
            self.logger.error(f"GUI应用运行失败: {e}")
            self._show_error("运行失败", str(e))
            return 1
    
    def shutdown(self):
        """关闭应用"""
        # 防止重复关闭
        if self._shutdown_in_progress:
            return

        self._shutdown_in_progress = True

        try:
            # 使用print而不是logger，避免在关闭过程中的日志问题
            print("开始关闭GUI应用")

            # 先关闭功能接口
            if self.function_interface:
                self.function_interface.shutdown_application()
                self.function_interface = None

            # 关闭主窗口
            if self.main_window:
                try:
                    self.main_window.close()
                except:
                    pass
                self.main_window = None

            # 不要在这里调用quit()，因为这个方法可能是由aboutToQuit信号触发的
            # Qt应用会自然退出

            print("GUI应用关闭完成")

        except Exception as e:
            print(f"关闭GUI应用失败: {e}")
            # 在异常情况下，确保应用能够退出
            if self.qt_app:
                try:
                    self.qt_app.exit(1)
                except:
                    pass
    
    def _set_application_icon(self):
        """设置应用图标"""
        try:
            # 查找图标文件
            icon_paths = [
                Path(__file__).parent / "ui" / "icons" / "app.ico",
                Path(__file__).parent / "ui" / "icons" / "app.png",
                Path(__file__).parent / "resources" / "app.ico",
                Path(__file__).parent / "resources" / "app.png"
            ]
            
            for icon_path in icon_paths:
                if icon_path.exists():
                    icon = QIcon(str(icon_path))
                    self.qt_app.setWindowIcon(icon)
                    self.logger.debug(f"设置应用图标: {icon_path}")
                    break
            else:
                self.logger.debug("未找到应用图标文件")
                
        except Exception as e:
            self.logger.warning(f"设置应用图标失败: {e}")
    
    def _load_stylesheet(self):
        """加载样式表"""
        try:
            style_path = Path(__file__).parent / "ui" / "styles" / "main.qss"
            if style_path.exists():
                with open(style_path, 'r', encoding='utf-8') as f:
                    stylesheet = f.read()
                self.qt_app.setStyleSheet(stylesheet)
                self.logger.debug("样式表加载成功")
            else:
                self.logger.debug("样式表文件不存在，使用默认样式")
                
        except Exception as e:
            self.logger.warning(f"加载样式表失败: {e}")
    
    def _on_progress_update(self, progress: float, message: str):
        """进度更新回调"""
        if self.main_window:
            self.main_window.update_progress(progress, message)
    
    def _on_error_occurred(self, message: str, details: str):
        """错误发生回调"""
        if self.main_window:
            self.main_window.show_error(message, details)
        else:
            self._show_error(message, details)
    
    def _show_error(self, message: str, details: str = ""):
        """显示错误对话框"""
        try:
            if PYSIDE_AVAILABLE and self.qt_app:
                QMessageBox.critical(None, "错误", message)
            else:
                print(f"错误: {message}")
                if details:
                    print(f"详情: {details}")
        except Exception as e:
            print(f"显示错误对话框失败: {e}")
            print(f"原始错误: {message}")
    
    def _on_application_quit(self):
        """应用退出处理"""
        # 防止重复关闭
        if self._shutdown_in_progress:
            return

        self._shutdown_in_progress = True

        try:
            print("Qt应用即将退出，开始清理资源")

            # 只进行资源清理，不调用quit()
            if self.function_interface:
                self.function_interface.shutdown_application()
                self.function_interface = None

            # 关闭主窗口
            if self.main_window:
                try:
                    self.main_window.close()
                except:
                    pass
                self.main_window = None

            print("资源清理完成")

        except Exception as e:
            print(f"资源清理失败: {e}")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"接收到信号 {signum}，准备退出")
        if self.qt_app and not self._shutdown_in_progress:
            self.qt_app.quit()


def main(args) -> int:
    """GUI应用主函数"""
    try:
        # 设置高DPI支持
        if PYSIDE_AVAILABLE:
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建并运行应用
        app = VideoReaderApp(args)
        return app.run()
        
    except Exception as e:
        logger.error(f"GUI应用启动失败: {e}")
        print(f"GUI应用启动失败: {e}")
        
        if not PYSIDE_AVAILABLE:
            print("\n提示: PySide6未安装，请使用以下命令安装:")
            print("pip install PySide6")
            print("\n或者使用CLI模式:")
            print("python main.py --cli")
        
        return 1


if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--debug', action='store_true')
    parser.add_argument('--input', type=str)
    args = parser.parse_args()
    
    sys.exit(main(args))
