# VideoReader 项目需求文档

## 1. 项目概述

### 1.1 项目背景
视频作为信息载体，虽然直观生动，但信息密度相对较低，观看效率不高。特别是学习类视频，用户往往需要反复观看、记录要点，传统的视频播放方式无法满足高效学习的需求。

### 1.2 项目目标
开发一个视频阅读器，将视频转换为以文本、图片为主，视频为辅的阅读方式，提高信息获取效率，特别适用于学习类视频的快速浏览和重点回顾。

### 1.3 核心价值
- 提高视频内容的阅读效率
- 支持快速定位和回顾重点内容
- 提供多种解析方式适应不同类型的视频
- 保存解析结果，避免重复处理

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 视频加载功能
- **功能描述**：支持加载本地视频文件
- **支持格式**：MP4、AVI、MOV、MKV等主流视频格式
- **文件大小**：支持大文件加载（建议最大支持2GB）
- **预览功能**：加载后显示视频基本信息（时长、分辨率、帧率等）

#### 2.1.2 语音识别功能
- **功能描述**：将视频中的语音转换为文本
- **技术要求**：
  - 支持中文、英文语音识别
  - 识别准确率≥85%
  - 输出带时间戳的文本
- **输出格式**：
  ```json
  {
    "segments": [
      {
        "start_time": 0.0,
        "end_time": 5.2,
        "text": "欢迎来到今天的课程"
      }
    ]
  }
  ```

#### 2.1.3 视频解析功能
基于解析器基类设计，支持多种解析方式：

##### 2.1.3.1 画面变化分段解析器
- **适用场景**：学习视频、演示视频
- **解析原理**：
  - 计算相邻帧之间的差异度
  - 设置变化阈值（可配置）
  - 当变化超过阈值时进行分段
- **参数配置**：
  - 变化阈值：0-100%（默认30%）
  - 最小段长度：10秒（避免过度分割）
  - 最大段长度：300秒（避免段落过长）

##### 2.1.3.2 文本长度分段解析器
- **适用场景**：讲座视频、访谈视频
- **解析原理**：
  - 按照文本字符数进行分段
  - 在句子结束处进行分割（避免截断句子）
- **参数配置**：
  - 分段长度：100-500字（默认200字）
  - 分割策略：句子边界优先

##### 2.1.3.3 时间固定分段解析器
- **适用场景**：新闻视频、短视频
- **解析原理**：按固定时间间隔分段
- **参数配置**：
  - 分段时长：30秒-600秒（默认120秒）

##### 2.1.3.4 静音分段解析器
- **适用场景**：会议录音、课程视频
- **解析原理**：根据音频静音段进行分割
- **参数配置**：
  - 静音阈值：音量百分比（默认5%）
  - 最小静音时长：1-10秒（默认2秒）

#### 2.1.4 界面显示功能

##### 2.1.4.1 左侧文本段列表
- **显示内容**：
  - 段落序号
  - 开始时间 - 结束时间
  - 文本摘要（前50字）
  - 段落时长
- **交互功能**：
  - 点击跳转到对应段落
  - 支持搜索功能
  - 支持筛选功能（按时长、关键词等）

##### 2.1.4.2 右侧内容显示区域
**上半部分（媒体显示区）**：
- 默认显示：该段落的关键帧截图
- 播放模式：显示视频播放器
- 支持全屏显示
- 支持截图保存

**下半部分（文本显示区）**：
- 显示当前段落的完整文本
- 支持文本编辑和标注
- 支持关键词高亮
- 支持字体大小调整

#### 2.1.5 播放控制功能
- **基础控制**：播放/暂停、停止
- **进度控制**：进度条拖拽、快进/快退
- **显示切换**：图片模式/视频模式切换
- **速度控制**：0.5x、1x、1.25x、1.5x、2x播放速度
- **音量控制**：音量调节、静音
- **循环播放**：单段循环、全部循环

### 2.2 辅助功能

#### 2.2.1 元数据管理
- **保存功能**：将解析结果保存为JSON元数据文件
- **加载功能**：通过元数据文件快速加载已解析的视频
- **元数据结构**：
  ```json
  {
    "video_info": {
      "file_path": "path/to/video.mp4",
      "duration": 3600.0,
      "resolution": "1920x1080",
      "file_size": 1024000000
    },
    "parse_info": {
      "parser_type": "scene_change",
      "parse_time": "2024-01-01T12:00:00",
      "parameters": {
        "threshold": 30,
        "min_length": 10
      }
    },
    "segments": [
      {
        "id": 1,
        "start_time": 0.0,
        "end_time": 45.2,
        "text": "完整的文本内容...",
        "summary": "文本摘要...",
        "key_frame": "path/to/keyframe.jpg"
      }
    ],
    "audio_transcript": "path/to/transcript.json"
  }
  ```

#### 2.2.2 导出功能
- **文本导出**：导出为TXT、DOCX、PDF格式
- **摘要导出**：导出段落摘要和时间轴
- **截图导出**：批量导出关键帧截图
- **字幕导出**：导出SRT、VTT字幕文件

#### 2.2.3 搜索功能
- **全文搜索**：在所有文本段中搜索关键词
- **时间范围搜索**：在指定时间范围内搜索
- **正则表达式搜索**：支持高级搜索模式

## 3. 技术需求

### 3.1 开发环境
- **编程语言**：Python 3.8+
- **界面框架**：PyQt5/PyQt6 或 Tkinter
- **音视频处理**：FFmpeg、OpenCV
- **语音识别**：Whisper、百度语音API、讯飞语音API

### 3.2 性能要求
- **启动时间**：≤3秒
- **视频加载时间**：≤5秒（1GB文件）
- **语音识别速度**：≥实时播放速度的2倍
- **界面响应时间**：≤200ms
- **内存占用**：≤2GB（处理2GB视频文件时）

### 3.3 兼容性要求
- **操作系统**：Windows 10+、macOS 10.14+、Ubuntu 18.04+
- **Python版本**：3.8-3.11
- **视频格式**：MP4、AVI、MOV、MKV、WMV、FLV

## 4. 架构设计

### 4.1 项目结构
```
VideoReader/
├── src/
│   ├── core/              # 核心抽象层
│   │   ├── __init__.py
│   │   ├── base_parser.py # 解析器基类
│   │   ├── video_processor.py # 视频处理核心
│   │   └── audio_processor.py # 音频处理核心
│   ├── business/          # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── parsers/       # 具体解析器实现
│   │   │   ├── scene_change_parser.py
│   │   │   ├── text_length_parser.py
│   │   │   ├── time_fixed_parser.py
│   │   │   └── silence_parser.py
│   │   ├── speech_recognition.py # 语音识别
│   │   ├── metadata_manager.py   # 元数据管理
│   │   └── export_manager.py     # 导出功能
│   ├── ui/               # 用户界面层
│   │   ├── __init__.py
│   │   ├── components/   # 界面组件
│   │   │   ├── video_player.py
│   │   │   ├── segment_list.py
│   │   │   ├── text_display.py
│   │   │   └── control_panel.py
│   │   ├── main_window.py
│   │   └── styles/       # 样式文件
│   │       └── main.qss
│   ├── utils/            # 工具和辅助功能
│   │   ├── __init__.py
│   │   ├── file_utils.py
│   │   ├── image_utils.py
│   │   └── config.py
│   ├── main.py           # 程序入口
│   ├── app.py            # 可视化界面入口
│   ├── cli.py            # 命令行入口
│   └── function_interface.py # 功能接口层
├── tests/                # 测试文件
├── doc/                  # 项目文档
├── requirements.txt      # 依赖列表
└── README.md
```

### 4.2 核心类设计

#### 4.2.1 解析器基类
```python
class BaseParser:
    def __init__(self, config):
        pass
    
    def parse(self, video_path, transcript):
        """解析视频，返回分段结果"""
        pass
    
    def get_config(self):
        """获取解析器配置"""
        pass
    
    def validate_config(self, config):
        """验证配置参数"""
        pass
```

#### 4.2.2 视频处理器
```python
class VideoProcessor:
    def load_video(self, video_path):
        """加载视频文件"""
        pass
    
    def extract_frames(self, start_time, end_time):
        """提取指定时间段的帧"""
        pass
    
    def get_key_frame(self, start_time, end_time):
        """获取关键帧"""
        pass
```

## 5. 用户界面设计

### 5.1 主界面布局
- **顶部**：菜单栏和工具栏
- **左侧**：文本段列表（宽度30%）
- **右侧**：内容显示区域（宽度70%）
  - **上半部分**：媒体显示区（高度60%）
  - **下半部分**：文本显示区（高度40%）
- **底部**：状态栏

### 5.2 交互设计
- **拖拽支持**：支持拖拽视频文件到界面
- **快捷键**：常用功能支持快捷键操作
- **右键菜单**：提供上下文相关的操作选项
- **进度指示**：长时间操作显示进度条

## 6. 开发计划

### 6.1 第一阶段：核心功能开发（4周）
- 搭建项目架构
- 实现视频加载和基础播放功能
- 实现语音识别功能
- 实现画面变化分段解析器

### 6.2 第二阶段：界面和交互（3周）
- 开发主界面和各个组件
- 实现文本段列表和内容显示
- 实现播放控制功能
- 完善用户交互体验

### 6.3 第三阶段：扩展功能（3周）
- 实现其他解析器
- 实现元数据管理
- 实现导出功能
- 实现搜索功能

### 6.4 第四阶段：测试和优化（2周）
- 单元测试和集成测试
- 性能优化
- 用户体验优化
- 文档完善

## 7. 风险评估

### 7.1 技术风险
- **语音识别准确率**：可能因为音质、口音等因素影响识别效果
- **视频格式兼容性**：部分特殊格式可能无法正确处理
- **性能问题**：大文件处理可能导致内存不足或处理缓慢

### 7.2 解决方案
- 提供多种语音识别引擎选择
- 使用FFmpeg确保广泛的格式支持
- 实现分块处理和缓存机制

## 8. 技术选型详细说明

### 8.1 语音识别技术选择
**推荐方案：OpenAI Whisper**
- 优点：开源免费、支持多语言、准确率高、本地处理
- 缺点：首次下载模型较大、GPU加速需要额外配置
- 备选方案：百度语音API、讯飞语音API（在线服务）

### 8.2 视频处理技术
**主要工具：FFmpeg + OpenCV**
- FFmpeg：视频解码、音频提取、格式转换
- OpenCV：帧提取、图像处理、场景变化检测
- PIL/Pillow：图像处理和缩略图生成

### 8.3 界面框架选择
**推荐方案：PyQt6**
- 优点：功能强大、跨平台、界面美观、文档完善
- 缺点：许可证限制（商业使用需付费）
- 备选方案：Tkinter（Python内置）、PyQt5

### 8.4 数据存储
**元数据存储：JSON文件**
- 优点：人类可读、易于调试、无需额外数据库
- 结构化存储视频解析结果和配置信息

## 9. 开发规范和约定

### 9.1 代码规范
- 遵循PEP 8 Python代码规范
- 使用类型提示（Type Hints）
- 函数和类必须有文档字符串
- 变量和函数命名使用英文，注释使用中文

### 9.2 文件命名规范
- Python文件：小写字母+下划线（snake_case）
- 类名：大驼峰命名（PascalCase）
- 函数名：小写字母+下划线（snake_case）
- 常量：全大写字母+下划线

### 9.3 异常处理规范
- 所有可能出错的操作都要有异常处理
- 自定义异常类继承自内置异常
- 记录详细的错误日志
- 向用户显示友好的错误信息

## 10. 测试策略

### 10.1 单元测试
- 每个核心类都要有对应的测试文件
- 测试覆盖率要求≥80%
- 使用pytest框架进行测试

### 10.2 集成测试
- 测试各模块之间的协作
- 测试完整的视频处理流程
- 测试界面与后端的交互

### 10.3 性能测试
- 测试不同大小视频文件的处理性能
- 测试内存使用情况
- 测试界面响应速度

## 11. 部署和分发

### 11.1 依赖管理
- 使用requirements.txt管理Python依赖
- 提供conda环境配置文件
- 说明FFmpeg等外部依赖的安装方法

### 11.2 打包分发
- 使用PyInstaller打包为可执行文件
- 提供Windows、macOS、Linux三个平台的版本
- 制作安装程序（Windows使用NSIS，macOS使用DMG）

## 12. 后续扩展计划

### 12.1 AI增强功能
- 智能摘要生成：使用大语言模型生成段落摘要
- 关键词自动提取：基于TF-IDF或BERT的关键词提取
- 内容分类和标签：自动为视频内容打标签

### 12.2 协作功能
- 多用户标注：支持多人对同一视频进行标注
- 分享和评论：支持分享解析结果和添加评论
- 云端同步：将解析结果同步到云端存储

### 12.3 移动端支持
- 开发移动端应用：使用React Native或Flutter
- 跨平台数据同步：确保桌面端和移动端数据一致

### 12.4 高级功能
- 批量处理：支持批量处理多个视频文件
- 自定义解析器：允许用户编写自定义解析逻辑
- 插件系统：支持第三方插件扩展功能
- 多语言支持：界面支持多种语言切换
