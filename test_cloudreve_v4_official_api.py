#!/usr/bin/env python3
"""按照Cloudreve V4官方API测试"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def test_cloudreve_v4_ping():
    """测试Cloudreve V4 ping端点"""
    print("=== 测试Cloudreve V4 Ping ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    
    try:
        import requests
        
        ping_url = f"{base_url}/api/v4/site/ping"
        print(f"Ping URL: {ping_url}")
        
        response = requests.get(ping_url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"JSON解析: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return True
            except:
                print("响应不是JSON格式")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Ping测试失败: {e}")
        return False


def test_cloudreve_v4_login():
    """测试Cloudreve V4登录API"""
    print("\n=== 测试Cloudreve V4登录 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    try:
        import requests
        
        # 根据Cloudreve V4 API文档，登录端点应该是 /api/v4/user/session
        login_url = f"{base_url}/api/v4/user/session"
        print(f"登录URL: {login_url}")
        
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
        })
        
        # 根据API文档，登录数据格式
        login_data = {
            'userName': username,
            'Password': password
        }
        
        print(f"登录数据: {login_data}")
        
        response = session.post(login_url, json=login_data, timeout=30)
        
        print(f"登录响应状态: {response.status_code}")
        print(f"登录响应头: {dict(response.headers)}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"登录成功! JSON响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                # 检查响应中的token
                if 'data' in result and isinstance(result['data'], dict):
                    token = result['data'].get('token')
                    if token:
                        print(f"获取到Token: {token[:20]}...")
                        return session, token
                
                return session, None
                
            except Exception as e:
                print(f"JSON解析失败: {e}")
                return None, None
        else:
            print(f"登录失败: {response.status_code}")
            return None, None
            
    except Exception as e:
        print(f"登录测试异常: {e}")
        return None, None


def test_cloudreve_v4_upload_session(session, token):
    """测试创建上传会话"""
    print("\n=== 测试创建上传会话 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    
    try:
        # 设置认证头
        if token:
            session.headers.update({'Authorization': f'Bearer {token}'})
        
        # 创建测试文件信息
        test_file_info = {
            'path': '/test_upload.txt',
            'size': 100,
            'name': 'test_upload.txt',
            'policy_id': 1,  # 使用默认存储策略
            'last_modified': 1640995200,  # 时间戳
            'mime_type': 'text/plain'
        }
        
        upload_url = f"{base_url}/api/v4/file/upload"
        print(f"上传会话URL: {upload_url}")
        print(f"文件信息: {json.dumps(test_file_info, indent=2)}")
        
        response = session.post(upload_url, json=test_file_info, timeout=30)
        
        print(f"上传会话响应状态: {response.status_code}")
        print(f"上传会话响应头: {dict(response.headers)}")
        print(f"上传会话响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"上传会话创建成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result.get('data', {})
            except Exception as e:
                print(f"JSON解析失败: {e}")
        
        return None
        
    except Exception as e:
        print(f"创建上传会话异常: {e}")
        return None


def test_cloudreve_v4_file_upload(session, upload_session_data):
    """测试文件上传"""
    print("\n=== 测试文件上传 ===")
    
    if not upload_session_data:
        print("没有上传会话数据，跳过文件上传测试")
        return None
    
    try:
        # 创建测试文件内容
        test_content = b"This is a test file for Cloudreve V4 upload"
        
        session_id = upload_session_data.get('sessionID')
        if not session_id:
            print("没有找到sessionID")
            return None
        
        print(f"上传会话ID: {session_id}")
        
        # 上传文件块
        base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
        chunk_url = f"{base_url}/api/v4/file/upload/chunk"
        
        files = {
            'file': ('test_upload.txt', test_content, 'text/plain')
        }
        
        data = {
            'sessionID': session_id,
            'index': 0
        }
        
        # 临时移除Content-Type头，让requests自动设置multipart/form-data
        original_content_type = session.headers.pop('Content-Type', None)
        
        print(f"上传块URL: {chunk_url}")
        print(f"上传数据: {data}")
        
        response = session.post(chunk_url, files=files, data=data, timeout=60)
        
        # 恢复Content-Type头
        if original_content_type:
            session.headers['Content-Type'] = original_content_type
        
        print(f"上传块响应状态: {response.status_code}")
        print(f"上传块响应内容: {response.text}")
        
        if response.status_code == 200:
            # 完成上传
            finish_url = f"{base_url}/api/v4/file/upload/finish"
            finish_data = {'sessionID': session_id}
            
            finish_response = session.post(finish_url, json=finish_data, timeout=30)
            
            print(f"完成上传响应状态: {finish_response.status_code}")
            print(f"完成上传响应内容: {finish_response.text}")
            
            if finish_response.status_code == 200:
                try:
                    result = finish_response.json()
                    print(f"文件上传完成: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    # 获取文件信息
                    file_data = result.get('data', {})
                    file_id = file_data.get('id')
                    if file_id:
                        # 尝试创建分享链接
                        return test_create_share_link(session, file_id)
                    
                except Exception as e:
                    print(f"JSON解析失败: {e}")
        
        return None
        
    except Exception as e:
        print(f"文件上传异常: {e}")
        return None


def test_create_share_link(session, file_id):
    """测试创建分享链接"""
    print(f"\n=== 测试创建分享链接 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    
    try:
        # 创建分享链接
        share_url = f"{base_url}/api/v4/share"
        share_data = {
            'id': [file_id],
            'is_dir': False,
            'password': '',
            'downloads': -1,  # 无限下载
            'expire': 3600 * 24 * 7  # 7天有效期
        }
        
        print(f"分享链接URL: {share_url}")
        print(f"分享数据: {json.dumps(share_data, indent=2)}")
        
        response = session.post(share_url, json=share_data, timeout=30)
        
        print(f"分享链接响应状态: {response.status_code}")
        print(f"分享链接响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"分享链接创建成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                share_data = result.get('data', {})
                share_key = share_data.get('key')
                if share_key:
                    public_url = f"{base_url}/s/{share_key}"
                    print(f"✅ 公开访问URL: {public_url}")
                    return public_url
                    
            except Exception as e:
                print(f"JSON解析失败: {e}")
        
        return None
        
    except Exception as e:
        print(f"创建分享链接异常: {e}")
        return None


def main():
    """主函数"""
    print("Cloudreve V4官方API测试")
    print("=" * 60)
    
    # 1. 测试ping
    if not test_cloudreve_v4_ping():
        print("❌ Ping测试失败，停止后续测试")
        return
    
    print("✅ Ping测试成功")
    
    # 2. 测试登录
    session, token = test_cloudreve_v4_login()
    if not session:
        print("❌ 登录失败，停止后续测试")
        return
    
    print("✅ 登录成功")
    
    # 3. 测试创建上传会话
    upload_session_data = test_cloudreve_v4_upload_session(session, token)
    if not upload_session_data:
        print("❌ 创建上传会话失败，停止后续测试")
        return
    
    print("✅ 创建上传会话成功")
    
    # 4. 测试文件上传
    public_url = test_cloudreve_v4_file_upload(session, upload_session_data)
    if public_url:
        print(f"\n🎉 完整测试成功!")
        print(f"文件公开URL: {public_url}")
        
        # 验证公开URL可访问性
        try:
            import requests
            verify_response = requests.get(public_url, timeout=10)
            if verify_response.status_code == 200:
                print("✅ 公开URL可以正常访问")
            else:
                print(f"⚠️ 公开URL访问异常: {verify_response.status_code}")
        except Exception as e:
            print(f"⚠️ 公开URL验证失败: {e}")
    else:
        print("❌ 文件上传失败")
    
    print("\n" + "=" * 60)
    print("测试完成!")


if __name__ == "__main__":
    main()
