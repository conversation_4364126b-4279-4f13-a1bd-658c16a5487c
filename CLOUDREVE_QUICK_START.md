# Cloudreve + Paraformer 快速开始指南

本指南帮助您快速配置和使用Cloudreve作为文件存储，配合阿里云Paraformer进行语音识别。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install requests dashscope tomli tomli-w
```

### 2. 配置方式（推荐使用TOML配置文件）

#### 方法1：使用配置向导（推荐）
```bash
python scripts/create_config.py
```

#### 方法2：手动创建配置文件
创建或编辑 `~/.videoreader/config.toml` 文件：

```toml
[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"
model = "paraformer-v2"

[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
username = "your-username"
password = "your-password"
# 或者使用访问令牌（推荐）
# token = "your-access-token"
```

#### 方法3：使用环境变量（备选）

##### Windows
```cmd
set DASHSCOPE_API_KEY=your-dashscope-api-key
set CLOUDREVE_BASE_URL=http://your-cloudreve-server.com:5212
set CLOUDREVE_USERNAME=your-username
set CLOUDREVE_PASSWORD=your-password
```

##### Linux/macOS
```bash
export DASHSCOPE_API_KEY='your-dashscope-api-key'
export CLOUDREVE_BASE_URL='http://your-cloudreve-server.com:5212'
export CLOUDREVE_USERNAME='your-username'
export CLOUDREVE_PASSWORD='your-password'
```

### 3. 测试配置

```bash
# 测试TOML配置功能
python test_toml_config.py

# 测试Cloudreve上传器
python test_cloudreve_uploader.py
```

### 4. 开始使用

```python
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer

# 创建识别器
recognizer = ParaformerRecognizer(model='paraformer-v2')

# 直接识别本地视频文件（会自动上传到Cloudreve）
segments = recognizer.recognize('your-video.mp4', language='zh')

# 查看识别结果
for segment in segments:
    print(f"[{segment.time_range_str}] {segment.text}")
```

## 📋 配置清单

### 必需配置
- [ ] 阿里云DashScope API密钥
- [ ] Cloudreve服务器地址
- [ ] Cloudreve用户认证信息

### 可选配置
- [ ] 自定义上传路径
- [ ] 访问令牌认证（推荐生产环境）
- [ ] HTTPS配置
- [ ] 自定义超时和重试设置

### 配置优先级
1. 代码中直接传递的参数
2. TOML配置文件 (`~/.videoreader/config.toml`)
3. 环境变量

## 🔧 故障排除

### 常见问题

1. **连接失败**
   ```
   ✗ Cloudreve上传器不可用
   ```
   - 检查CLOUDREVE_BASE_URL是否正确
   - 确认Cloudreve服务器正在运行
   - 测试网络连接

2. **认证失败**
   ```
   ✗ Cloudreve认证失败
   ```
   - 检查用户名密码是否正确
   - 确认用户有上传权限
   - 考虑使用访问令牌

3. **上传失败**
   ```
   ✗ 文件上传失败
   ```
   - 检查文件是否存在
   - 确认存储空间足够
   - 检查文件大小限制

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 更多资源

- [详细集成指南](doc/cloudreve_integration_guide.md)
- [配置示例](cloudreve_config_example.py)
- [完整测试](test_paraformer_cloudreve_integration.py)

## 💡 提示

1. **生产环境建议使用访问令牌而不是用户名密码**
2. **确保Cloudreve服务器配置了正确的CORS策略**
3. **大文件上传可能需要较长时间，请耐心等待**
4. **定期清理Cloudreve中的临时文件**

## 🎯 下一步

配置完成后，您可以：
- 使用图形界面进行视频处理
- 通过命令行批量处理文件
- 集成到您的自动化工作流中

---

如有问题，请查看详细文档或提交Issue。
