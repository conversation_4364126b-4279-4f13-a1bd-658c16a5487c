#!/usr/bin/env python3
"""模拟浏览器访问Cloudreve"""

import sys
import json
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def simulate_browser_login():
    """模拟浏览器登录流程"""
    print("=== 模拟浏览器登录Cloudreve ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    try:
        import requests
        
        # 创建会话，模拟浏览器
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 1. 访问主页，获取可能的CSRF token或其他信息
        print(f"1. 访问主页: {base_url}")
        response = session.get(base_url, timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 查找可能的API端点或配置信息
            html_content = response.text
            
            # 查找JavaScript中的API配置
            api_patterns = [
                r'api["\']?\s*:\s*["\']([^"\']+)["\']',
                r'baseURL["\']?\s*:\s*["\']([^"\']+)["\']',
                r'apiUrl["\']?\s*:\s*["\']([^"\']+)["\']',
                r'/api/[^"\'\\s]+',
                r'axios\.defaults\.baseURL\s*=\s*["\']([^"\']+)["\']'
            ]
            
            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                found_apis.update(matches)
            
            if found_apis:
                print("   找到可能的API端点:")
                for api in found_apis:
                    print(f"     {api}")
            
            # 查找可能的配置信息
            if 'window.' in html_content:
                # 提取window对象中的配置
                window_configs = re.findall(r'window\.(\w+)\s*=\s*({[^}]+}|"[^"]*")', html_content)
                if window_configs:
                    print("   找到window配置:")
                    for name, value in window_configs[:5]:  # 只显示前5个
                        print(f"     window.{name} = {value[:100]}...")
        
        # 2. 尝试查找真实的API端点
        print("\n2. 查找真实API端点...")
        
        # 检查常见的API路径
        potential_apis = [
            f"{base_url}/api",
            f"{base_url}/api/v1",
            f"{base_url}/api/v2", 
            f"{base_url}/api/v3",
            f"{base_url}/api/v4"
        ]
        
        for api_base in potential_apis:
            try:
                # 测试OPTIONS请求（CORS预检）
                response = session.options(f"{api_base}/user/session", timeout=10)
                if response.status_code != 404:
                    print(f"   找到API基础路径: {api_base} (OPTIONS {response.status_code})")
                    
                    # 测试实际的登录端点
                    login_data = {'username': username, 'password': password}
                    session.headers['Content-Type'] = 'application/json'
                    response = session.post(f"{api_base}/user/session", json=login_data, timeout=30)
                    print(f"     登录测试: {response.status_code} - {response.text[:100]}")
                    
            except Exception as e:
                continue
        
        # 3. 尝试直接访问可能的管理界面
        print("\n3. 尝试访问管理界面...")
        admin_paths = [
            "/admin",
            "/dashboard", 
            "/login",
            "/signin",
            "/#/login",
            "/#/admin"
        ]
        
        for path in admin_paths:
            try:
                url = f"{base_url}{path}"
                response = session.get(url, timeout=10)
                if response.status_code == 200 and 'login' in response.text.lower():
                    print(f"   找到登录页面: {url}")
                    
                    # 查找登录表单
                    form_action = re.search(r'<form[^>]*action=["\']([^"\']*)["\']', response.text, re.IGNORECASE)
                    if form_action:
                        print(f"     表单action: {form_action.group(1)}")
                        
            except Exception as e:
                continue
        
        # 4. 尝试WebDAV接口（Cloudreve支持WebDAV）
        print("\n4. 测试WebDAV接口...")
        webdav_paths = [
            "/dav",
            "/webdav",
            "/remote.php/dav",
            "/remote.php/webdav"
        ]
        
        for path in webdav_paths:
            try:
                url = f"{base_url}{path}"
                # WebDAV通常需要PROPFIND方法
                response = session.request('PROPFIND', url, timeout=10)
                if response.status_code in [207, 401]:  # 207=Multi-Status, 401=Unauthorized
                    print(f"   找到WebDAV端点: {url} (状态码: {response.status_code})")
                    
                    if response.status_code == 401:
                        # 尝试基本认证
                        from requests.auth import HTTPBasicAuth
                        auth_response = session.request('PROPFIND', url, 
                                                      auth=HTTPBasicAuth(username, password), 
                                                      timeout=10)
                        print(f"     认证后状态: {auth_response.status_code}")
                        
            except Exception as e:
                continue
                
    except ImportError:
        print("❌ 需要安装requests库")


def check_cloudreve_version():
    """检查Cloudreve版本信息"""
    print("\n=== 检查Cloudreve版本 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    
    try:
        import requests
        
        session = requests.Session()
        
        # 检查响应头中的版本信息
        response = session.get(base_url, timeout=10)
        
        print("响应头信息:")
        for header, value in response.headers.items():
            if any(keyword in header.lower() for keyword in ['server', 'version', 'powered', 'x-']):
                print(f"  {header}: {value}")
        
        # 检查HTML中的版本信息
        html = response.text
        version_patterns = [
            r'version["\']?\s*:\s*["\']([^"\']+)["\']',
            r'Cloudreve\s+v?([0-9.]+)',
            r'version\s*=\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in version_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                print(f"找到版本信息: {matches}")
                
    except Exception as e:
        print(f"检查版本失败: {e}")


def main():
    """主函数"""
    print("Cloudreve浏览器模拟测试")
    print("=" * 60)
    
    simulate_browser_login()
    check_cloudreve_version()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n分析结果:")
    print("1. 如果找到了API端点，我们可以更新代码使用正确的路径")
    print("2. 如果只有WebDAV，我们可以实现WebDAV上传")
    print("3. 如果都没有，可能需要联系管理员启用API")


if __name__ == "__main__":
    main()
