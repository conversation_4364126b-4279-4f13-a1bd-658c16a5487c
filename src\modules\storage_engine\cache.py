"""缓存管理模块

负责管理视频处理结果的缓存，提高重复处理的效率。
"""

import os
import json
import hashlib
import pickle
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import asdict

from .models import CacheInfo, CacheEntry
from core.models import ProcessResult, VideoInfo
from common.exceptions import StorageError
from common.logging import get_logger
from common.config import get_config


logger = get_logger(__name__)


class CacheManager:
    """缓存管理器
    
    负责管理视频处理结果的缓存存储和检索。
    """
    
    def __init__(self, cache_dir: Optional[str] = None):
        self.cache_dir = Path(cache_dir or get_config('paths.cache_dir', './cache'))
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = get_logger(__name__)
        self.max_cache_size = get_config('cache.max_size_mb', 1000) * 1024 * 1024  # MB to bytes
        self.max_cache_age = get_config('cache.max_age_days', 30) * 24 * 3600  # days to seconds
        
        # 缓存索引文件
        self.index_file = self.cache_dir / 'cache_index.json'
        self.cache_index = self._load_cache_index()
        
        self.logger.info(f"缓存管理器初始化完成: {self.cache_dir}")
    
    def _load_cache_index(self) -> Dict[str, CacheEntry]:
        """加载缓存索引"""
        try:
            if self.index_file.exists():
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return {k: CacheEntry.from_dict(v) for k, v in data.items()}
            return {}
        except Exception as e:
            self.logger.warning(f"加载缓存索引失败: {e}")
            return {}
    
    def _save_cache_index(self) -> None:
        """保存缓存索引"""
        try:
            data = {k: v.to_dict() for k, v in self.cache_index.items()}
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存缓存索引失败: {e}")
    
    def _generate_cache_key(self, video_info: VideoInfo, config: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 使用视频文件路径、大小、修改时间和配置生成唯一键
        key_data = {
            'file_path': video_info.file_path,
            'file_size': video_info.file_size,
            'duration': video_info.duration,
            'config': config
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get_cached_result(self, video_info: VideoInfo, config: Dict[str, Any]) -> Optional[ProcessResult]:
        """获取缓存的处理结果
        
        Args:
            video_info: 视频信息
            config: 处理配置
            
        Returns:
            Optional[ProcessResult]: 缓存的结果，如果不存在则返回None
        """
        try:
            cache_key = self._generate_cache_key(video_info, config)
            
            if cache_key not in self.cache_index:
                return None
            
            cache_entry = self.cache_index[cache_key]
            cache_file = self.cache_dir / cache_entry.file_name
            
            # 检查缓存文件是否存在
            if not cache_file.exists():
                self.logger.warning(f"缓存文件不存在: {cache_file}")
                del self.cache_index[cache_key]
                self._save_cache_index()
                return None
            
            # 检查缓存是否过期
            if time.time() - cache_entry.created_time > self.max_cache_age:
                self.logger.info(f"缓存已过期: {cache_key}")
                self._remove_cache_entry(cache_key)
                return None
            
            # 加载缓存数据
            with open(cache_file, 'rb') as f:
                cached_data = pickle.load(f)
            
            # 更新访问时间
            cache_entry.last_accessed = time.time()
            cache_entry.access_count += 1
            self._save_cache_index()
            
            self.logger.info(f"缓存命中: {cache_key}")
            return cached_data
            
        except Exception as e:
            self.logger.error(f"获取缓存失败: {e}")
            return None
    
    def save_result_to_cache(self, video_info: VideoInfo, config: Dict[str, Any], 
                           result: ProcessResult) -> bool:
        """保存处理结果到缓存
        
        Args:
            video_info: 视频信息
            config: 处理配置
            result: 处理结果
            
        Returns:
            bool: 是否保存成功
        """
        try:
            cache_key = self._generate_cache_key(video_info, config)
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            # 保存数据到文件
            with open(cache_file, 'wb') as f:
                pickle.dump(result, f)
            
            # 获取文件大小
            file_size = cache_file.stat().st_size
            
            # 创建缓存条目
            cache_entry = CacheEntry(
                cache_key=cache_key,
                file_name=cache_file.name,
                file_size=file_size,
                created_time=time.time(),
                last_accessed=time.time(),
                access_count=1,
                video_path=video_info.file_path,
                config_hash=hashlib.md5(json.dumps(config, sort_keys=True).encode()).hexdigest()
            )
            
            # 添加到索引
            self.cache_index[cache_key] = cache_entry
            self._save_cache_index()
            
            # 检查缓存大小限制
            self._cleanup_cache_if_needed()
            
            self.logger.info(f"结果已缓存: {cache_key}, 大小: {file_size} bytes")
            return True
            
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
            return False
    
    def _cleanup_cache_if_needed(self) -> None:
        """如果需要，清理缓存"""
        try:
            # 计算当前缓存总大小
            total_size = sum(entry.file_size for entry in self.cache_index.values())
            
            if total_size <= self.max_cache_size:
                return
            
            self.logger.info(f"缓存大小超限 ({total_size} > {self.max_cache_size}), 开始清理")
            
            # 按最后访问时间排序，删除最旧的缓存
            sorted_entries = sorted(
                self.cache_index.items(),
                key=lambda x: x[1].last_accessed
            )
            
            for cache_key, entry in sorted_entries:
                if total_size <= self.max_cache_size * 0.8:  # 清理到80%
                    break
                
                self._remove_cache_entry(cache_key)
                total_size -= entry.file_size
                
            self.logger.info(f"缓存清理完成，当前大小: {total_size} bytes")
            
        except Exception as e:
            self.logger.error(f"缓存清理失败: {e}")
    
    def _remove_cache_entry(self, cache_key: str) -> None:
        """删除缓存条目"""
        try:
            if cache_key in self.cache_index:
                entry = self.cache_index[cache_key]
                cache_file = self.cache_dir / entry.file_name
                
                # 删除文件
                if cache_file.exists():
                    cache_file.unlink()
                
                # 从索引中删除
                del self.cache_index[cache_key]
                self._save_cache_index()
                
                self.logger.debug(f"删除缓存条目: {cache_key}")
                
        except Exception as e:
            self.logger.error(f"删除缓存条目失败: {e}")
    
    def clear_cache(self, video_path: Optional[str] = None) -> bool:
        """清理缓存
        
        Args:
            video_path: 如果指定，只清理该视频的缓存；否则清理所有缓存
            
        Returns:
            bool: 是否清理成功
        """
        try:
            if video_path:
                # 清理指定视频的缓存
                keys_to_remove = []
                for cache_key, entry in self.cache_index.items():
                    if entry.video_path == video_path:
                        keys_to_remove.append(cache_key)
                
                for cache_key in keys_to_remove:
                    self._remove_cache_entry(cache_key)
                
                self.logger.info(f"清理视频缓存: {video_path}, 删除 {len(keys_to_remove)} 个条目")
            else:
                # 清理所有缓存
                for cache_key in list(self.cache_index.keys()):
                    self._remove_cache_entry(cache_key)
                
                self.logger.info("清理所有缓存")
            
            return True
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            return False
    
    def get_cache_info(self, video_path: Optional[str] = None) -> CacheInfo:
        """获取缓存信息
        
        Args:
            video_path: 如果指定，只获取该视频的缓存信息
            
        Returns:
            CacheInfo: 缓存信息
        """
        try:
            if video_path:
                # 获取指定视频的缓存信息
                entries = [entry for entry in self.cache_index.values() 
                          if entry.video_path == video_path]
            else:
                # 获取所有缓存信息
                entries = list(self.cache_index.values())
            
            total_size = sum(entry.file_size for entry in entries)
            total_count = len(entries)
            
            # 计算最旧和最新的缓存时间
            if entries:
                oldest_time = min(entry.created_time for entry in entries)
                newest_time = max(entry.created_time for entry in entries)
            else:
                oldest_time = newest_time = 0
            
            return CacheInfo(
                total_size=total_size,
                total_count=total_count,
                oldest_cache_time=oldest_time,
                newest_cache_time=newest_time,
                cache_dir=str(self.cache_dir),
                max_size=self.max_cache_size,
                max_age=self.max_cache_age
            )
            
        except Exception as e:
            self.logger.error(f"获取缓存信息失败: {e}")
            return CacheInfo(
                total_size=0, total_count=0, oldest_cache_time=0,
                newest_cache_time=0, cache_dir=str(self.cache_dir),
                max_size=self.max_cache_size, max_age=self.max_cache_age
            )
    
    def cleanup_expired_cache(self) -> int:
        """清理过期缓存
        
        Returns:
            int: 清理的缓存条目数量
        """
        try:
            current_time = time.time()
            expired_keys = []
            
            for cache_key, entry in self.cache_index.items():
                if current_time - entry.created_time > self.max_cache_age:
                    expired_keys.append(cache_key)
            
            for cache_key in expired_keys:
                self._remove_cache_entry(cache_key)
            
            if expired_keys:
                self.logger.info(f"清理过期缓存: {len(expired_keys)} 个条目")
            
            return len(expired_keys)
            
        except Exception as e:
            self.logger.error(f"清理过期缓存失败: {e}")
            return 0
