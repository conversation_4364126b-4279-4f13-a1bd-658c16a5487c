# VideoReader 包清单文件
# 指定在构建包时包含的额外文件

# 包含项目根目录的重要文件
include README.md
include LICENSE
include CHANGELOG.md
include requirements.txt
include requirements-dev.txt
include pyproject.toml
include setup.py
include pytest.ini

# 包含配置文件
include .gitignore
include .pre-commit-config.yaml

# 包含文档
recursive-include doc *.md
recursive-include doc *.rst
recursive-include doc *.txt
recursive-include doc *.png
recursive-include doc *.jpg
recursive-include doc *.svg

# 包含UI资源文件
recursive-include src/ui/styles *.qss
recursive-include src/ui/icons *.png
recursive-include src/ui/icons *.ico
recursive-include src/ui/icons *.svg
recursive-include src/ui/resources *.qrc

# 包含配置文件
recursive-include src/common/config *.json
recursive-include src/common/config *.yaml
recursive-include src/common/config *.yml
recursive-include src/common/config *.toml

# 包含测试文件
recursive-include tests *.py
recursive-include tests/fixtures *.json
recursive-include tests/fixtures *.yaml
recursive-include tests/fixtures *.mp4
recursive-include tests/fixtures *.wav
recursive-include tests/fixtures *.txt

# 包含示例文件
recursive-include examples *.py
recursive-include examples *.md
recursive-include examples *.json

# 包含脚本文件
recursive-include scripts *.py
recursive-include scripts *.sh
recursive-include scripts *.bat

# 包含数据文件
recursive-include data *.json
recursive-include data *.csv
recursive-include data *.txt

# 排除不需要的文件
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .DS_Store
global-exclude Thumbs.db
global-exclude *.so
global-exclude *.dylib
global-exclude *.dll

# 排除开发和构建文件
global-exclude .git*
global-exclude .pytest_cache
global-exclude .mypy_cache
global-exclude .coverage
global-exclude htmlcov
global-exclude .tox
global-exclude build
global-exclude dist
global-exclude *.egg-info

# 排除IDE文件
global-exclude .vscode
global-exclude .idea
global-exclude *.swp
global-exclude *.swo
global-exclude *~

# 排除临时文件
global-exclude *.tmp
global-exclude *.temp
global-exclude *.log

# 排除大型媒体文件（测试用的小文件除外）
global-exclude *.mp4
global-exclude *.avi
global-exclude *.mov
global-exclude *.mkv
global-exclude *.wmv
global-exclude *.flv
global-exclude *.webm

# 但包含测试用的小文件
include tests/fixtures/*.mp4
include tests/fixtures/*.wav
