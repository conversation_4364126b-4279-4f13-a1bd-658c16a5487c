"""搜索引擎数据模型

定义搜索引擎模块内部使用的数据结构。
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime
from enum import Enum
import re


class IndexType(Enum):
    """索引类型枚举"""
    INVERTED = "inverted"
    TRIE = "trie"
    HASH = "hash"
    VECTOR = "vector"


class MatchType(Enum):
    """匹配类型枚举"""
    EXACT = "exact"
    PARTIAL = "partial"
    FUZZY = "fuzzy"
    SEMANTIC = "semantic"


@dataclass
class IndexDocument:
    """索引文档数据模型"""
    doc_id: int
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    tokens: List[str] = field(default_factory=list)
    timestamp: float = 0.0
    confidence: float = 1.0
    
    def __post_init__(self):
        if not self.tokens and self.content:
            self.tokens = self._tokenize(self.content)
    
    def _tokenize(self, text: str) -> List[str]:
        """简单的分词"""
        # 移除标点符号并转换为小写
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        return [token.strip() for token in text.split() if token.strip()]


@dataclass
class InvertedIndex:
    """倒排索引数据模型"""
    term_to_docs: Dict[str, Set[int]] = field(default_factory=dict)
    doc_to_terms: Dict[int, Set[str]] = field(default_factory=dict)
    term_frequencies: Dict[str, Dict[int, int]] = field(default_factory=dict)
    document_count: int = 0
    
    def add_document(self, doc: IndexDocument):
        """添加文档到索引"""
        doc_id = doc.doc_id
        self.document_count += 1
        
        # 计算词频
        term_freq = {}
        for token in doc.tokens:
            term_freq[token] = term_freq.get(token, 0) + 1
        
        # 更新倒排索引
        for term, freq in term_freq.items():
            if term not in self.term_to_docs:
                self.term_to_docs[term] = set()
                self.term_frequencies[term] = {}
            
            self.term_to_docs[term].add(doc_id)
            self.term_frequencies[term][doc_id] = freq
        
        # 更新文档到词项的映射
        self.doc_to_terms[doc_id] = set(term_freq.keys())
    
    def remove_document(self, doc_id: int):
        """从索引中移除文档"""
        if doc_id not in self.doc_to_terms:
            return
        
        terms = self.doc_to_terms[doc_id]
        
        # 从倒排索引中移除
        for term in terms:
            if term in self.term_to_docs:
                self.term_to_docs[term].discard(doc_id)
                if not self.term_to_docs[term]:
                    del self.term_to_docs[term]
            
            if term in self.term_frequencies and doc_id in self.term_frequencies[term]:
                del self.term_frequencies[term][doc_id]
                if not self.term_frequencies[term]:
                    del self.term_frequencies[term]
        
        del self.doc_to_terms[doc_id]
        self.document_count -= 1
    
    def search_term(self, term: str) -> Set[int]:
        """搜索包含指定词项的文档"""
        return self.term_to_docs.get(term.lower(), set())
    
    def get_term_frequency(self, term: str, doc_id: int) -> int:
        """获取词项在文档中的频率"""
        return self.term_frequencies.get(term.lower(), {}).get(doc_id, 0)


@dataclass
class SearchMatch:
    """搜索匹配数据模型"""
    start_pos: int
    end_pos: int
    matched_text: str
    match_type: MatchType
    score: float = 1.0
    context: str = ""
    
    @property
    def length(self) -> int:
        return self.end_pos - self.start_pos


@dataclass
class QueryTerm:
    """查询词项数据模型"""
    term: str
    weight: float = 1.0
    required: bool = False
    excluded: bool = False
    fuzzy_distance: int = 0
    
    def __post_init__(self):
        self.term = self.term.lower().strip()


@dataclass
class ParsedQuery:
    """解析后的查询数据模型"""
    terms: List[QueryTerm] = field(default_factory=list)
    phrases: List[str] = field(default_factory=list)
    time_range: Optional[Tuple[float, float]] = None
    filters: Dict[str, Any] = field(default_factory=dict)
    sort_by: str = "relevance"
    max_results: int = 100
    
    def get_all_terms(self) -> List[str]:
        """获取所有查询词项"""
        all_terms = [term.term for term in self.terms]
        
        # 添加短语中的词项
        for phrase in self.phrases:
            phrase_terms = re.findall(r'\w+', phrase.lower())
            all_terms.extend(phrase_terms)
        
        return list(set(all_terms))


@dataclass
class SearchStatistics:
    """搜索统计数据模型"""
    total_queries: int = 0
    successful_queries: int = 0
    failed_queries: int = 0
    average_response_time: float = 0.0
    popular_terms: Dict[str, int] = field(default_factory=dict)
    query_history: List[str] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)
    
    def record_query(self, query: str, success: bool, response_time: float):
        """记录查询统计"""
        self.total_queries += 1
        
        if success:
            self.successful_queries += 1
        else:
            self.failed_queries += 1
        
        # 更新平均响应时间
        self.average_response_time = (
            (self.average_response_time * (self.total_queries - 1) + response_time) 
            / self.total_queries
        )
        
        # 记录查询历史
        self.query_history.append(query)
        if len(self.query_history) > 1000:  # 限制历史记录数量
            self.query_history.pop(0)
        
        # 更新热门词项
        terms = re.findall(r'\w+', query.lower())
        for term in terms:
            self.popular_terms[term] = self.popular_terms.get(term, 0) + 1
        
        self.last_updated = datetime.now()
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_queries == 0:
            return 0.0
        return self.successful_queries / self.total_queries
    
    def get_top_terms(self, limit: int = 10) -> List[Tuple[str, int]]:
        """获取热门词项"""
        return sorted(self.popular_terms.items(), key=lambda x: x[1], reverse=True)[:limit]


@dataclass
class IndexMetadata:
    """索引元数据"""
    created_time: datetime
    last_updated: datetime
    document_count: int
    term_count: int
    index_size_bytes: int
    version: str = "1.0.0"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'created_time': self.created_time.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'document_count': self.document_count,
            'term_count': self.term_count,
            'index_size_bytes': self.index_size_bytes,
            'version': self.version
        }


@dataclass
class SuggestionTrie:
    """建议词典树数据模型"""
    children: Dict[str, 'SuggestionTrie'] = field(default_factory=dict)
    is_end_of_word: bool = False
    frequency: int = 0
    suggestions: List[str] = field(default_factory=list)
    
    def insert(self, word: str, frequency: int = 1):
        """插入单词"""
        node = self
        for char in word.lower():
            if char not in node.children:
                node.children[char] = SuggestionTrie()
            node = node.children[char]
        
        node.is_end_of_word = True
        node.frequency += frequency
    
    def search_prefix(self, prefix: str) -> List[Tuple[str, int]]:
        """搜索前缀匹配的单词"""
        node = self
        for char in prefix.lower():
            if char not in node.children:
                return []
            node = node.children[char]
        
        # 收集所有以该前缀开始的单词
        results = []
        self._collect_words(node, prefix, results)
        
        # 按频率排序
        return sorted(results, key=lambda x: x[1], reverse=True)
    
    def _collect_words(self, node: 'SuggestionTrie', prefix: str, results: List[Tuple[str, int]]):
        """递归收集单词"""
        if node.is_end_of_word:
            results.append((prefix, node.frequency))
        
        for char, child_node in node.children.items():
            self._collect_words(child_node, prefix + char, results)


@dataclass
class SearchCache:
    """搜索缓存数据模型"""
    cache: Dict[str, Tuple[List[Any], datetime]] = field(default_factory=dict)
    max_size: int = 1000
    ttl_seconds: int = 3600  # 1小时
    
    def get(self, query_key: str) -> Optional[List[Any]]:
        """获取缓存结果"""
        if query_key in self.cache:
            results, timestamp = self.cache[query_key]
            
            # 检查是否过期
            if (datetime.now() - timestamp).total_seconds() < self.ttl_seconds:
                return results
            else:
                del self.cache[query_key]
        
        return None
    
    def put(self, query_key: str, results: List[Any]):
        """缓存结果"""
        # 如果缓存已满，删除最旧的条目
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][1])
            del self.cache[oldest_key]
        
        self.cache[query_key] = (results, datetime.now())
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
    
    def cleanup_expired(self):
        """清理过期缓存"""
        now = datetime.now()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if (now - timestamp).total_seconds() >= self.ttl_seconds
        ]
        
        for key in expired_keys:
            del self.cache[key]


@dataclass
class HighlightConfig:
    """高亮配置数据模型"""
    pre_tag: str = "<mark>"
    post_tag: str = "</mark>"
    max_fragments: int = 3
    fragment_size: int = 100
    fragment_separator: str = "..."
    
    def highlight_text(self, text: str, matches: List[SearchMatch]) -> str:
        """高亮文本中的匹配部分"""
        if not matches:
            return text
        
        # 按位置排序匹配项
        sorted_matches = sorted(matches, key=lambda m: m.start_pos)
        
        # 构建高亮文本
        result = ""
        last_end = 0
        
        for match in sorted_matches:
            # 添加匹配前的文本
            result += text[last_end:match.start_pos]
            
            # 添加高亮的匹配文本
            result += self.pre_tag + match.matched_text + self.post_tag
            
            last_end = match.end_pos
        
        # 添加最后的文本
        result += text[last_end:]
        
        return result


@dataclass
class SearchIndex:
    """搜索索引数据模型"""
    index_entries: Dict[str, 'IndexEntry']
    segment_count: int
    word_count: int
    config: 'SearchConfig'
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'index_entries': {k: v.to_dict() for k, v in self.index_entries.items()},
            'segment_count': self.segment_count,
            'word_count': self.word_count,
            'config': self.config.to_dict(),
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchIndex':
        """从字典创建实例"""
        config = SearchConfig.from_dict(data['config'])
        index_entries = {k: IndexEntry.from_dict(v) for k, v in data['index_entries'].items()}

        return cls(
            index_entries=index_entries,
            segment_count=data['segment_count'],
            word_count=data['word_count'],
            config=config,
            metadata=data.get('metadata', {})
        )


@dataclass
class IndexEntry:
    """索引条目数据模型"""
    word: str
    segment_ids: List[int]
    frequency: int
    tf_idf_weights: Dict[int, float] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'word': self.word,
            'segment_ids': self.segment_ids,
            'frequency': self.frequency,
            'tf_idf_weights': self.tf_idf_weights
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'IndexEntry':
        """从字典创建实例"""
        return cls(
            word=data['word'],
            segment_ids=data['segment_ids'],
            frequency=data['frequency'],
            tf_idf_weights=data.get('tf_idf_weights', {})
        )


@dataclass
class SearchConfig:
    """搜索配置数据模型"""
    min_word_length: int = 2
    max_word_length: int = 50
    include_numbers: bool = False
    include_summary: bool = True
    stop_words_path: Optional[str] = None
    user_dict_path: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'min_word_length': self.min_word_length,
            'max_word_length': self.max_word_length,
            'include_numbers': self.include_numbers,
            'include_summary': self.include_summary,
            'stop_words_path': self.stop_words_path,
            'user_dict_path': self.user_dict_path
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchConfig':
        """从字典创建实例"""
        return cls(
            min_word_length=data.get('min_word_length', 2),
            max_word_length=data.get('max_word_length', 50),
            include_numbers=data.get('include_numbers', False),
            include_summary=data.get('include_summary', True),
            stop_words_path=data.get('stop_words_path'),
            user_dict_path=data.get('user_dict_path')
        )
