"""配置管理模块主入口

提供统一的配置管理接口。
"""

# 从子模块导入主要功能
from .config.manager import ConfigManager
from .config.defaults import get_default_config

# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def get_config(key: str, default=None):
    """获取配置值"""
    return get_config_manager().get(key, default)

def set_config(key: str, value):
    """设置配置值"""
    return get_config_manager().set(key, value)

def save_config():
    """保存配置"""
    return get_config_manager().save()

def load_config():
    """加载配置"""
    return get_config_manager().load()

# 导出主要接口
__all__ = [
    'ConfigManager',
    'get_config_manager',
    'get_config',
    'set_config', 
    'save_config',
    'load_config',
    'get_default_config'
]
