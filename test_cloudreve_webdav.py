#!/usr/bin/env python3
"""测试Cloudreve WebDAV功能"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def test_webdav_connection():
    """测试WebDAV连接"""
    print("=== 测试Cloudreve WebDAV连接 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    webdav_url = f"{base_url}/dav"
    
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        print(f"WebDAV URL: {webdav_url}")
        print(f"用户名: {username}")
        print(f"密码: {'已配置' if password else '未配置'}")
        
        # 创建会话
        session = requests.Session()
        session.auth = HTTPBasicAuth(username, password)
        session.headers.update({
            'User-Agent': 'VideoReader-WebDAV-Client/1.0'
        })
        
        # 1. 测试PROPFIND（列出目录）
        print("\n1. 测试PROPFIND（列出根目录）")
        propfind_body = '''<?xml version="1.0" encoding="utf-8" ?>
<D:propfind xmlns:D="DAV:">
    <D:prop>
        <D:displayname/>
        <D:getcontentlength/>
        <D:getcontenttype/>
        <D:getlastmodified/>
        <D:resourcetype/>
    </D:prop>
</D:propfind>'''
        
        response = session.request(
            'PROPFIND',
            webdav_url,
            data=propfind_body,
            headers={'Content-Type': 'application/xml', 'Depth': '1'},
            timeout=30
        )
        
        print(f"   PROPFIND状态码: {response.status_code}")
        print(f"   响应内容: {response.text[:300]}")
        
        if response.status_code == 207:  # Multi-Status
            print("   ✅ WebDAV连接成功！")
            return True
        elif response.status_code == 401:
            print("   ❌ 认证失败，请检查用户名密码")
            return False
        else:
            print(f"   ❌ WebDAV连接失败: {response.status_code}")
            return False
            
    except ImportError:
        print("❌ 需要安装requests库")
        return False
    except Exception as e:
        print(f"❌ WebDAV测试异常: {e}")
        return False


def test_webdav_upload():
    """测试WebDAV文件上传"""
    print("\n=== 测试WebDAV文件上传 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    webdav_url = f"{base_url}/dav"
    
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # 创建测试文件
        test_content = f"""WebDAV上传测试文件
创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
测试内容: 这是通过WebDAV协议上传的文件
""".encode('utf-8')
        
        test_filename = f"webdav_test_{int(time.time())}.txt"
        
        print(f"创建测试文件: {test_filename}")
        print(f"文件大小: {len(test_content)} bytes")
        
        # 创建会话
        session = requests.Session()
        session.auth = HTTPBasicAuth(username, password)
        
        # 上传文件（使用PUT方法）
        upload_url = f"{webdav_url}/{test_filename}"
        print(f"上传URL: {upload_url}")
        
        response = session.put(
            upload_url,
            data=test_content,
            headers={'Content-Type': 'text/plain'},
            timeout=30
        )
        
        print(f"上传状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code in [200, 201, 204]:  # 成功状态码
            print("✅ 文件上传成功！")
            
            # 验证文件是否存在
            print("\n验证文件是否存在...")
            check_response = session.request(
                'PROPFIND',
                upload_url,
                headers={'Depth': '0'},
                timeout=10
            )
            
            if check_response.status_code == 207:
                print("✅ 文件验证成功，文件已存在于服务器")
                
                # 尝试下载文件验证内容
                print("\n下载文件验证内容...")
                download_response = session.get(upload_url, timeout=10)
                if download_response.status_code == 200:
                    downloaded_content = download_response.content
                    if downloaded_content == test_content:
                        print("✅ 文件内容验证成功")
                        
                        # 构造公网访问URL
                        public_url = f"{base_url}/s/{test_filename}"  # 尝试直接访问
                        print(f"尝试公网URL: {public_url}")
                        
                        # 测试公网访问
                        public_response = requests.get(public_url, timeout=10)
                        print(f"公网访问状态: {public_response.status_code}")
                        
                        if public_response.status_code == 200:
                            print("✅ 文件可以通过公网访问")
                            return public_url
                        else:
                            # 尝试其他可能的公网路径
                            alternative_urls = [
                                f"{base_url}/file/{test_filename}",
                                f"{base_url}/share/{test_filename}",
                                f"{base_url}/public/{test_filename}",
                                f"{base_url}/d/{test_filename}"
                            ]
                            
                            for alt_url in alternative_urls:
                                alt_response = requests.get(alt_url, timeout=10)
                                if alt_response.status_code == 200:
                                    print(f"✅ 找到公网访问路径: {alt_url}")
                                    return alt_url
                            
                            print("⚠️ 文件上传成功，但无法找到公网访问路径")
                            return upload_url  # 返回WebDAV路径
                    else:
                        print("❌ 文件内容验证失败")
                else:
                    print(f"❌ 文件下载失败: {download_response.status_code}")
            else:
                print(f"❌ 文件验证失败: {check_response.status_code}")
                
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ WebDAV上传测试异常: {e}")
        return None


def test_webdav_directory_operations():
    """测试WebDAV目录操作"""
    print("\n=== 测试WebDAV目录操作 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    webdav_url = f"{base_url}/dav"
    
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        session = requests.Session()
        session.auth = HTTPBasicAuth(username, password)
        
        # 创建测试目录
        test_dir = f"test_dir_{int(time.time())}"
        dir_url = f"{webdav_url}/{test_dir}"
        
        print(f"创建目录: {test_dir}")
        
        # 使用MKCOL方法创建目录
        response = session.request('MKCOL', dir_url, timeout=10)
        print(f"创建目录状态: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("✅ 目录创建成功")
            
            # 在目录中上传文件
            test_content = b"Directory test file content"
            file_url = f"{dir_url}/test_file.txt"
            
            upload_response = session.put(file_url, data=test_content, timeout=10)
            print(f"目录中文件上传状态: {upload_response.status_code}")
            
            if upload_response.status_code in [200, 201, 204]:
                print("✅ 目录中文件上传成功")
                return True
            else:
                print("❌ 目录中文件上传失败")
        else:
            print("❌ 目录创建失败")
            
    except Exception as e:
        print(f"❌ 目录操作测试异常: {e}")
        
    return False


def main():
    """主函数"""
    print("Cloudreve WebDAV功能测试")
    print("=" * 60)
    
    # 测试连接
    if test_webdav_connection():
        # 测试文件上传
        upload_result = test_webdav_upload()
        
        # 测试目录操作
        test_webdav_directory_operations()
        
        if upload_result:
            print(f"\n🎉 WebDAV测试成功！")
            print(f"文件URL: {upload_result}")
            print("\n我们可以基于WebDAV实现Cloudreve文件上传功能。")
        else:
            print("\n⚠️ WebDAV连接成功，但文件上传有问题")
    else:
        print("\n❌ WebDAV连接失败")
        print("可能的原因:")
        print("1. WebDAV功能未启用")
        print("2. 用户名密码错误")
        print("3. 用户没有WebDAV权限")
    
    print("\n" + "=" * 60)
    print("测试完成！")


if __name__ == "__main__":
    main()
