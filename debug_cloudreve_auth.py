#!/usr/bin/env python3
"""调试Cloudreve认证问题"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config
from src.common.logging import get_logger

logger = get_logger(__name__)


def test_cloudreve_api():
    """测试Cloudreve API"""
    print("=== 调试Cloudreve认证 ===")
    
    # 获取配置
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    print(f"Base URL: {base_url}")
    print(f"Username: {username}")
    print(f"Password: {'已配置' if password else '未配置'}")
    
    try:
        import requests
        
        # 1. 测试基本连接
        print("\n=== 测试基本连接 ===")
        try:
            response = requests.get(f"{base_url}/site/ping", timeout=10)
            print(f"Ping响应状态码: {response.status_code}")
            print(f"Ping响应内容: {response.text[:200]}")
        except Exception as e:
            print(f"Ping测试失败: {e}")
        
        # 2. 测试登录API
        print("\n=== 测试登录API ===")
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0',
            'Content-Type': 'application/json'
        })
        
        login_data = {
            'username': username,
            'password': password
        }
        
        print(f"登录请求URL: {base_url}/user/session")
        print(f"登录请求数据: {json.dumps(login_data, indent=2)}")
        
        try:
            response = session.post(
                f"{base_url}/user/session",
                json=login_data,
                timeout=30
            )
            
            print(f"登录响应状态码: {response.status_code}")
            print(f"登录响应头: {dict(response.headers)}")
            print(f"登录响应内容: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"解析后的JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    if result.get('code') == 0:
                        print("✅ 登录成功!")
                        token = result.get('data', {}).get('token')
                        if token:
                            print(f"获取到Token: {token[:20]}...")
                        else:
                            print("⚠️ 未获取到Token")
                    else:
                        print(f"❌ 登录失败: {result.get('msg', 'Unknown error')}")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应: {response.text}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 登录请求异常: {e}")
        
        # 3. 测试其他可能的API端点
        print("\n=== 测试其他API端点 ===")
        test_endpoints = [
            "/api/v3/site/ping",
            "/api/v3/user/session",
            "/ping",
            "/"
        ]
        
        for endpoint in test_endpoints:
            try:
                url = f"{base_url}{endpoint}"
                print(f"\n测试端点: {url}")
                response = requests.get(url, timeout=10)
                print(f"  状态码: {response.status_code}")
                print(f"  内容预览: {response.text[:100]}")
            except Exception as e:
                print(f"  错误: {e}")
        
    except ImportError:
        print("❌ 需要安装requests库: pip install requests")


def test_different_auth_methods():
    """测试不同的认证方法"""
    print("\n=== 测试不同认证方法 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    try:
        import requests
        
        # 方法1: 标准JSON登录
        print("\n方法1: 标准JSON登录")
        session1 = requests.Session()
        session1.headers.update({'Content-Type': 'application/json'})
        
        login_data = {'username': username, 'password': password}
        response = session1.post(f"{base_url}/user/session", json=login_data, timeout=30)
        print(f"  状态码: {response.status_code}")
        print(f"  响应: {response.text[:200]}")
        
        # 方法2: 表单数据登录
        print("\n方法2: 表单数据登录")
        session2 = requests.Session()
        session2.headers.update({'Content-Type': 'application/x-www-form-urlencoded'})
        
        form_data = {'username': username, 'password': password}
        response = session2.post(f"{base_url}/user/session", data=form_data, timeout=30)
        print(f"  状态码: {response.status_code}")
        print(f"  响应: {response.text[:200]}")
        
        # 方法3: 不设置Content-Type
        print("\n方法3: 不设置Content-Type")
        session3 = requests.Session()
        
        response = session3.post(f"{base_url}/user/session", json=login_data, timeout=30)
        print(f"  状态码: {response.status_code}")
        print(f"  响应: {response.text[:200]}")
        
    except Exception as e:
        print(f"测试异常: {e}")


def main():
    """主函数"""
    print("Cloudreve认证调试工具")
    print("=" * 50)
    
    test_cloudreve_api()
    test_different_auth_methods()
    
    print("\n" + "=" * 50)
    print("调试完成！")
    print("\n建议:")
    print("1. 检查Cloudreve服务器是否正常运行")
    print("2. 确认API端点是否正确")
    print("3. 验证用户名密码是否正确")
    print("4. 检查Cloudreve版本和API兼容性")


if __name__ == "__main__":
    main()
