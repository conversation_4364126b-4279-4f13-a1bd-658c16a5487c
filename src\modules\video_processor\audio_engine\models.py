"""音频引擎数据模型

定义音频处理相关的数据结构。
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from pathlib import Path


@dataclass
class AudioInfo:
    """音频信息数据模型"""
    file_path: str
    file_name: str
    file_size: int
    duration: float
    sample_rate: int
    channels: int
    bit_depth: int
    codec: str
    bitrate: Optional[int] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        
        if not self.file_name and self.file_path:
            self.file_name = Path(self.file_path).name
    
    @property
    def duration_str(self) -> str:
        """格式化的时长字符串"""
        hours = int(self.duration // 3600)
        minutes = int((self.duration % 3600) // 60)
        seconds = int(self.duration % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    @property
    def file_size_str(self) -> str:
        """格式化的文件大小字符串"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
        else:
            return f"{self.file_size / (1024 * 1024 * 1024):.1f} GB"


@dataclass
class TranscriptSegment:
    """语音识别转录段落"""
    id: int
    start_time: float
    end_time: float
    text: str
    confidence: float
    language: str = 'zh'
    speaker_id: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def duration(self) -> float:
        """段落时长"""
        return self.end_time - self.start_time
    
    @property
    def words_count(self) -> int:
        """词数统计"""
        return len(self.text.split())
    
    @property
    def time_range_str(self) -> str:
        """时间范围字符串"""
        return f"{self.start_time:.2f}s - {self.end_time:.2f}s"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'text': self.text,
            'confidence': self.confidence,
            'language': self.language,
            'speaker_id': self.speaker_id,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TranscriptSegment':
        """从字典创建实例"""
        return cls(
            id=data['id'],
            start_time=data['start_time'],
            end_time=data['end_time'],
            text=data['text'],
            confidence=data['confidence'],
            language=data.get('language', 'zh'),
            speaker_id=data.get('speaker_id'),
            metadata=data.get('metadata', {})
        )


@dataclass
class SilenceSegment:
    """静音段落"""
    start_time: float
    end_time: float
    duration: float
    confidence: float = 1.0
    
    @property
    def time_range_str(self) -> str:
        """时间范围字符串"""
        return f"{self.start_time:.2f}s - {self.end_time:.2f}s"


@dataclass
class AudioProcessingConfig:
    """音频处理配置"""
    # 音频提取配置
    audio_format: str = 'wav'
    sample_rate: int = 16000
    channels: int = 1
    
    # 语音识别配置
    speech_engine: str = 'whisper'
    language: str = 'zh'
    model_size: str = 'base'
    
    # 静音检测配置
    silence_threshold: float = -40.0  # dB
    min_silence_duration: float = 1.0  # 秒
    
    # 输出配置
    output_dir: Optional[str] = None
    keep_temp_files: bool = False
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.audio_format not in ['wav', 'mp3', 'flac']:
            return False
        
        if self.sample_rate not in [8000, 16000, 22050, 44100, 48000]:
            return False
        
        if self.channels not in [1, 2]:
            return False
        
        if self.speech_engine not in ['whisper', 'azure', 'google', 'paraformer']:
            return False
        
        if self.language not in ['zh', 'en', 'ja', 'ko']:
            return False
        
        if self.model_size not in ['tiny', 'base', 'small', 'medium', 'large']:
            return False
        
        return True
