"""搜索索引构建器

负责为视频段落构建搜索索引，支持全文搜索和关键词搜索。
"""

import re
import json
import pickle
from pathlib import Path
from typing import Dict, List, Set, Any, Optional
from collections import defaultdict, Counter
# 尝试导入jieba，如果失败则使用简单分词
try:
    import jieba  # 中文分词
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False

from core.models import SegmentInfo
from .models import SearchIndex, IndexEntry, SearchConfig
from common.exceptions import SearchIndexError
from common.logging import get_logger


logger = get_logger(__name__)


class SearchIndexer:
    """搜索索引构建器
    
    负责为视频段落构建倒排索引，支持中英文分词和全文搜索。
    """
    
    def __init__(self, config: Optional[SearchConfig] = None):
        self.config = config or SearchConfig()
        self.logger = get_logger(__name__)
        
        # 初始化中文分词
        self._init_jieba()
        
        # 停用词列表
        self.stop_words = self._load_stop_words()
        
        self.logger.info("搜索索引器初始化完成")
    
    def _init_jieba(self) -> None:
        """初始化jieba分词器"""
        if not JIEBA_AVAILABLE:
            self.logger.warning("jieba未安装，将使用简单分词方法")
            return

        try:
            # 设置jieba日志级别
            jieba.setLogLevel(20)  # INFO级别

            # 加载用户词典（如果存在）
            user_dict_path = self.config.user_dict_path
            if user_dict_path and Path(user_dict_path).exists():
                jieba.load_userdict(user_dict_path)
                self.logger.info(f"加载用户词典: {user_dict_path}")

        except Exception as e:
            self.logger.warning(f"初始化jieba失败: {e}")
    
    def _load_stop_words(self) -> Set[str]:
        """加载停用词列表"""
        stop_words = set()
        
        try:
            # 默认停用词
            default_stop_words = {
                # 中文停用词
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                '自己', '这', '那', '里', '就是', '还', '把', '来', '时候', '可以', '这个',
                # 英文停用词
                'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
                'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
                'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
                'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
            }
            stop_words.update(default_stop_words)
            
            # 从文件加载停用词（如果存在）
            stop_words_path = self.config.stop_words_path
            if stop_words_path and Path(stop_words_path).exists():
                with open(stop_words_path, 'r', encoding='utf-8') as f:
                    file_stop_words = {line.strip() for line in f if line.strip()}
                    stop_words.update(file_stop_words)
                    self.logger.info(f"加载停用词文件: {stop_words_path}, 共 {len(file_stop_words)} 个")
            
        except Exception as e:
            self.logger.warning(f"加载停用词失败: {e}")
        
        self.logger.info(f"停用词总数: {len(stop_words)}")
        return stop_words
    
    def build_index(self, segments: List[SegmentInfo]) -> SearchIndex:
        """构建搜索索引
        
        Args:
            segments: 视频段落列表
            
        Returns:
            SearchIndex: 构建的搜索索引
        """
        try:
            self.logger.info(f"开始构建搜索索引: {len(segments)} 个段落")
            
            # 初始化索引结构
            word_index = defaultdict(list)  # 词汇 -> 段落ID列表
            segment_words = {}  # 段落ID -> 词汇列表
            word_frequencies = Counter()  # 词汇频率统计
            
            # 处理每个段落
            for segment in segments:
                segment_id = segment.id
                
                # 提取文本内容
                text = segment.text
                if self.config.include_summary and segment.summary:
                    text += " " + segment.summary
                
                # 分词和预处理
                words = self._tokenize_and_preprocess(text)
                
                # 更新索引
                segment_words[segment_id] = words
                word_frequencies.update(words)
                
                # 构建倒排索引
                for word in set(words):  # 去重
                    word_index[word].append(segment_id)
            
            # 计算TF-IDF权重
            tf_idf_weights = self._calculate_tf_idf(segment_words, word_frequencies, len(segments))
            
            # 创建索引条目
            index_entries = {}
            for word, segment_ids in word_index.items():
                index_entries[word] = IndexEntry(
                    word=word,
                    segment_ids=segment_ids,
                    frequency=word_frequencies[word],
                    tf_idf_weights=tf_idf_weights.get(word, {})
                )
            
            # 创建搜索索引
            search_index = SearchIndex(
                index_entries=index_entries,
                segment_count=len(segments),
                word_count=len(word_index),
                config=self.config,
                metadata={
                    'build_time': self._get_current_time(),
                    'total_words': sum(word_frequencies.values()),
                    'unique_words': len(word_frequencies),
                    'avg_words_per_segment': sum(len(words) for words in segment_words.values()) / len(segments) if segments else 0
                }
            )
            
            self.logger.info(f"搜索索引构建完成: {len(word_index)} 个词汇, "
                           f"{sum(word_frequencies.values())} 个词汇实例")
            
            return search_index
            
        except Exception as e:
            self.logger.error(f"构建搜索索引失败: {e}")
            raise SearchIndexError(f"构建搜索索引失败: {e}")
    
    def _tokenize_and_preprocess(self, text: str) -> List[str]:
        """分词和预处理"""
        if not text:
            return []
        
        # 清理文本
        text = self._clean_text(text)
        
        # 分词
        words = []
        
        # 中文分词
        if self._contains_chinese(text):
            if JIEBA_AVAILABLE:
                chinese_words = jieba.lcut(text, cut_all=False)
                words.extend(chinese_words)
            else:
                # 简单的中文分词：按字符分割
                chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
                for chars in chinese_chars:
                    # 将连续的中文字符按2-3个字符分组
                    for i in range(len(chars)):
                        if i + 2 <= len(chars):
                            words.append(chars[i:i+2])
                        if i + 3 <= len(chars):
                            words.append(chars[i:i+3])
        
        # 英文分词
        english_words = re.findall(r'\b[a-zA-Z]+\b', text)
        words.extend(english_words)
        
        # 数字提取
        if self.config.include_numbers:
            numbers = re.findall(r'\b\d+\b', text)
            words.extend(numbers)
        
        # 预处理
        processed_words = []
        for word in words:
            word = word.strip().lower()
            
            # 过滤条件
            if (len(word) >= self.config.min_word_length and
                len(word) <= self.config.max_word_length and
                word not in self.stop_words and
                not word.isspace()):
                
                processed_words.append(word)
        
        return processed_words
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除特殊字符，保留中文、英文、数字和空格
        cleaned = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', text)
        
        # 合并多个空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        return cleaned.strip()
    
    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        return bool(re.search(r'[\u4e00-\u9fff]', text))
    
    def _calculate_tf_idf(self, segment_words: Dict[int, List[str]], 
                         word_frequencies: Counter, total_segments: int) -> Dict[str, Dict[int, float]]:
        """计算TF-IDF权重"""
        import math
        
        tf_idf_weights = defaultdict(dict)
        
        # 计算每个词在每个段落中的TF-IDF
        for segment_id, words in segment_words.items():
            word_count = Counter(words)
            segment_length = len(words)
            
            for word, count in word_count.items():
                # TF (Term Frequency)
                tf = count / segment_length if segment_length > 0 else 0
                
                # IDF (Inverse Document Frequency)
                # 包含该词的段落数
                df = len([sid for sid, seg_words in segment_words.items() if word in seg_words])
                idf = math.log(total_segments / df) if df > 0 else 0
                
                # TF-IDF
                tf_idf = tf * idf
                tf_idf_weights[word][segment_id] = tf_idf
        
        return dict(tf_idf_weights)
    
    def save_index(self, index: SearchIndex, file_path: str) -> bool:
        """保存索引到文件
        
        Args:
            index: 搜索索引
            file_path: 文件路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            if file_path.suffix.lower() == '.json':
                # 保存为JSON格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(index.to_dict(), f, indent=2, ensure_ascii=False)
            else:
                # 保存为pickle格式
                with open(file_path, 'wb') as f:
                    pickle.dump(index, f)
            
            self.logger.info(f"搜索索引已保存: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存搜索索引失败: {e}")
            return False
    
    def load_index(self, file_path: str) -> Optional[SearchIndex]:
        """从文件加载索引
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[SearchIndex]: 加载的搜索索引，失败返回None
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                self.logger.warning(f"索引文件不存在: {file_path}")
                return None
            
            if file_path.suffix.lower() == '.json':
                # 从JSON格式加载
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return SearchIndex.from_dict(data)
            else:
                # 从pickle格式加载
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            
        except Exception as e:
            self.logger.error(f"加载搜索索引失败: {e}")
            return None
    
    def update_index(self, index: SearchIndex, new_segments: List[SegmentInfo]) -> SearchIndex:
        """更新索引（增量更新）
        
        Args:
            index: 现有索引
            new_segments: 新增段落
            
        Returns:
            SearchIndex: 更新后的索引
        """
        try:
            self.logger.info(f"更新搜索索引: 新增 {len(new_segments)} 个段落")
            
            # 构建新段落的索引
            new_index = self.build_index(new_segments)
            
            # 合并索引
            merged_entries = index.index_entries.copy()
            
            for word, new_entry in new_index.index_entries.items():
                if word in merged_entries:
                    # 合并现有词汇的索引
                    existing_entry = merged_entries[word]
                    existing_entry.segment_ids.extend(new_entry.segment_ids)
                    existing_entry.frequency += new_entry.frequency
                    existing_entry.tf_idf_weights.update(new_entry.tf_idf_weights)
                else:
                    # 添加新词汇
                    merged_entries[word] = new_entry
            
            # 创建更新后的索引
            updated_index = SearchIndex(
                index_entries=merged_entries,
                segment_count=index.segment_count + new_index.segment_count,
                word_count=len(merged_entries),
                config=self.config,
                metadata={
                    'build_time': self._get_current_time(),
                    'last_update_time': self._get_current_time(),
                    'update_count': index.metadata.get('update_count', 0) + 1
                }
            )
            
            self.logger.info(f"索引更新完成: {len(merged_entries)} 个词汇")
            return updated_index
            
        except Exception as e:
            self.logger.error(f"更新搜索索引失败: {e}")
            raise SearchIndexError(f"更新搜索索引失败: {e}")
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_index_statistics(self, index: SearchIndex) -> Dict[str, Any]:
        """获取索引统计信息
        
        Args:
            index: 搜索索引
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 计算词汇频率分布
            frequencies = [entry.frequency for entry in index.index_entries.values()]
            
            # 计算段落覆盖率
            all_segment_ids = set()
            for entry in index.index_entries.values():
                all_segment_ids.update(entry.segment_ids)
            
            stats = {
                'total_words': len(index.index_entries),
                'total_segments': index.segment_count,
                'covered_segments': len(all_segment_ids),
                'coverage_rate': len(all_segment_ids) / index.segment_count if index.segment_count > 0 else 0,
                'avg_frequency': sum(frequencies) / len(frequencies) if frequencies else 0,
                'max_frequency': max(frequencies) if frequencies else 0,
                'min_frequency': min(frequencies) if frequencies else 0,
                'build_time': index.metadata.get('build_time', ''),
                'config': index.config.__dict__ if index.config else {}
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取索引统计信息失败: {e}")
            return {}
