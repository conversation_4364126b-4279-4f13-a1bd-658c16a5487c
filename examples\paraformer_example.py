#!/usr/bin/env python3
"""Paraformer语音识别器使用示例

演示如何使用阿里云Paraformer语音识别器进行音频文件识别。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.recognizer import Speech<PERSON>ecognizer, ParaformerRecognizer
from src.modules.video_processor.audio_engine.file_uploader import FileUploadManager
from src.common.exceptions import SpeechRecognitionError
from src.common.logging import get_logger


def check_environment():
    """检查环境配置"""
    print("=== 环境检查 ===")
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if api_key:
        print(f"✓ DASHSCOPE_API_KEY: {api_key[:10]}...")
    else:
        print("✗ DASHSCOPE_API_KEY 未配置")
        print("  请设置环境变量: export DASHSCOPE_API_KEY='your_api_key'")
    
    # 检查SDK
    try:
        import dashscope
        print("✓ DashScope SDK 已安装")
    except ImportError:
        print("✗ DashScope SDK 未安装")
        print("  请安装: pip install dashscope")
    
    # 检查文件上传配置
    uploader = FileUploadManager()
    available_uploaders = uploader.get_available_uploaders()
    if available_uploaders:
        print(f"✓ 可用的文件上传器: {', '.join(available_uploaders)}")
    else:
        print("✗ 没有可用的文件上传器")
        print("  请配置OSS或使用本地文件服务器")
    
    print()


def test_recognizer_availability():
    """测试识别器可用性"""
    print("=== 识别器可用性测试 ===")
    
    # 测试通过管理器
    speech_recognizer = SpeechRecognizer()
    available_engines = speech_recognizer.get_available_engines()
    print(f"可用的识别引擎: {available_engines}")
    
    if 'paraformer' in available_engines:
        print("✓ Paraformer识别器可用")
    else:
        print("✗ Paraformer识别器不可用")
    
    # 测试直接创建
    paraformer = ParaformerRecognizer()
    if paraformer.is_available():
        print("✓ Paraformer直接创建可用")
        print(f"  当前模型: {paraformer.model}")
        print(f"  支持的模型: {list(paraformer.supported_models.keys())}")
    else:
        print("✗ Paraformer直接创建不可用")
    
    print()


def test_file_upload():
    """测试文件上传功能"""
    print("=== 文件上传测试 ===")
    
    # 创建一个测试文件
    test_file = Path("test_audio.txt")
    test_file.write_text("This is a test file", encoding='utf-8')
    
    try:
        uploader = FileUploadManager()
        
        if uploader.get_available_uploaders():
            print("尝试上传测试文件...")
            url = uploader.upload(str(test_file), uploader='auto')
            print(f"✓ 文件上传成功: {url}")
        else:
            print("✗ 没有可用的文件上传器")
    
    except Exception as e:
        print(f"✗ 文件上传失败: {e}")
    
    finally:
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
    
    print()


def demonstrate_basic_usage():
    """演示基本使用方法"""
    print("=== 基本使用演示 ===")
    
    # 注意：这里使用模拟的音频文件路径
    # 实际使用时请替换为真实的音频文件路径
    audio_file = "path/to/your/audio.wav"
    
    print(f"音频文件: {audio_file}")
    print("注意：这是一个示例路径，请替换为实际的音频文件路径")
    
    try:
        # 方法1：使用语音识别管理器
        print("\n方法1：使用SpeechRecognizer")
        speech_recognizer = SpeechRecognizer()
        
        if speech_recognizer.is_engine_available('paraformer'):
            print("Paraformer引擎可用，开始识别...")
            # segments = speech_recognizer.recognize(audio_file, language='zh', engine='paraformer')
            # print(f"识别完成，共 {len(segments)} 个段落")
            print("（由于没有实际音频文件，此处跳过实际识别）")
        else:
            print("Paraformer引擎不可用")
        
        # 方法2：直接使用Paraformer识别器
        print("\n方法2：直接使用ParaformerRecognizer")
        paraformer = ParaformerRecognizer(model='paraformer-v2')
        
        if paraformer.is_available():
            print("Paraformer识别器可用")
            print(f"使用模型: {paraformer.model}")
            # segments = paraformer.recognize(audio_file, language='zh')
            # print(f"识别完成，共 {len(segments)} 个段落")
            print("（由于没有实际音频文件，此处跳过实际识别）")
        else:
            print("Paraformer识别器不可用")
    
    except SpeechRecognitionError as e:
        print(f"识别过程出错: {e}")
    except Exception as e:
        print(f"未知错误: {e}")
    
    print()


def demonstrate_advanced_features():
    """演示高级功能"""
    print("=== 高级功能演示 ===")
    
    # 模型信息展示
    paraformer = ParaformerRecognizer()
    
    print("支持的模型详情:")
    for model_name, model_info in paraformer.supported_models.items():
        print(f"\n模型: {model_name}")
        print(f"  描述: {model_info['description']}")
        print(f"  支持语言: {', '.join(model_info['languages'])}")
        print(f"  采样率: {model_info['sample_rates']}")
        print(f"  特色功能: {', '.join(model_info['features'])}")
    
    # 语言代码转换测试
    print("\n语言代码转换测试:")
    test_languages = ['zh', 'en', 'ja', 'ko', 'zh-cn', 'yue', 'unknown']
    for lang in test_languages:
        hints = paraformer._convert_language_hints(lang)
        print(f"  {lang} -> {hints}")
    
    print()


def show_configuration_guide():
    """显示配置指南"""
    print("=== 配置指南 ===")
    
    print("1. 安装依赖:")
    print("   pip install dashscope")
    print("   pip install oss2  # 可选，用于文件上传")
    
    print("\n2. 配置API密钥:")
    print("   export DASHSCOPE_API_KEY='your_api_key_here'")
    
    print("\n3. 配置文件上传（可选）:")
    print("   export OSS_ACCESS_KEY_ID='your_access_key_id'")
    print("   export OSS_ACCESS_KEY_SECRET='your_access_key_secret'")
    print("   export OSS_ENDPOINT='https://oss-cn-beijing.aliyuncs.com'")
    print("   export OSS_BUCKET_NAME='your_bucket_name'")
    
    print("\n4. 获取API密钥:")
    print("   访问: https://bailian.console.aliyun.com/")
    print("   在API-KEY管理页面创建新的API密钥")
    
    print()


def main():
    """主函数"""
    logger = get_logger(__name__)
    logger.info("开始Paraformer识别器示例演示")
    
    print("Paraformer语音识别器使用示例")
    print("=" * 50)
    
    # 检查环境
    check_environment()
    
    # 测试识别器可用性
    test_recognizer_availability()
    
    # 测试文件上传
    test_file_upload()
    
    # 演示基本使用
    demonstrate_basic_usage()
    
    # 演示高级功能
    demonstrate_advanced_features()
    
    # 显示配置指南
    show_configuration_guide()
    
    print("示例演示完成！")
    print("\n如需实际使用，请:")
    print("1. 配置好API密钥和文件上传")
    print("2. 准备音频文件")
    print("3. 参考上述示例代码进行调用")


if __name__ == '__main__':
    main()
