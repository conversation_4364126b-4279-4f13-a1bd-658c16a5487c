"""核心数据模型

定义VideoReader应用的核心数据结构。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from datetime import datetime
from pathlib import Path


class ProcessingStatus(Enum):
    """处理状态枚举"""
    IDLE = "idle"
    LOADING = "loading"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ParserType(Enum):
    """解析器类型枚举"""
    SCENE_CHANGE = "scene_change"
    TEXT_LENGTH = "text_length"
    TIME_FIXED = "time_fixed"
    SILENCE_BASED = "silence_based"


class ExportFormat(Enum):
    """导出格式枚举"""
    TXT = "txt"
    DOCX = "docx"
    PDF = "pdf"
    SRT = "srt"
    VTT = "vtt"
    JSON = "json"
    CSV = "csv"


@dataclass
class VideoInfo:
    """视频信息数据模型"""
    file_path: str
    file_name: str
    file_size: int
    duration: float
    width: int
    height: int
    fps: float
    codec: str
    has_audio: bool
    audio_codec: Optional[str] = None
    bitrate: Optional[int] = None
    creation_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if isinstance(self.file_path, str):
            self.file_path = str(Path(self.file_path).resolve())


@dataclass
class AudioInfo:
    """音频信息数据模型"""
    file_path: str
    duration: float
    sample_rate: int
    channels: int
    codec: str
    bitrate: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TranscriptSegment:
    """转录段落数据模型"""
    start_time: float
    end_time: float
    text: str
    confidence: float
    language: Optional[str] = None
    speaker: Optional[str] = None
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time


@dataclass
class SegmentInfo:
    """段落信息数据模型"""
    id: int
    start_time: float
    end_time: float
    text: str
    summary: Optional[str] = None
    confidence: float = 1.0
    key_frame_path: Optional[str] = None
    thumbnail_path: Optional[str] = None
    transcript_segments: List[TranscriptSegment] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time
    
    @property
    def word_count(self) -> int:
        return len(self.text.split()) if self.text else 0


@dataclass
class ProcessConfig:
    """处理配置数据模型"""
    parser_type: ParserType
    parser_config: Dict[str, Any]
    language: str = 'zh'
    speech_engine: str = 'whisper'
    enable_cache: bool = True
    output_dir: Optional[str] = None
    generate_thumbnails: bool = True
    generate_summaries: bool = False
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        if not isinstance(self.parser_config, dict):
            return False
        
        # 根据解析器类型验证配置
        if self.parser_type == ParserType.SCENE_CHANGE:
            required_keys = ['threshold']
        elif self.parser_type == ParserType.TEXT_LENGTH:
            required_keys = ['min_length', 'max_length']
        elif self.parser_type == ParserType.TIME_FIXED:
            required_keys = ['segment_duration']
        elif self.parser_type == ParserType.SILENCE_BASED:
            required_keys = ['silence_threshold']
        else:
            return False
        
        return all(key in self.parser_config for key in required_keys)


@dataclass
class ProcessResult:
    """处理结果数据模型"""
    video_info: VideoInfo
    segments: List[SegmentInfo]
    parser_type: ParserType
    parser_config: Dict[str, Any]
    processing_time: float
    transcript_path: Optional[str] = None
    total_segments: int = 0
    created_time: datetime = field(default_factory=datetime.now)
    version: str = "1.0.0"
    
    def __post_init__(self):
        if self.total_segments == 0:
            self.total_segments = len(self.segments)
    
    @property
    def total_duration(self) -> float:
        return self.video_info.duration
    
    @property
    def total_text_length(self) -> int:
        return sum(len(segment.text) for segment in self.segments)
    
    @property
    def average_segment_duration(self) -> float:
        if not self.segments:
            return 0.0
        return sum(segment.duration for segment in self.segments) / len(self.segments)


@dataclass
class SearchQuery:
    """搜索查询数据模型"""
    query: str
    search_type: str = "full_text"
    time_start: Optional[float] = None
    time_end: Optional[float] = None
    case_sensitive: bool = False
    max_results: int = 100
    
    def validate(self) -> bool:
        """验证查询的有效性"""
        if not self.query or not self.query.strip():
            return False
        
        if self.time_start is not None and self.time_start < 0:
            return False
        
        if self.time_end is not None and self.time_end < 0:
            return False
        
        if (self.time_start is not None and self.time_end is not None and 
            self.time_start >= self.time_end):
            return False
        
        return True


@dataclass
class SearchResult:
    """搜索结果数据模型"""
    segment: SegmentInfo
    matches: List[Dict[str, Any]]
    relevance_score: float
    highlight_text: Optional[str] = None
    
    @property
    def match_count(self) -> int:
        return len(self.matches)


@dataclass
class ApplicationState:
    """应用状态数据模型"""
    current_video: Optional[VideoInfo] = None
    current_process_result: Optional[ProcessResult] = None
    processing_status: ProcessingStatus = ProcessingStatus.IDLE
    current_segment_id: Optional[int] = None
    playback_position: float = 0.0
    is_playing: bool = False
    recent_files: List[str] = field(default_factory=list)
    last_search_query: Optional[SearchQuery] = None
    last_search_results: List[SearchResult] = field(default_factory=list)
    
    def add_recent_file(self, file_path: str, max_recent: int = 10):
        """添加最近打开的文件"""
        file_path = str(Path(file_path).resolve())
        
        # 移除已存在的相同路径
        if file_path in self.recent_files:
            self.recent_files.remove(file_path)
        
        # 添加到开头
        self.recent_files.insert(0, file_path)
        
        # 限制数量
        if len(self.recent_files) > max_recent:
            self.recent_files = self.recent_files[:max_recent]
    
    def get_current_segment(self) -> Optional[SegmentInfo]:
        """获取当前段落"""
        if (self.current_process_result and 
            self.current_segment_id is not None and
            0 <= self.current_segment_id < len(self.current_process_result.segments)):
            return self.current_process_result.segments[self.current_segment_id]
        return None
    
    def find_segment_by_time(self, timestamp: float) -> Optional[SegmentInfo]:
        """根据时间戳查找段落"""
        if not self.current_process_result:
            return None
        
        for segment in self.current_process_result.segments:
            if segment.start_time <= timestamp <= segment.end_time:
                return segment
        
        return None
    
    def reset(self):
        """重置应用状态"""
        self.current_video = None
        self.current_process_result = None
        self.processing_status = ProcessingStatus.IDLE
        self.current_segment_id = None
        self.playback_position = 0.0
        self.is_playing = False
        self.last_search_query = None
        self.last_search_results.clear()


@dataclass
class MetadataInfo:
    """元数据信息数据模型"""
    video_path: str
    process_result: ProcessResult
    created_time: datetime
    modified_time: datetime
    version: str
    checksum: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    notes: str = ""
    
    def __post_init__(self):
        if isinstance(self.video_path, str):
            self.video_path = str(Path(self.video_path).resolve())


@dataclass
class ExportOptions:
    """导出选项数据模型"""
    format: ExportFormat
    output_path: str
    include_timestamps: bool = True
    include_thumbnails: bool = False
    include_metadata: bool = True
    segment_range: Optional[tuple] = None  # (start_index, end_index)
    time_range: Optional[tuple] = None     # (start_time, end_time)
    
    def validate(self) -> bool:
        """验证导出选项"""
        if not self.output_path:
            return False
        
        if self.segment_range:
            start, end = self.segment_range
            if start < 0 or end < start:
                return False
        
        if self.time_range:
            start, end = self.time_range
            if start < 0 or end < start:
                return False
        
        return True
