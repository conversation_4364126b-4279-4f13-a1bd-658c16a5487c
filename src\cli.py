"""VideoReader CLI应用启动器

负责启动和管理命令行界面应用。
"""

import sys
import time
import signal
from pathlib import Path
from typing import Optional

from function_interface import FunctionInterface
from core.models import ParserType, ProcessConfig, ExportFormat, SearchQuery
from common.logging import get_logger
from common.config import get_config
from common.exceptions import VideoReaderError


logger = get_logger(__name__)


class VideoReaderCLI:
    """VideoReader CLI应用类"""
    
    def __init__(self, args):
        self.args = args
        self.logger = get_logger(__name__)
        self.function_interface: Optional[FunctionInterface] = None
        self.interrupted = False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def initialize(self) -> bool:
        """初始化应用"""
        try:
            # 创建功能接口
            self.function_interface = FunctionInterface()
            
            # 初始化功能接口
            if not self.function_interface.initialize("cli"):
                raise VideoReaderError("功能接口初始化失败")
            
            # 设置进度和错误回调
            self.function_interface.progress_callback = self._on_progress_update
            self.function_interface.error_callback = self._on_error_occurred
            
            self.logger.info("CLI应用初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"CLI应用初始化失败: {e}")
            print(f"错误: CLI应用初始化失败 - {e}")
            return False
    
    def run(self) -> int:
        """运行应用"""
        try:
            if not self.initialize():
                return 1
            
            # 启动应用
            self.function_interface.start_application("cli")
            
            # 根据参数执行不同的操作
            if self.args.input:
                return self._process_video_file()
            else:
                return self._interactive_mode()
                
        except KeyboardInterrupt:
            print("\n用户中断操作")
            return 1
        except Exception as e:
            self.logger.error(f"CLI应用运行失败: {e}")
            print(f"错误: {e}")
            return 1
        finally:
            self._shutdown()
    
    def _process_video_file(self) -> int:
        """处理指定的视频文件"""
        try:
            input_path = Path(self.args.input)
            if not input_path.exists():
                print(f"错误: 视频文件不存在 - {input_path}")
                return 1
            
            print(f"正在处理视频文件: {input_path}")
            
            # 加载视频
            if not self.function_interface.load_video(str(input_path)):
                print("错误: 加载视频文件失败")
                return 1
            
            # 创建处理配置
            parser_type = ParserType(self.args.parser)
            config = self._create_process_config()
            
            print(f"使用解析器: {parser_type.value}")
            print(f"语言设置: {self.args.language}")
            
            # 开始处理
            if not self.function_interface.process_video(parser_type, config):
                print("错误: 启动视频处理失败")
                return 1
            
            # 等待处理完成
            self._wait_for_processing()
            
            if self.interrupted:
                return 1
            
            # 导出结果
            if self.args.output:
                output_path = Path(self.args.output)
            else:
                output_path = input_path.parent / f"{input_path.stem}_output.{self.args.export_format}"
            
            export_format = ExportFormat(self.args.export_format)
            
            print(f"正在导出结果到: {output_path}")
            if self.function_interface.export_data(export_format, str(output_path)):
                print("导出完成!")
            else:
                print("警告: 导出失败")
                return 1
            
            return 0
            
        except Exception as e:
            self.logger.error(f"处理视频文件失败: {e}")
            print(f"错误: {e}")
            return 1
    
    def _interactive_mode(self) -> int:
        """交互模式"""
        print("VideoReader CLI 交互模式")
        print("输入 'help' 查看可用命令，输入 'quit' 退出")
        
        while not self.interrupted:
            try:
                command = input("\nVideoReader> ").strip()
                
                if not command:
                    continue
                
                if command.lower() in ['quit', 'exit', 'q']:
                    break
                elif command.lower() in ['help', 'h']:
                    self._show_help()
                elif command.startswith('load '):
                    self._cmd_load(command[5:].strip())
                elif command.startswith('process '):
                    self._cmd_process(command[8:].strip())
                elif command.startswith('search '):
                    self._cmd_search(command[7:].strip())
                elif command.startswith('export '):
                    self._cmd_export(command[7:].strip())
                elif command == 'status':
                    self._cmd_status()
                elif command == 'recent':
                    self._cmd_recent()
                else:
                    print(f"未知命令: {command}")
                    print("输入 'help' 查看可用命令")
                    
            except EOFError:
                break
            except KeyboardInterrupt:
                print("\n使用 'quit' 命令退出")
                continue
        
        print("再见!")
        return 0
    
    def _create_process_config(self) -> dict:
        """创建处理配置"""
        config = {}
        
        if self.args.parser == 'scene_change':
            config['threshold'] = 0.3
        elif self.args.parser == 'text_length':
            config['min_length'] = 50
            config['max_length'] = 500
        elif self.args.parser == 'time_fixed':
            config['segment_duration'] = 60.0
        elif self.args.parser == 'silence_based':
            config['silence_threshold'] = -40.0
        
        return config
    
    def _wait_for_processing(self):
        """等待处理完成"""
        print("处理中", end="")
        
        while self.function_interface.is_processing() and not self.interrupted:
            progress, message = self.function_interface.get_processing_progress()
            
            # 显示进度
            if progress > 0:
                print(f"\r处理进度: {progress*100:.1f}% - {message}", end="")
            else:
                print(".", end="")
            
            sys.stdout.flush()
            time.sleep(0.5)
        
        print()  # 换行
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
可用命令:
  load <file>              - 加载视频文件
  process <parser>         - 处理当前视频 (scene_change|text_length|time_fixed|silence_based)
  search <query>           - 搜索文本内容
  export <format> [path]   - 导出结果 (txt|json|srt|vtt)
  status                   - 显示当前状态
  recent                   - 显示最近打开的文件
  help, h                  - 显示此帮助信息
  quit, exit, q            - 退出程序

示例:
  load /path/to/video.mp4
  process scene_change
  search "关键词"
  export txt output.txt
        """
        print(help_text)
    
    def _cmd_load(self, file_path: str):
        """加载命令"""
        if not file_path:
            print("错误: 请指定视频文件路径")
            return
        
        if self.function_interface.load_video(file_path):
            print(f"视频加载成功: {file_path}")
        else:
            print(f"视频加载失败: {file_path}")
    
    def _cmd_process(self, parser_name: str):
        """处理命令"""
        if not self.function_interface.app_state.current_video:
            print("错误: 请先加载视频文件")
            return
        
        try:
            parser_type = ParserType(parser_name)
            config = self._create_process_config()
            
            if self.function_interface.process_video(parser_type, config):
                print(f"开始处理，使用解析器: {parser_name}")
                self._wait_for_processing()
                
                if not self.interrupted:
                    print("处理完成!")
            else:
                print("启动处理失败")
                
        except ValueError:
            print(f"错误: 无效的解析器类型 - {parser_name}")
            print("可用类型: scene_change, text_length, time_fixed, silence_based")
    
    def _cmd_search(self, query: str):
        """搜索命令"""
        if not query:
            print("错误: 请输入搜索关键词")
            return
        
        if not self.function_interface.app_state.current_process_result:
            print("错误: 请先处理视频文件")
            return
        
        search_query = SearchQuery(query=query, max_results=10)
        results = self.function_interface.search_segments(search_query)
        
        if results:
            print(f"找到 {len(results)} 个结果:")
            for i, result in enumerate(results, 1):
                segment = result.segment
                print(f"\n{i}. 段落 {segment.id} ({segment.start_time:.1f}s - {segment.end_time:.1f}s)")
                print(f"   相关性: {result.relevance_score:.2f}")
                print(f"   内容: {segment.text[:100]}...")
        else:
            print("未找到匹配的结果")
    
    def _cmd_export(self, args: str):
        """导出命令"""
        if not self.function_interface.app_state.current_process_result:
            print("错误: 请先处理视频文件")
            return
        
        parts = args.split()
        if not parts:
            print("错误: 请指定导出格式")
            return
        
        format_name = parts[0]
        output_path = parts[1] if len(parts) > 1 else f"output.{format_name}"
        
        try:
            export_format = ExportFormat(format_name)
            if self.function_interface.export_data(export_format, output_path):
                print(f"导出成功: {output_path}")
            else:
                print("导出失败")
        except ValueError:
            print(f"错误: 无效的导出格式 - {format_name}")
            print("可用格式: txt, json, srt, vtt")
    
    def _cmd_status(self):
        """状态命令"""
        state = self.function_interface.app_state
        
        print("当前状态:")
        if state.current_video:
            print(f"  视频文件: {state.current_video.file_name}")
            print(f"  时长: {state.current_video.duration:.2f}秒")
            print(f"  分辨率: {state.current_video.width}x{state.current_video.height}")
        else:
            print("  未加载视频文件")
        
        if state.current_process_result:
            print(f"  段落数量: {len(state.current_process_result.segments)}")
            print(f"  处理状态: 已完成")
        else:
            print("  处理状态: 未处理")
        
        print(f"  处理状态: {state.processing_status.value}")
    
    def _cmd_recent(self):
        """最近文件命令"""
        recent_files = self.function_interface.get_recent_files()
        
        if recent_files:
            print("最近打开的文件:")
            for i, file_path in enumerate(recent_files, 1):
                print(f"  {i}. {file_path}")
        else:
            print("没有最近打开的文件")
    
    def _on_progress_update(self, progress: float, message: str):
        """进度更新回调"""
        # CLI模式下的进度更新在_wait_for_processing中处理
        pass
    
    def _on_error_occurred(self, message: str, details: str):
        """错误发生回调"""
        print(f"\n错误: {message}")
        if details and self.args.debug:
            print(f"详情: {details}")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.interrupted = True
        print(f"\n接收到信号 {signum}，正在退出...")
        
        # 尝试取消当前处理
        if self.function_interface and self.function_interface.is_processing():
            print("正在取消当前处理...")
            self.function_interface.cancel_processing()
    
    def _shutdown(self):
        """关闭应用"""
        try:
            if self.function_interface:
                self.function_interface.shutdown_application()
        except Exception as e:
            self.logger.error(f"关闭CLI应用失败: {e}")


def main(args) -> int:
    """CLI应用主函数"""
    try:
        app = VideoReaderCLI(args)
        return app.run()
        
    except Exception as e:
        logger.error(f"CLI应用启动失败: {e}")
        print(f"CLI应用启动失败: {e}")
        return 1


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--debug', action='store_true')
    parser.add_argument('--input', type=str)
    parser.add_argument('--output', type=str)
    parser.add_argument('--parser', default='scene_change')
    parser.add_argument('--language', default='zh')
    parser.add_argument('--export-format', default='txt')
    parser.add_argument('--no-cache', action='store_true')
    args = parser.parse_args()
    
    sys.exit(main(args))
