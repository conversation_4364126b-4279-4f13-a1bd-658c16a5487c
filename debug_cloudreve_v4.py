#!/usr/bin/env python3
"""调试Cloudreve V4 API"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def test_cloudreve_v4_api():
    """测试Cloudreve V4 API路径"""
    print("=== 测试Cloudreve V4 API ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    print(f"服务器: {base_url}")
    print(f"用户名: {username}")
    
    try:
        import requests
        
        # Cloudreve V4 可能的API路径
        v4_api_paths = [
            # V4 API路径
            "/api/v4/user/session",
            "/api/v4/auth/login",
            "/api/v4/site/config",
            "/api/v4/user/login",
            
            # 可能的简化路径
            "/api/user/session",
            "/api/auth/login", 
            "/api/login",
            "/api/session",
            
            # 直接路径
            "/session",
            "/auth",
            "/auth/login",
            
            # 检查API版本信息
            "/api",
            "/api/version",
            "/api/v4",
        ]
        
        print(f"\n1. 测试V4 API路径")
        working_endpoints = []
        
        for path in v4_api_paths:
            url = f"{base_url}{path}"
            try:
                # 先用GET测试端点是否存在
                resp = requests.get(url, timeout=5)
                status = resp.status_code
                content_type = resp.headers.get('content-type', '')
                
                print(f"{path}: {status} - {content_type}")
                
                # 如果不是404且不是HTML，可能是有效的API端点
                if status != 404 and 'html' not in content_type.lower():
                    working_endpoints.append(path)
                    print(f"  -> 可能的API端点: {resp.text[:100]}")
                    
            except Exception as e:
                print(f"{path}: 连接失败 - {e}")
        
        print(f"\n找到可能的API端点: {working_endpoints}")
        
        # 2. 尝试登录到找到的端点
        if working_endpoints:
            print(f"\n2. 尝试登录到发现的端点")
            
            for endpoint in working_endpoints:
                if 'session' in endpoint or 'login' in endpoint or 'auth' in endpoint:
                    print(f"\n测试登录端点: {endpoint}")
                    
                    url = f"{base_url}{endpoint}"
                    
                    # 尝试不同的登录数据格式
                    login_formats = [
                        {'username': username, 'password': password},
                        {'userName': username, 'Password': password},
                        {'email': username, 'password': password},
                        {'user': username, 'pass': password},
                    ]
                    
                    for i, login_data in enumerate(login_formats):
                        try:
                            session = requests.Session()
                            session.headers.update({
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            })
                            
                            response = session.post(url, json=login_data, timeout=10)
                            print(f"  格式{i+1} {login_data}: {response.status_code}")
                            
                            if response.status_code == 200:
                                try:
                                    result = response.json()
                                    print(f"    成功! 响应: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}")
                                    
                                    # 检查是否有token
                                    if 'token' in str(result).lower():
                                        print("    ✅ 找到token信息!")
                                        return endpoint, login_data
                                        
                                except:
                                    print(f"    响应内容: {response.text[:100]}")
                            else:
                                print(f"    失败: {response.text[:100]}")
                                
                        except Exception as e:
                            print(f"    异常: {e}")
        
        # 3. 检查是否有API文档或配置信息
        print(f"\n3. 查找API文档")
        doc_paths = [
            "/api-docs",
            "/swagger", 
            "/docs",
            "/api/docs",
            "/openapi.json",
            "/api.json"
        ]
        
        for path in doc_paths:
            try:
                url = f"{base_url}{path}"
                resp = requests.get(url, timeout=5)
                if resp.status_code == 200:
                    print(f"找到文档: {path} - {resp.text[:200]}")
            except:
                pass
                
    except ImportError:
        print("需要安装requests库")
    except Exception as e:
        print(f"测试异常: {e}")
    
    return None, None


def test_direct_cloudreve_v4():
    """直接测试Cloudreve V4的标准API"""
    print(f"\n=== 直接测试Cloudreve V4标准API ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    try:
        import requests
        
        # 根据Cloudreve V4文档，尝试标准登录
        login_url = f"{base_url}/api/v3/user/session"  # V4可能仍使用v3 API路径
        
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
        })
        
        # V4标准登录格式
        login_data = {
            'userName': username,
            'Password': password
        }
        
        print(f"尝试V4标准登录: {login_url}")
        print(f"登录数据: {login_data}")
        
        response = session.post(login_url, json=login_data, timeout=30)
        
        print(f"响应状态: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"JSON解析成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return True
            except:
                print("响应不是JSON格式")
        
    except Exception as e:
        print(f"标准API测试失败: {e}")
    
    return False


def main():
    """主函数"""
    print("Cloudreve V4 API调试")
    print("=" * 50)
    
    # 测试V4 API路径
    endpoint, login_format = test_cloudreve_v4_api()
    
    if endpoint:
        print(f"\n✅ 找到工作的API端点: {endpoint}")
        print(f"✅ 登录格式: {login_format}")
    else:
        print(f"\n❌ 未找到工作的API端点")
        
        # 尝试标准API
        if test_direct_cloudreve_v4():
            print("✅ 标准API可用")
        else:
            print("❌ 标准API也不可用")
            print("\n可能的问题:")
            print("1. Cloudreve V4 API配置未启用")
            print("2. 用户权限不足")
            print("3. 服务器配置问题")
            print("4. 版本不匹配")


if __name__ == "__main__":
    main()
