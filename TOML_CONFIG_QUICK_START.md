# TOML配置快速开始指南

🎉 VideoReader现在支持TOML配置文件！不再需要每次设置环境变量。

## 🚀 快速开始（3步搞定）

### 1️⃣ 运行配置向导
```bash
python scripts/create_config.py
```

### 2️⃣ 测试配置
```bash
python test_toml_config.py
```

### 3️⃣ 开始使用
```python
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer

# 自动从配置文件读取设置
recognizer = ParaformerRecognizer()
segments = recognizer.recognize('your-video.mp4', language='zh')
```

## 📝 配置文件示例

配置文件位置：`~/.videoreader/config.toml`

```toml
# 阿里云Paraformer语音识别
[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"
model = "paraformer-v2"

# Cloudreve文件存储
[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
username = "your-username"
password = "your-password"
# 或者使用访问令牌（推荐）
# token = "your-access-token"

# 阿里云OSS（可选）
[file_uploader.uploaders.oss]
access_key_id = "your-oss-key-id"
access_key_secret = "your-oss-key-secret"
endpoint = "your-oss-endpoint"
bucket_name = "your-bucket-name"
```

## 🔧 配置优先级

1. **代码参数** - 直接传递给函数的参数
2. **TOML配置文件** - `~/.videoreader/config.toml`
3. **环境变量** - 系统环境变量

## 📚 更多资源

- **详细配置指南**: [doc/toml_configuration_guide.md](doc/toml_configuration_guide.md)
- **Cloudreve集成**: [CLOUDREVE_QUICK_START.md](CLOUDREVE_QUICK_START.md)
- **使用示例**: `python example_toml_usage.py`
- **配置测试**: `python test_toml_config.py`

## 🎯 常用命令

```bash
# 创建配置文件
python scripts/create_config.py

# 测试TOML配置
python test_toml_config.py

# 测试Cloudreve上传
python test_cloudreve_uploader.py

# 完整集成测试
python test_paraformer_cloudreve_integration.py

# 查看使用示例
python example_toml_usage.py
```

## ✨ 主要优势

- 🚫 **不再需要环境变量** - 配置集中管理
- 📁 **结构化配置** - 清晰的层次结构
- 💬 **支持注释** - 配置更易理解
- 🔄 **向后兼容** - 现有环境变量仍然有效
- 🛠️ **配置向导** - 交互式配置体验

## 🆘 需要帮助？

1. 查看详细文档：`doc/toml_configuration_guide.md`
2. 运行示例代码：`python example_toml_usage.py`
3. 检查配置状态：`python scripts/create_config.py` → 选择"显示配置信息"

---

🎉 享受更简单的配置体验！
