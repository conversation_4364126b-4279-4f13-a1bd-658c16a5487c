"""搜索与过滤组件

独立的搜索与过滤功能组件，支持隐藏/显示，支持选择搜索范围。
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QLineEdit, QComboBox, QGroupBox, QRadioButton, QButtonGroup
)
from PySide6.QtCore import Qt, Signal, QTimer
from typing import Dict, Any

try:
    from ...common.logging import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from src.common.logging import get_logger


logger = get_logger(__name__)


class SearchFilterWidget(QWidget):
    """搜索与过滤组件"""
    
    # 信号定义
    search_requested = Signal(dict)  # 搜索请求信号，包含搜索参数
    filter_changed = Signal(dict)    # 过滤条件改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 搜索和过滤状态
        self.search_text = ""
        self.search_scope = "current"  # "current" 或 "all"
        self.filter_criteria = {}
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()
        
        self.logger.info("搜索与过滤组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 搜索区域
        search_group = QGroupBox("搜索")
        search_layout = QVBoxLayout(search_group)
        
        # 搜索范围选择
        scope_layout = QHBoxLayout()
        scope_layout.addWidget(QLabel("搜索范围:"))
        
        self.scope_button_group = QButtonGroup()
        
        self.current_video_radio = QRadioButton("当前视频")
        self.current_video_radio.setChecked(True)
        self.scope_button_group.addButton(self.current_video_radio, 0)
        scope_layout.addWidget(self.current_video_radio)
        
        self.all_videos_radio = QRadioButton("全部视频")
        self.scope_button_group.addButton(self.all_videos_radio, 1)
        scope_layout.addWidget(self.all_videos_radio)
        
        scope_layout.addStretch()
        search_layout.addLayout(scope_layout)
        
        # 搜索框
        search_input_layout = QHBoxLayout()
        search_input_layout.addWidget(QLabel("关键词:"))
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索段落内容...")
        self.search_edit.textChanged.connect(self._on_search_text_changed)
        search_input_layout.addWidget(self.search_edit)
        
        self.search_button = QPushButton("搜索")
        self.search_button.clicked.connect(self._perform_search)
        search_input_layout.addWidget(self.search_button)
        
        search_layout.addLayout(search_input_layout)
        layout.addWidget(search_group)
        
        # 过滤区域
        filter_group = QGroupBox("过滤")
        filter_layout = QHBoxLayout(filter_group)
        
        # 时长过滤
        filter_layout.addWidget(QLabel("时长:"))
        self.duration_filter = QComboBox()
        self.duration_filter.addItems(["全部", "短段落(<30s)", "中等段落(30s-2m)", "长段落(>2m)"])
        self.duration_filter.currentTextChanged.connect(self._on_filter_changed)
        filter_layout.addWidget(self.duration_filter)
        
        # 置信度过滤
        filter_layout.addWidget(QLabel("置信度:"))
        self.confidence_filter = QComboBox()
        self.confidence_filter.addItems(["全部", "高(>0.8)", "中(0.5-0.8)", "低(<0.5)"])
        self.confidence_filter.currentTextChanged.connect(self._on_filter_changed)
        filter_layout.addWidget(self.confidence_filter)
        
        # 清除过滤器
        self.clear_filter_button = QPushButton("清除过滤")
        self.clear_filter_button.clicked.connect(self._clear_filters)
        filter_layout.addWidget(self.clear_filter_button)
        
        filter_layout.addStretch()
        layout.addWidget(filter_group)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 搜索范围改变
        self.scope_button_group.buttonClicked.connect(self._on_scope_changed)
    
    def _on_search_text_changed(self, text: str):
        """搜索文本改变处理"""
        self.search_text = text
        
        # 延迟搜索，避免频繁更新
        if hasattr(self, '_search_timer'):
            self._search_timer.stop()
        
        self._search_timer = QTimer()
        self._search_timer.timeout.connect(self._perform_search)
        self._search_timer.setSingleShot(True)
        self._search_timer.start(300)  # 300ms延迟
    
    def _on_scope_changed(self, button):
        """搜索范围改变处理"""
        if button == self.current_video_radio:
            self.search_scope = "current"
        else:
            self.search_scope = "all"
        
        self.logger.debug(f"搜索范围改变为: {self.search_scope}")
        # 如果有搜索内容，重新执行搜索
        if self.search_text:
            self._perform_search()
    
    def _perform_search(self):
        """执行搜索"""
        try:
            search_params = {
                'text': self.search_text,
                'scope': self.search_scope,
                'duration_filter': self.duration_filter.currentText(),
                'confidence_filter': self.confidence_filter.currentText()
            }
            
            self.search_requested.emit(search_params)
            self.logger.debug(f"执行搜索: {search_params}")
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
    
    def _on_filter_changed(self):
        """过滤条件改变处理"""
        filter_params = {
            'duration_filter': self.duration_filter.currentText(),
            'confidence_filter': self.confidence_filter.currentText()
        }
        
        self.filter_changed.emit(filter_params)
        self.logger.debug(f"过滤条件改变: {filter_params}")
        
        # 如果有搜索内容，重新执行搜索
        if self.search_text:
            self._perform_search()
    
    def _clear_filters(self):
        """清除所有过滤条件"""
        self.search_edit.clear()
        self.duration_filter.setCurrentIndex(0)
        self.confidence_filter.setCurrentIndex(0)
        self.current_video_radio.setChecked(True)
        self.search_scope = "current"
        
        # 发送清除信号
        self.search_requested.emit({
            'text': '',
            'scope': 'current',
            'duration_filter': '全部',
            'confidence_filter': '全部'
        })
        
        self.logger.debug("清除所有过滤条件")
    
    def get_search_params(self) -> Dict[str, Any]:
        """获取当前搜索参数"""
        return {
            'text': self.search_text,
            'scope': self.search_scope,
            'duration_filter': self.duration_filter.currentText(),
            'confidence_filter': self.confidence_filter.currentText()
        }
    
    def set_search_text(self, text: str):
        """设置搜索文本"""
        self.search_edit.setText(text)
    
    def set_search_scope(self, scope: str):
        """设置搜索范围"""
        if scope == "current":
            self.current_video_radio.setChecked(True)
        else:
            self.all_videos_radio.setChecked(True)
        self.search_scope = scope
