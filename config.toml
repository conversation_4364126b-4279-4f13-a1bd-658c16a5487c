# VideoReader 配置文件
# 这是一个TOML格式的配置文件，用于配置VideoReader的各种功能

[app]
name = "VideoReader"
version = "1.0.0"
debug = false
log_level = "INFO"

[paths]
data_dir = "~/.videoreader"
cache_dir = "~/.videoreader/cache"
output_dir = "~/.videoreader/output"
temp_dir = "~/.videoreader/temp"
log_dir = "~/.videoreader/logs"

[video]
supported_formats = [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm"]
max_file_size = 2147483648  # 2GB
default_fps = 30
thumbnail_size = [320, 240]
keyframe_size = [640, 480]

[audio]
supported_formats = [".mp3", ".wav", ".aac", ".ogg", ".m4a"]
sample_rate = 16000
channels = 1
bit_depth = 16

# 语音识别引擎配置
[audio.speech_engines.whisper]
model = "base"
language = "zh"
device = "auto"

[audio.speech_engines.azure]
region = "eastasia"
language = "zh-CN"
api_key = ""  # 请填入您的Azure语音服务API密钥
endpoint = ""  # 请填入您的Azure语音服务端点

[audio.speech_engines.paraformer]
model = "paraformer-v2"
language = "zh"
api_key = ""  # 请填入您的阿里云DashScope API密钥
endpoint = "https://dashscope.aliyuncs.com"

[parsers.scene_change]
threshold = 0.3
min_duration = 2.0
max_duration = 300.0

[parsers.text_length]
min_length = 50
max_length = 500
overlap_ratio = 0.1

[parsers.time_fixed]
segment_duration = 60.0
overlap_duration = 5.0

[parsers.silence_based]
silence_threshold = -40  # dB
min_silence_duration = 1.0
min_segment_duration = 5.0

[storage]
metadata_format = "json"
compression = true
backup_enabled = true
max_backups = 5
cache_size = 104857600  # 100MB
export_formats = ["txt", "docx", "pdf", "srt", "vtt", "json"]

[search]
index_type = "whoosh"
max_results = 100
highlight_enabled = true
fuzzy_search = true
min_query_length = 2

[ui]
theme = "light"
language = "zh_CN"
window_size = [1200, 800]
window_position = "center"
auto_save = true
auto_save_interval = 300  # 5分钟
recent_files_count = 10

[performance]
max_workers = 4
chunk_size = 1048576  # 1MB
memory_limit = 536870912  # 512MB
timeout = 300  # 5分钟
retry_count = 3

[network]
timeout = 30
max_retries = 3
proxy = ""  # 代理设置，如 "http://proxy.example.com:8080"
user_agent = "VideoReader/1.0.0"

# 文件上传器配置
[file_uploader]
default_uploader = "auto"  # auto, cloudreve, oss, http, local

# Cloudreve文件存储配置
[file_uploader.uploaders.cloudreve]
base_url = "http://150.158.84.19:32203"  # 请填入您的Cloudreve服务器地址，如 "http://your-server.com:5212"
username = "stoarg"  # 请填入您的Cloudreve用户名
password = "csj@123_456"  # 请填入您的Cloudreve密码
token = ""     # 或者填入访问令牌（推荐）
timeout = 60
chunk_size = 5242880  # 5MB
max_retries = 3

# 阿里云OSS配置
[file_uploader.uploaders.oss]
access_key_id = ""     # 请填入您的阿里云OSS Access Key ID
access_key_secret = "" # 请填入您的阿里云OSS Access Key Secret
endpoint = ""          # 请填入您的阿里云OSS端点
bucket_name = ""       # 请填入您的阿里云OSS存储桶名称
timeout = 60

# 本地HTTP服务器配置
[file_uploader.uploaders.http]
port = 8000
temp_dir = "temp_files"

# 本地文件服务器配置
[file_uploader.uploaders.local]
base_url = "http://localhost:8000"
upload_dir = "uploads"
