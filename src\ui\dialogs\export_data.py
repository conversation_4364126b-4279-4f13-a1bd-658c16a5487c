"""数据导出对话框

独立的数据导出对话框，提供各种导出格式和选项。
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox,
    QGroupBox, QCheckBox, QLineEdit, QFileDialog, QMessageBox, QTextEdit
)
from PySide6.QtCore import Signal
from pathlib import Path

try:
    from ...core.models import ExportFormat, ProcessResult
    from ...common.logging import get_logger
except ImportError:
    try:
        # 如果相对导入失败，尝试绝对导入
        from src.core.models import ExportFormat, ProcessResult
        from src.common.logging import get_logger
    except ImportError:
        # 最后尝试直接导入
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from core.models import ExportFormat, ProcessResult
        from common.logging import get_logger


class ExportDataDialog(QDialog):
    """数据导出对话框"""
    
    # 信号定义
    export_requested = Signal(object, str, dict)  # 请求导出数据
    
    def __init__(self, process_result: ProcessResult = None, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 处理结果
        self.process_result = process_result
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()
        
        self.logger.info("数据导出对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("数据导出")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 导出格式选择
        format_group = QGroupBox("导出格式")
        format_layout = QVBoxLayout(format_group)
        
        format_combo_layout = QHBoxLayout()
        format_combo_layout.addWidget(QLabel("格式:"))
        
        self.export_format_combo = QComboBox()
        self.export_format_combo.addItem("文本文件 (.txt)", ExportFormat.TXT)
        self.export_format_combo.addItem("JSON文件 (.json)", ExportFormat.JSON)
        self.export_format_combo.addItem("CSV文件 (.csv)", ExportFormat.CSV)
        self.export_format_combo.addItem("SRT字幕 (.srt)", ExportFormat.SRT)
        self.export_format_combo.addItem("VTT字幕 (.vtt)", ExportFormat.VTT)
        self.export_format_combo.addItem("Word文档 (.docx)", ExportFormat.DOCX)
        self.export_format_combo.addItem("PDF文档 (.pdf)", ExportFormat.PDF)
        self.export_format_combo.currentTextChanged.connect(self._on_format_changed)
        format_combo_layout.addWidget(self.export_format_combo)
        
        format_layout.addLayout(format_combo_layout)
        layout.addWidget(format_group)
        
        # 导出选项
        options_group = QGroupBox("导出选项")
        options_layout = QVBoxLayout(options_group)
        
        self.include_summary_checkbox = QCheckBox("包含摘要")
        self.include_summary_checkbox.setChecked(True)
        options_layout.addWidget(self.include_summary_checkbox)
        
        self.include_metadata_checkbox = QCheckBox("包含元数据")
        options_layout.addWidget(self.include_metadata_checkbox)
        
        self.include_timestamps_checkbox = QCheckBox("包含时间戳")
        self.include_timestamps_checkbox.setChecked(True)
        options_layout.addWidget(self.include_timestamps_checkbox)
        
        self.include_confidence_checkbox = QCheckBox("包含置信度")
        options_layout.addWidget(self.include_confidence_checkbox)
        
        layout.addWidget(options_group)
        
        # 输出文件
        output_group = QGroupBox("输出文件")
        output_layout = QVBoxLayout(output_group)
        
        output_file_layout = QHBoxLayout()
        output_file_layout.addWidget(QLabel("文件路径:"))
        
        self.output_file_edit = QLineEdit()
        self.output_file_edit.setPlaceholderText("选择输出文件路径...")
        output_file_layout.addWidget(self.output_file_edit)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self._on_browse_clicked)
        output_file_layout.addWidget(self.browse_button)
        
        output_layout.addLayout(output_file_layout)
        layout.addWidget(output_group)
        
        # 预览区域
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)
        
        layout.addWidget(preview_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.preview_button = QPushButton("预览")
        self.preview_button.clicked.connect(self._on_preview_clicked)
        button_layout.addWidget(self.preview_button)
        
        self.export_button = QPushButton("导出")
        self.export_button.clicked.connect(self._on_export_clicked)
        self.export_button.setEnabled(False)
        button_layout.addWidget(self.export_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.close)
        button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 更新初始状态
        self._update_export_button_state()
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.output_file_edit.textChanged.connect(self._update_export_button_state)
    
    def _on_format_changed(self):
        """导出格式改变处理"""
        # 更新文件扩展名
        if self.output_file_edit.text():
            current_path = Path(self.output_file_edit.text())
            export_format = self.export_format_combo.currentData()
            new_path = current_path.with_suffix(f".{export_format.value}")
            self.output_file_edit.setText(str(new_path))
    
    def _on_browse_clicked(self):
        """浏览按钮点击处理"""
        export_format = self.export_format_combo.currentData()
        format_name = self.export_format_combo.currentText()
        
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            self, 
            f"导出{format_name}", 
            f"export.{export_format.value}", 
            f"{format_name} (*.{export_format.value});;所有文件 (*)"
        )
        
        if file_path:
            self.output_file_edit.setText(file_path)
    
    def _on_preview_clicked(self):
        """预览按钮点击处理"""
        if not self.process_result:
            QMessageBox.warning(self, "警告", "没有可预览的数据")
            return
        
        try:
            # 生成预览内容
            preview_content = self._generate_preview()
            self.preview_text.setPlainText(preview_content)
            
        except Exception as e:
            self.logger.error(f"生成预览失败: {e}")
            QMessageBox.critical(self, "错误", f"生成预览失败: {e}")
    
    def _on_export_clicked(self):
        """导出按钮点击处理"""
        if not self.process_result:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return
        
        if not self.output_file_edit.text():
            QMessageBox.warning(self, "警告", "请选择输出文件路径")
            return
        
        try:
            # 获取导出选项
            options = self._get_export_options()
            
            # 发送导出请求信号
            export_format = self.export_format_combo.currentData()
            file_path = self.output_file_edit.text()
            
            self.export_requested.emit(export_format, file_path, options)
            
            # 显示成功消息
            QMessageBox.information(self, "成功", "导出请求已发送")
            self.close()
            
        except Exception as e:
            self.logger.error(f"导出失败: {e}")
            QMessageBox.critical(self, "错误", f"导出失败: {e}")
    
    def _generate_preview(self) -> str:
        """生成预览内容"""
        if not self.process_result or not self.process_result.segments:
            return "没有可预览的内容"
        
        export_format = self.export_format_combo.currentData()
        options = self._get_export_options()
        
        # 只显示前3个段落作为预览
        preview_segments = self.process_result.segments[:3]
        
        if export_format == ExportFormat.TXT:
            return self._generate_txt_preview(preview_segments, options)
        elif export_format == ExportFormat.JSON:
            return self._generate_json_preview(preview_segments, options)
        elif export_format == ExportFormat.CSV:
            return self._generate_csv_preview(preview_segments, options)
        elif export_format in [ExportFormat.SRT, ExportFormat.VTT]:
            return self._generate_subtitle_preview(preview_segments, export_format)
        else:
            return f"暂不支持 {export_format.value} 格式的预览"
    
    def _generate_txt_preview(self, segments, options) -> str:
        """生成TXT格式预览"""
        lines = []
        for i, segment in enumerate(segments):
            lines.append(f"=== 段落 {segment.id + 1} ===")
            if options.get('include_timestamps', True):
                lines.append(f"时间: {self._format_time(segment.start_time)} - {self._format_time(segment.end_time)}")
            if options.get('include_confidence', False):
                lines.append(f"置信度: {segment.confidence:.2f}")
            lines.append(f"内容: {segment.text}")
            if options.get('include_summary', True) and segment.summary:
                lines.append(f"摘要: {segment.summary}")
            lines.append("")
        
        lines.append("... (仅显示前3个段落)")
        return "\n".join(lines)
    
    def _generate_json_preview(self, segments, options) -> str:
        """生成JSON格式预览"""
        import json
        
        data = {
            "segments": []
        }
        
        for segment in segments:
            segment_data = {
                "id": segment.id,
                "text": segment.text
            }
            
            if options.get('include_timestamps', True):
                segment_data.update({
                    "start_time": segment.start_time,
                    "end_time": segment.end_time,
                    "duration": segment.duration
                })
            
            if options.get('include_confidence', False):
                segment_data["confidence"] = segment.confidence
            
            if options.get('include_summary', True) and segment.summary:
                segment_data["summary"] = segment.summary
            
            data["segments"].append(segment_data)
        
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    def _generate_csv_preview(self, segments, options) -> str:
        """生成CSV格式预览"""
        lines = []
        
        # 表头
        headers = ["ID", "文本"]
        if options.get('include_timestamps', True):
            headers.extend(["开始时间", "结束时间", "时长"])
        if options.get('include_confidence', False):
            headers.append("置信度")
        if options.get('include_summary', True):
            headers.append("摘要")
        
        lines.append(",".join(headers))
        
        # 数据行
        for segment in segments:
            row = [str(segment.id), f'"{segment.text}"']
            
            if options.get('include_timestamps', True):
                row.extend([
                    str(segment.start_time),
                    str(segment.end_time),
                    str(segment.duration)
                ])
            
            if options.get('include_confidence', False):
                row.append(str(segment.confidence))
            
            if options.get('include_summary', True):
                row.append(f'"{segment.summary or ""}"')
            
            lines.append(",".join(row))
        
        return "\n".join(lines)
    
    def _generate_subtitle_preview(self, segments, format_type) -> str:
        """生成字幕格式预览"""
        lines = []
        
        for i, segment in enumerate(segments):
            if format_type == ExportFormat.SRT:
                lines.append(str(i + 1))
                lines.append(f"{self._format_srt_time(segment.start_time)} --> {self._format_srt_time(segment.end_time)}")
                lines.append(segment.text)
                lines.append("")
            elif format_type == ExportFormat.VTT:
                if i == 0:
                    lines.append("WEBVTT")
                    lines.append("")
                lines.append(f"{self._format_vtt_time(segment.start_time)} --> {self._format_vtt_time(segment.end_time)}")
                lines.append(segment.text)
                lines.append("")
        
        return "\n".join(lines)
    
    def _get_export_options(self) -> dict:
        """获取导出选项"""
        return {
            'include_summary': self.include_summary_checkbox.isChecked(),
            'include_metadata': self.include_metadata_checkbox.isChecked(),
            'include_timestamps': self.include_timestamps_checkbox.isChecked(),
            'include_confidence': self.include_confidence_checkbox.isChecked()
        }
    
    def _update_export_button_state(self):
        """更新导出按钮状态"""
        has_data = self.process_result is not None
        has_output_path = bool(self.output_file_edit.text().strip())
        self.export_button.setEnabled(has_data and has_output_path)
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def _format_srt_time(self, seconds: float) -> str:
        """格式化SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _format_vtt_time(self, seconds: float) -> str:
        """格式化VTT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
    
    def set_process_result(self, process_result: ProcessResult):
        """设置处理结果"""
        self.process_result = process_result
        self._update_export_button_state()
