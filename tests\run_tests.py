"""测试运行脚本

提供便捷的测试运行和报告功能。
"""

import sys
import argparse
import subprocess
from pathlib import Path


def run_pytest(args_list):
    """运行pytest"""
    cmd = [sys.executable, "-m", "pytest"] + args_list
    print(f"运行命令: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent.parent)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="VideoReader 测试运行器")
    
    parser.add_argument(
        "--type", "-t",
        choices=["unit", "integration", "all"],
        default="all",
        help="测试类型"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="生成覆盖率报告"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--fast", "-f",
        action="store_true",
        help="跳过慢速测试"
    )
    
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="并行运行测试"
    )
    
    parser.add_argument(
        "--html-report",
        action="store_true",
        help="生成HTML报告"
    )
    
    parser.add_argument(
        "files",
        nargs="*",
        help="指定测试文件"
    )
    
    args = parser.parse_args()
    
    # 构建pytest参数
    pytest_args = []
    
    # 添加测试目录或文件
    if args.files:
        pytest_args.extend(args.files)
    else:
        test_dir = Path(__file__).parent
        if args.type == "unit":
            pytest_args.append(str(test_dir / "unit"))
        elif args.type == "integration":
            pytest_args.append(str(test_dir / "integration"))
        else:
            pytest_args.append(str(test_dir))
    
    # 添加标记过滤
    if args.fast:
        pytest_args.extend(["-m", "not slow"])
    
    if args.type == "unit":
        pytest_args.extend(["-m", "unit"])
    elif args.type == "integration":
        pytest_args.extend(["-m", "integration"])
    
    # 添加详细输出
    if args.verbose:
        pytest_args.append("-v")
    else:
        pytest_args.append("-q")
    
    # 添加并行运行
    if args.parallel:
        pytest_args.extend(["-n", "auto"])
    
    # 添加覆盖率
    if args.coverage:
        pytest_args.extend([
            "--cov=src",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov"
        ])
    
    # 添加HTML报告
    if args.html_report:
        pytest_args.extend([
            "--html=reports/report.html",
            "--self-contained-html"
        ])
    
    # 其他有用的选项
    pytest_args.extend([
        "--tb=short",  # 简短的traceback
        "--strict-markers",  # 严格的标记检查
        "--disable-warnings"  # 禁用警告
    ])
    
    # 运行测试
    result = run_pytest(pytest_args)
    
    # 输出结果摘要
    if result.returncode == 0:
        print("\n✅ 所有测试通过!")
    else:
        print(f"\n❌ 测试失败，退出码: {result.returncode}")
    
    return result.returncode


if __name__ == "__main__":
    sys.exit(main())
