"""VideoReader 主程序入口

根据命令行参数决定启动GUI界面还是CLI界面。
如果没有参数则默认启动GUI界面。
"""

import sys
import argparse
from pathlib import Path

# 添加src目录到Python路径
src_dir = Path(__file__).parent
sys.path.insert(0, str(src_dir))

# 添加项目根目录到Python路径，以支持相对导入
project_root = src_dir.parent
sys.path.insert(0, str(project_root))

from common.logging import get_logger, set_log_level
from common.config import get_config
from common.exceptions import VideoReaderError


def setup_logging(debug: bool = False):
    """设置日志"""
    if debug:
        set_log_level('DEBUG')
    else:
        set_log_level(get_config('app.log_level', 'INFO'))


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='VideoReader - 视频阅读器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s                           # 启动GUI界面
  %(prog)s --cli                     # 启动CLI界面
  %(prog)s --cli --input video.mp4   # CLI模式处理指定视频
  %(prog)s --debug                   # 启动GUI界面并开启调试模式
        """
    )
    
    # 基本选项
    parser.add_argument(
        '--cli', 
        action='store_true',
        help='启动命令行界面模式'
    )
    
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='开启调试模式'
    )
    
    parser.add_argument(
        '--version', 
        action='version',
        version='VideoReader 1.0.0'
    )
    
    # CLI模式专用选项
    cli_group = parser.add_argument_group('CLI模式选项')
    cli_group.add_argument(
        '--input', '-i',
        type=str,
        help='输入视频文件路径'
    )
    
    cli_group.add_argument(
        '--output', '-o',
        type=str,
        help='输出目录路径'
    )
    
    cli_group.add_argument(
        '--parser',
        choices=['scene_change', 'text_length', 'time_fixed', 'silence_based'],
        default='scene_change',
        help='解析器类型 (默认: scene_change)'
    )
    
    cli_group.add_argument(
        '--language',
        choices=['zh', 'en'],
        default='zh',
        help='语音识别语言 (默认: zh)'
    )
    
    cli_group.add_argument(
        '--export-format',
        choices=['txt', 'json', 'srt', 'vtt'],
        default='txt',
        help='导出格式 (默认: txt)'
    )
    
    cli_group.add_argument(
        '--no-cache',
        action='store_true',
        help='禁用缓存'
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志
        setup_logging(args.debug)
        logger = get_logger(__name__)
        
        logger.info("VideoReader 启动")
        logger.debug(f"命令行参数: {vars(args)}")
        
        # 根据参数选择启动模式
        if args.cli:
            # 启动CLI模式
            logger.info("启动CLI模式")
            from cli import main as cli_main
            return cli_main(args)
        else:
            # 启动GUI模式
            logger.info("启动GUI模式")
            from app import main as gui_main
            return gui_main(args)
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 1
    except VideoReaderError as e:
        print(f"应用错误: {e}")
        return 1
    except Exception as e:
        print(f"未知错误: {e}")
        if '--debug' in sys.argv:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
