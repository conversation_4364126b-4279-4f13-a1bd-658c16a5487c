"""音频处理引擎实现

负责音频提取、语音识别和音频分析功能。
"""

import os
import tempfile
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging

from ..interface import AudioEngineInterface
from .models import AudioInfo, TranscriptSegment, SilenceSegment, AudioProcessingConfig
from .recognizer import SpeechRecognizer
from ....common.exceptions import ProcessingError, SpeechRecognitionError
from ....common.logging import get_logger
from ....common.utils import file_ops


logger = get_logger(__name__)


class AudioEngineImpl(AudioEngineInterface):
    """音频引擎实现类
    
    负责从视频中提取音频、进行语音识别和音频分析。
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.speech_recognizer = SpeechRecognizer()
        self._temp_dir = None
        
        # 检查依赖工具
        self._check_dependencies()
    
    def _check_dependencies(self) -> None:
        """检查必要的依赖工具"""
        try:
            # 检查 ffmpeg
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise ProcessingError("FFmpeg 未安装或不可用")
            
            self.logger.info("FFmpeg 检查通过")
            
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            raise ProcessingError(f"依赖检查失败: {e}")
    
    def extract_audio(self, video_path: str, output_path: Optional[str] = None) -> str:
        """从视频中提取音频
        
        Args:
            video_path: 视频文件路径
            output_path: 输出音频文件路径，如果为None则自动生成
            
        Returns:
            str: 提取的音频文件路径
            
        Raises:
            SpeechRecognitionError: 音频提取失败
        """
        try:
            self.logger.info(f"开始提取音频: {video_path}")

            # 验证输入文件
            if not Path(video_path).exists():
                raise SpeechRecognitionError(f"视频文件不存在: {video_path}")
            
            # 生成输出路径
            if output_path is None:
                if self._temp_dir is None:
                    self._temp_dir = tempfile.mkdtemp(prefix="videoreader_audio_")
                
                video_name = Path(video_path).stem
                output_path = os.path.join(self._temp_dir, f"{video_name}.wav")
            
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 使用 ffmpeg 提取音频
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-vn',  # 不包含视频
                '-acodec', 'pcm_s16le',  # 16位PCM编码
                '-ar', '16000',  # 采样率16kHz
                '-ac', '1',  # 单声道
                '-y',  # 覆盖输出文件
                output_path
            ]
            
            self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                raise SpeechRecognitionError(f"音频提取失败: {result.stderr}")

            # 验证输出文件
            if not Path(output_path).exists():
                raise SpeechRecognitionError("音频提取完成但输出文件不存在")

            self.logger.info(f"音频提取成功: {output_path}")
            return output_path

        except subprocess.TimeoutExpired:
            raise SpeechRecognitionError("音频提取超时")
        except Exception as e:
            self.logger.error(f"音频提取失败: {e}")
            raise SpeechRecognitionError(f"音频提取失败: {e}")
    
    def recognize_speech(self, audio_path: str, language: str = 'zh',
                        engine: str = 'whisper') -> List[TranscriptSegment]:
        """语音识别
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码
            engine: 识别引擎
            
        Returns:
            List[TranscriptSegment]: 识别结果段落列表
            
        Raises:
            SpeechRecognitionError: 语音识别失败
        """
        try:
            self.logger.info(f"开始语音识别: {audio_path}, 语言: {language}, 引擎: {engine}")

            # 验证输入文件
            if not Path(audio_path).exists():
                raise SpeechRecognitionError(f"音频文件不存在: {audio_path}")

            # 使用语音识别器进行识别
            segments = self.speech_recognizer.recognize(audio_path, language, engine)

            self.logger.info(f"语音识别完成，共 {len(segments)} 个段落")
            return segments

        except Exception as e:
            self.logger.error(f"语音识别失败: {e}")
            raise SpeechRecognitionError(f"语音识别失败: {e}")
    
    def detect_silence(self, audio_path: str, threshold: float = -40.0,
                      min_duration: float = 1.0) -> List[Tuple[float, float]]:
        """检测静音段
        
        Args:
            audio_path: 音频文件路径
            threshold: 静音阈值 (dB)
            min_duration: 最小静音时长 (秒)
            
        Returns:
            List[Tuple[float, float]]: 静音段列表 (开始时间, 结束时间)
            
        Raises:
            SpeechRecognitionError: 静音检测失败
        """
        try:
            self.logger.info(f"开始检测静音: {audio_path}")

            # 验证输入文件
            if not Path(audio_path).exists():
                raise SpeechRecognitionError(f"音频文件不存在: {audio_path}")
            
            # 使用 ffmpeg 检测静音
            cmd = [
                'ffmpeg',
                '-i', audio_path,
                '-af', f'silencedetect=noise={threshold}dB:duration={min_duration}',
                '-f', 'null',
                '-'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            # 解析静音检测结果
            silence_segments = self._parse_silence_output(result.stderr)
            
            self.logger.info(f"静音检测完成，共 {len(silence_segments)} 个静音段")
            return silence_segments
            
        except subprocess.TimeoutExpired:
            raise SpeechRecognitionError("静音检测超时")
        except Exception as e:
            self.logger.error(f"静音检测失败: {e}")
            raise SpeechRecognitionError(f"静音检测失败: {e}")
    
    def get_audio_info(self, audio_path: str) -> Dict[str, Any]:
        """获取音频信息
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            Dict[str, Any]: 音频信息
            
        Raises:
            SpeechRecognitionError: 获取音频信息失败
        """
        try:
            self.logger.debug(f"获取音频信息: {audio_path}")

            # 验证输入文件
            if not Path(audio_path).exists():
                raise SpeechRecognitionError(f"音频文件不存在: {audio_path}")
            
            # 使用 ffprobe 获取音频信息
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                raise SpeechRecognitionError(f"获取音频信息失败: {result.stderr}")

            # 解析JSON结果
            import json
            info = json.loads(result.stdout)

            # 提取音频流信息
            audio_stream = None
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'audio':
                    audio_stream = stream
                    break

            if not audio_stream:
                raise SpeechRecognitionError("未找到音频流")
            
            # 构建音频信息
            audio_info = {
                'file_path': audio_path,
                'file_name': Path(audio_path).name,
                'file_size': int(info.get('format', {}).get('size', 0)),
                'duration': float(info.get('format', {}).get('duration', 0)),
                'sample_rate': int(audio_stream.get('sample_rate', 0)),
                'channels': int(audio_stream.get('channels', 0)),
                'codec': audio_stream.get('codec_name', ''),
                'bitrate': int(audio_stream.get('bit_rate', 0)) if audio_stream.get('bit_rate') else None,
                'bit_depth': int(audio_stream.get('bits_per_sample', 16))
            }
            
            return audio_info
            
        except subprocess.TimeoutExpired:
            raise SpeechRecognitionError("获取音频信息超时")
        except Exception as e:
            self.logger.error(f"获取音频信息失败: {e}")
            raise SpeechRecognitionError(f"获取音频信息失败: {e}")
    
    def _parse_silence_output(self, output: str) -> List[Tuple[float, float]]:
        """解析静音检测输出"""
        import re
        
        silence_segments = []
        silence_start = None
        
        for line in output.split('\n'):
            # 匹配静音开始
            start_match = re.search(r'silence_start: ([\d.]+)', line)
            if start_match:
                silence_start = float(start_match.group(1))
            
            # 匹配静音结束
            end_match = re.search(r'silence_end: ([\d.]+)', line)
            if end_match and silence_start is not None:
                silence_end = float(end_match.group(1))
                silence_segments.append((silence_start, silence_end))
                silence_start = None
        
        return silence_segments
    
    def cleanup(self) -> None:
        """清理临时文件"""
        if self._temp_dir and Path(self._temp_dir).exists():
            try:
                import shutil
                shutil.rmtree(self._temp_dir)
                self.logger.info(f"清理临时目录: {self._temp_dir}")
            except Exception as e:
                self.logger.warning(f"清理临时目录失败: {e}")
    
    def __del__(self):
        """析构函数，清理资源"""
        self.cleanup()
