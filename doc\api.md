# VideoReader API 文档

本文档描述了VideoReader应用的主要API接口。

## 目录

- [功能接口层 (FunctionInterface)](#功能接口层-functioninterface)
- [视频预处理模块 (VideoProcessor)](#视频预处理模块-videoprocessor)
- [存储引擎模块 (StorageEngine)](#存储引擎模块-storageengine)
- [搜索引擎模块 (SearchEngine)](#搜索引擎模块-searchengine)
- [用户界面模块 (UIManager)](#用户界面模块-uimanager)
- [数据模型](#数据模型)

## 功能接口层 (FunctionInterface)

功能接口层是整个应用的中央控制器，提供统一的API接口。

### 类: FunctionInterface

#### 初始化和生命周期

```python
def initialize(self, mode: str = "gui") -> bool:
    """初始化应用
    
    Args:
        mode: 启动模式 ("gui" 或 "cli")
        
    Returns:
        bool: 是否初始化成功
    """

def start_application(self, mode: str = "gui") -> None:
    """启动应用
    
    Args:
        mode: 启动模式 ("gui" 或 "cli")
    """

def shutdown_application(self) -> None:
    """关闭应用"""
```

#### 视频处理

```python
def load_video(self, file_path: str) -> bool:
    """加载视频文件
    
    Args:
        file_path: 视频文件路径
        
    Returns:
        bool: 是否加载成功
    """

def process_video(self, parser_type: ParserType, config: Dict[str, Any]) -> bool:
    """处理视频（完整的预处理流程）
    
    Args:
        parser_type: 解析器类型
        config: 处理配置
        
    Returns:
        bool: 是否开始处理成功
    """

def cancel_processing(self) -> bool:
    """取消当前处理
    
    Returns:
        bool: 是否成功取消
    """

def get_processing_progress(self) -> tuple:
    """获取处理进度
    
    Returns:
        tuple: (progress: float, message: str)
    """

def is_processing(self) -> bool:
    """检查是否正在处理
    
    Returns:
        bool: 是否正在处理
    """
```

#### 搜索功能

```python
def search_segments(self, query: SearchQuery) -> List[SearchResult]:
    """搜索段落
    
    Args:
        query: 搜索查询
        
    Returns:
        List[SearchResult]: 搜索结果列表
    """
```

#### 数据导出

```python
def export_data(self, format: ExportFormat, output_path: str, 
               options: Dict[str, Any] = None) -> bool:
    """导出数据
    
    Args:
        format: 导出格式
        output_path: 输出路径
        options: 导出选项
        
    Returns:
        bool: 是否导出成功
    """
```

#### 状态管理

```python
def get_recent_files(self) -> List[str]:
    """获取最近打开的文件列表
    
    Returns:
        List[str]: 最近文件路径列表
    """
```

## 视频预处理模块 (VideoProcessor)

负责视频文件的处理和分析。

### 接口: VideoProcessorInterface

```python
def process_video(self, video_path: str, config: ProcessConfig,
                 progress_callback: Optional[Callable[[float, str], None]] = None) -> ProcessResult:
    """处理视频文件
    
    Args:
        video_path: 视频文件路径
        config: 处理配置
        progress_callback: 进度回调函数
    
    Returns:
        ProcessResult: 处理结果
    """

def get_video_info(self, video_path: str) -> VideoInfo:
    """获取视频信息
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        VideoInfo: 视频信息
    """

def extract_key_frame(self, video_path: str, timestamp: float,
                     size: Optional[tuple] = None) -> np.ndarray:
    """提取关键帧
    
    Args:
        video_path: 视频文件路径
        timestamp: 时间戳（秒）
        size: 输出尺寸 (width, height)
        
    Returns:
        np.ndarray: 关键帧图像
    """

def get_available_parsers(self) -> List[ParserType]:
    """获取可用的解析器
    
    Returns:
        List[ParserType]: 可用的解析器类型列表
    """

def get_parser_default_config(self, parser_type: ParserType) -> Dict[str, Any]:
    """获取解析器默认配置
    
    Args:
        parser_type: 解析器类型
        
    Returns:
        Dict[str, Any]: 默认配置
    """
```

## 存储引擎模块 (StorageEngine)

负责元数据管理、缓存管理和数据导出。

### 接口: StorageEngineInterface

```python
def save_metadata(self, metadata: MetadataInfo, file_path: str) -> bool:
    """保存元数据
    
    Args:
        metadata: 元数据信息
        file_path: 保存路径
        
    Returns:
        bool: 是否保存成功
    """

def load_metadata(self, file_path: str) -> Optional[MetadataInfo]:
    """加载元数据
    
    Args:
        file_path: 元数据文件路径
        
    Returns:
        Optional[MetadataInfo]: 元数据信息
    """

def export_data(self, process_result: ProcessResult, options: ExportOptions) -> bool:
    """导出数据
    
    Args:
        process_result: 处理结果
        options: 导出选项
        
    Returns:
        bool: 是否导出成功
    """

def get_cache_info(self, video_path: str) -> Optional[Dict[str, Any]]:
    """获取缓存信息
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        Optional[Dict[str, Any]]: 缓存信息
    """

def clear_cache(self, video_path: str = None) -> bool:
    """清理缓存
    
    Args:
        video_path: 视频文件路径，如果为None则清理所有缓存
        
    Returns:
        bool: 是否清理成功
    """
```

## 搜索引擎模块 (SearchEngine)

负责构建搜索索引和执行搜索功能。

### 接口: SearchEngineInterface

```python
def build_index(self, segments: List[SegmentInfo]) -> bool:
    """构建搜索索引
    
    Args:
        segments: 段落信息列表
        
    Returns:
        bool: 是否构建成功
    """

def search(self, query: SearchQuery) -> List[SearchResult]:
    """执行搜索
    
    Args:
        query: 搜索查询
        
    Returns:
        List[SearchResult]: 搜索结果列表
    """

def get_suggestions(self, partial_query: str, max_suggestions: int = 10) -> List[str]:
    """获取搜索建议
    
    Args:
        partial_query: 部分查询字符串
        max_suggestions: 最大建议数量
        
    Returns:
        List[str]: 搜索建议列表
    """

def clear_index(self) -> bool:
    """清理索引
    
    Returns:
        bool: 是否清理成功
    """
```

## 用户界面模块 (UIManager)

负责GUI界面的显示和用户交互。

### 接口: UIManagerInterface

```python
def initialize(self) -> bool:
    """初始化界面
    
    Returns:
        bool: 是否初始化成功
    """

def show_main_window(self) -> None:
    """显示主窗口"""

def hide_main_window(self) -> None:
    """隐藏主窗口"""

def register_event_handler(self, event_type: UIEventType, 
                          handler: Callable[[UIEvent], None]) -> None:
    """注册事件处理器
    
    Args:
        event_type: 事件类型
        handler: 事件处理函数
    """

def update_progress(self, progress: float, message: str = "") -> None:
    """更新进度
    
    Args:
        progress: 进度值 (0.0-1.0)
        message: 进度消息
    """

def show_error(self, message: str, details: str = "") -> None:
    """显示错误信息
    
    Args:
        message: 错误消息
        details: 错误详情
    """
```

## 数据模型

### VideoInfo

```python
@dataclass
class VideoInfo:
    """视频信息数据模型"""
    file_path: str
    file_name: str
    file_size: int
    duration: float
    width: int
    height: int
    fps: float
    codec: str
    has_audio: bool
    audio_codec: Optional[str] = None
```

### SegmentInfo

```python
@dataclass
class SegmentInfo:
    """段落信息数据模型"""
    id: int
    start_time: float
    end_time: float
    text: str
    summary: Optional[str] = None
    confidence: float = 1.0
    key_frame_path: Optional[str] = None
    thumbnail_path: Optional[str] = None
```

### ProcessResult

```python
@dataclass
class ProcessResult:
    """处理结果数据模型"""
    video_info: VideoInfo
    segments: List[SegmentInfo]
    parser_type: ParserType
    parser_config: Dict[str, Any]
    processing_time: float
    total_segments: int = 0
```

### SearchQuery

```python
@dataclass
class SearchQuery:
    """搜索查询数据模型"""
    query: str
    search_type: str = "full_text"
    time_start: Optional[float] = None
    time_end: Optional[float] = None
    case_sensitive: bool = False
    max_results: int = 100
```

### SearchResult

```python
@dataclass
class SearchResult:
    """搜索结果数据模型"""
    segment: SegmentInfo
    matches: List[Dict[str, Any]]
    relevance_score: float
    highlight_text: Optional[str] = None
```

## 错误处理

所有API都可能抛出以下异常：

- `VideoReaderError`: 基础异常类
- `VideoLoadError`: 视频加载失败
- `ProcessingError`: 处理过程出错
- `StorageError`: 存储操作失败
- `SearchIndexError`: 搜索索引错误
- `UIError`: 界面操作错误

## 事件系统

应用使用事件系统进行模块间通信：

```python
# 订阅事件
subscribe_event(EventType.VIDEO_LOADED, callback_function)

# 发布事件
publish_event(EventType.VIDEO_PROCESSING_COMPLETED, {'result': process_result})
```

主要事件类型：
- `VIDEO_LOADED`: 视频加载完成
- `VIDEO_PROCESSING_STARTED`: 视频处理开始
- `VIDEO_PROCESSING_PROGRESS`: 视频处理进度更新
- `VIDEO_PROCESSING_COMPLETED`: 视频处理完成
- `SEARCH_COMPLETED`: 搜索完成
- `EXPORT_COMPLETED`: 导出完成

## 配置管理

```python
# 获取配置
value = get_config('key.nested.key', default_value)

# 设置配置
set_config('key.nested.key', new_value)

# 保存配置
save_config()
```

## 示例代码

### 基本使用流程

```python
from src.function_interface import FunctionInterface
from src.core.models import ParserType, SearchQuery, ExportFormat

# 创建功能接口
interface = FunctionInterface()

# 初始化
interface.initialize("cli")

# 加载视频
interface.load_video("/path/to/video.mp4")

# 处理视频
config = {"threshold": 0.3}
interface.process_video(ParserType.SCENE_CHANGE, config)

# 等待处理完成
while interface.is_processing():
    progress, message = interface.get_processing_progress()
    print(f"进度: {progress*100:.1f}% - {message}")
    time.sleep(1)

# 搜索内容
query = SearchQuery(query="关键词", max_results=10)
results = interface.search_segments(query)

# 导出结果
interface.export_data(ExportFormat.TXT, "/path/to/output.txt")

# 关闭应用
interface.shutdown_application()
```
