"""视频分析器

提供高级的视频分析功能，包括场景检测、关键帧提取、运动分析等。
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import time

from .models import (
    FrameInfo, SceneChangeInfo, VideoAnalysisResult, 
    VideoProcessingConfig, FrameExtractionRequest, BatchFrameResult
)
from ....common.exceptions import ProcessingError
from ....common.logging import get_logger


logger = get_logger(__name__)


class VideoAnalyzer:
    """视频分析器
    
    提供视频的高级分析功能，包括场景检测、关键帧提取等。
    """
    
    def __init__(self, config: Optional[VideoProcessingConfig] = None):
        self.config = config or VideoProcessingConfig()
        self.logger = get_logger(__name__)
        self._is_processing = False
        self._should_cancel = False
    
    def analyze_video(self, video_path: str, 
                     progress_callback: Optional[callable] = None) -> VideoAnalysisResult:
        """完整的视频分析
        
        Args:
            video_path: 视频文件路径
            progress_callback: 进度回调函数
            
        Returns:
            VideoAnalysisResult: 分析结果
        """
        try:
            self.logger.info(f"开始分析视频: {video_path}")
            start_time = time.time()
            
            self._is_processing = True
            self._should_cancel = False
            
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ProcessingError(f"无法打开视频文件: {video_path}")
            
            try:
                # 获取视频基本信息
                fps = cap.get(cv2.CAP_PROP_FPS)
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = total_frames / fps if fps > 0 else 0
                
                # 场景检测
                scene_changes = []
                if self.config.scene_detection_enabled:
                    if progress_callback:
                        progress_callback(0.1, "正在检测场景变化...")
                    scene_changes = self._detect_scenes(cap, fps, total_frames, progress_callback)
                
                if self._should_cancel:
                    raise ProcessingError("分析被取消")
                
                # 关键帧提取
                key_frames = []
                if self.config.extract_key_frames:
                    if progress_callback:
                        progress_callback(0.6, "正在提取关键帧...")
                    key_frames = self._extract_key_frames(cap, fps, duration, progress_callback)
                
                analysis_time = time.time() - start_time
                
                # 创建分析结果
                result = VideoAnalysisResult(
                    video_path=video_path,
                    total_frames=total_frames,
                    duration=duration,
                    fps=fps,
                    scene_changes=scene_changes,
                    key_frames=key_frames,
                    analysis_time=analysis_time,
                    metadata={
                        'config': self.config.__dict__,
                        'analysis_date': time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                )
                
                if progress_callback:
                    progress_callback(1.0, "分析完成")
                
                self.logger.info(f"视频分析完成: {len(scene_changes)} 个场景变化, "
                               f"{len(key_frames)} 个关键帧, 耗时 {analysis_time:.2f}s")
                
                return result
                
            finally:
                cap.release()
                self._is_processing = False
                
        except Exception as e:
            self.logger.error(f"视频分析失败: {e}")
            raise ProcessingError(f"视频分析失败: {e}")
    
    def _detect_scenes(self, cap: cv2.VideoCapture, fps: float, total_frames: int,
                      progress_callback: Optional[callable] = None) -> List[SceneChangeInfo]:
        """检测场景变化"""
        scene_changes = []
        prev_frame = None
        frame_index = 0
        
        # 重置视频位置
        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        while True:
            if self._should_cancel:
                break
            
            ret, frame = cap.read()
            if not ret:
                break
            
            # 跳帧处理
            if frame_index % self.config.analysis_frame_skip != 0:
                frame_index += 1
                continue
            
            # 预处理帧
            processed_frame = self._preprocess_frame_for_analysis(frame)
            
            if prev_frame is not None:
                # 计算场景变化
                similarity = self._calculate_frame_similarity(prev_frame, processed_frame)
                
                if similarity < (1.0 - self.config.scene_threshold):
                    timestamp = frame_index / fps
                    confidence = 1.0 - similarity
                    
                    scene_change = SceneChangeInfo(
                        timestamp=timestamp,
                        frame_index=frame_index,
                        confidence=confidence,
                        change_type='histogram',
                        similarity_score=similarity,
                        previous_frame_index=frame_index - self.config.analysis_frame_skip,
                        metadata={'method': 'histogram_comparison'}
                    )
                    
                    scene_changes.append(scene_change)
                    self.logger.debug(f"场景变化: {timestamp:.2f}s, 置信度: {confidence:.3f}")
            
            prev_frame = processed_frame
            frame_index += 1
            
            # 更新进度
            if progress_callback and frame_index % 100 == 0:
                progress = 0.1 + 0.5 * (frame_index / total_frames)
                progress_callback(progress, f"场景检测进度: {frame_index}/{total_frames}")
        
        return scene_changes
    
    def _extract_key_frames(self, cap: cv2.VideoCapture, fps: float, duration: float,
                           progress_callback: Optional[callable] = None) -> List[FrameInfo]:
        """提取关键帧"""
        key_frames = []
        
        # 计算关键帧时间戳
        timestamps = []
        current_time = 0.0
        while current_time < duration:
            timestamps.append(current_time)
            current_time += self.config.key_frame_interval
        
        # 提取帧
        for i, timestamp in enumerate(timestamps):
            if self._should_cancel:
                break
            
            # 设置视频位置
            cap.set(cv2.CAP_PROP_POS_MSEC, timestamp * 1000)
            
            ret, frame = cap.read()
            if ret:
                # 转换颜色格式
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 调整大小
                if self.config.frame_size:
                    frame_rgb = cv2.resize(frame_rgb, self.config.frame_size)
                
                height, width, channels = frame_rgb.shape
                frame_index = int(timestamp * fps)
                
                frame_info = FrameInfo(
                    frame_index=frame_index,
                    timestamp=timestamp,
                    width=width,
                    height=height,
                    channels=channels,
                    data=frame_rgb if not self.config.save_frames else None,
                    metadata={
                        'extraction_method': 'interval',
                        'interval': self.config.key_frame_interval
                    }
                )
                
                key_frames.append(frame_info)
                
                # 保存帧文件
                if self.config.save_frames and self.config.output_dir:
                    self._save_frame(frame_rgb, timestamp, self.config.output_dir)
            
            # 更新进度
            if progress_callback:
                progress = 0.6 + 0.4 * (i / len(timestamps))
                progress_callback(progress, f"关键帧提取: {i+1}/{len(timestamps)}")
        
        return key_frames
    
    def _preprocess_frame_for_analysis(self, frame: np.ndarray) -> np.ndarray:
        """预处理帧用于分析"""
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 缩小尺寸提高处理速度
        small = cv2.resize(gray, (160, 120))
        
        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(small, (5, 5), 0)
        
        return blurred
    
    def _calculate_frame_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """计算帧间相似度"""
        # 使用直方图比较
        hist1 = cv2.calcHist([frame1], [0], None, [256], [0, 256])
        hist2 = cv2.calcHist([frame2], [0], None, [256], [0, 256])
        
        # 归一化直方图
        cv2.normalize(hist1, hist1, 0, 1, cv2.NORM_MINMAX)
        cv2.normalize(hist2, hist2, 0, 1, cv2.NORM_MINMAX)
        
        # 计算相关性
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        
        return max(0.0, correlation)
    
    def _save_frame(self, frame: np.ndarray, timestamp: float, output_dir: str) -> str:
        """保存帧到文件"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            filename = f"frame_{timestamp:.2f}s.{self.config.frame_format}"
            file_path = output_path / filename
            
            # 转换颜色格式用于保存
            if self.config.frame_format.lower() in ['jpg', 'jpeg']:
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                cv2.imwrite(str(file_path), frame_bgr, 
                           [cv2.IMWRITE_JPEG_QUALITY, self.config.frame_quality])
            elif self.config.frame_format.lower() == 'png':
                frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                cv2.imwrite(str(file_path), frame_bgr)
            
            return str(file_path)
            
        except Exception as e:
            self.logger.warning(f"保存帧失败: {e}")
            return ""
    
    def extract_frames_batch(self, request: FrameExtractionRequest) -> BatchFrameResult:
        """批量提取帧"""
        try:
            start_time = time.time()
            
            if not request.validate():
                raise ProcessingError("无效的帧提取请求")
            
            cap = cv2.VideoCapture(request.video_path)
            if not cap.isOpened():
                raise ProcessingError(f"无法打开视频文件: {request.video_path}")
            
            frames = []
            failed_indices = []
            success_count = 0
            
            try:
                for i, timestamp in enumerate(request.timestamps):
                    # 设置视频位置
                    cap.set(cv2.CAP_PROP_POS_MSEC, timestamp * 1000)
                    
                    ret, frame = cap.read()
                    if ret:
                        # 颜色格式转换
                        if request.output_format == 'rgb':
                            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        elif request.output_format == 'gray':
                            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                        # bgr格式保持不变
                        
                        # 调整大小
                        if request.output_size:
                            frame = cv2.resize(frame, request.output_size)
                        
                        # 归一化
                        if request.normalize:
                            frame = frame.astype(np.float32) / 255.0
                        
                        frames.append(frame)
                        success_count += 1
                    else:
                        failed_indices.append(i)
                        # 添加空帧占位
                        if frames:
                            frames.append(frames[-1].copy())
                        else:
                            # 创建默认帧
                            if request.output_size:
                                h, w = request.output_size[1], request.output_size[0]
                            else:
                                h, w = 480, 640
                            
                            if request.output_format == 'gray':
                                default_frame = np.zeros((h, w), dtype=np.uint8)
                            else:
                                default_frame = np.zeros((h, w, 3), dtype=np.uint8)
                            
                            frames.append(default_frame)
                
                processing_time = time.time() - start_time
                
                return BatchFrameResult(
                    request=request,
                    frames=frames,
                    timestamps=request.timestamps,
                    success_count=success_count,
                    failed_indices=failed_indices,
                    processing_time=processing_time
                )
                
            finally:
                cap.release()
                
        except Exception as e:
            self.logger.error(f"批量帧提取失败: {e}")
            raise ProcessingError(f"批量帧提取失败: {e}")
    
    def cancel_processing(self) -> bool:
        """取消处理"""
        self._should_cancel = True
        return True
    
    def is_processing(self) -> bool:
        """检查是否正在处理"""
        return self._is_processing
