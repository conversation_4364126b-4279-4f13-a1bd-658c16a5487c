"""数据导出模块

负责将处理结果导出为各种格式的文件。
"""

import json
import csv
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

from core.models import ProcessResult, ExportFormat, ExportOptions
from common.exceptions import StorageError
from common.logging import get_logger


logger = get_logger(__name__)


class DataExporter:
    """数据导出器
    
    负责将视频处理结果导出为各种格式。
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.supported_formats = {
            ExportFormat.TXT: self._export_to_txt,
            ExportFormat.JSON: self._export_to_json,
            ExportFormat.CSV: self._export_to_csv,
            ExportFormat.SRT: self._export_to_srt,
            ExportFormat.VTT: self._export_to_vtt,
            ExportFormat.DOCX: self._export_to_docx,
            ExportFormat.PDF: self._export_to_pdf
        }
    
    def export_data(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出数据
        
        Args:
            result: 处理结果
            options: 导出选项
            
        Returns:
            bool: 是否导出成功
        """
        try:
            if options.format not in self.supported_formats:
                raise StorageError(f"不支持的导出格式: {options.format.value}")
            
            # 确保输出目录存在
            output_path = Path(options.output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 调用对应的导出方法
            export_func = self.supported_formats[options.format]
            success = export_func(result, options)
            
            if success:
                self.logger.info(f"数据导出成功: {options.output_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            raise StorageError(f"数据导出失败: {e}")
    
    def _export_to_txt(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出为文本文件"""
        try:
            with open(options.output_path, 'w', encoding='utf-8') as f:
                # 写入头部信息
                f.write(f"视频文件: {result.video_info.file_name}\n")
                f.write(f"文件路径: {result.video_info.file_path}\n")
                f.write(f"视频时长: {result.video_info.duration:.2f} 秒\n")
                f.write(f"分辨率: {result.video_info.width}x{result.video_info.height}\n")
                f.write(f"帧率: {result.video_info.fps:.2f} fps\n")
                f.write(f"解析器类型: {result.parser_type.value}\n")
                f.write(f"段落数量: {len(result.segments)}\n")
                f.write(f"处理时间: {result.processing_time:.2f} 秒\n")
                f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")
                
                # 写入段落内容
                for i, segment in enumerate(result.segments, 1):
                    f.write(f"段落 {i}\n")
                    f.write(f"时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s "
                           f"(时长: {segment.duration:.2f}s)\n")
                    
                    if options.include_summary and segment.summary:
                        f.write(f"摘要: {segment.summary}\n")
                    
                    f.write(f"内容: {segment.text}\n")
                    
                    if options.include_metadata and segment.metadata:
                        f.write(f"元数据: {segment.metadata}\n")
                    
                    f.write("-" * 30 + "\n\n")
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出TXT失败: {e}")
            return False
    
    def _export_to_json(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出为JSON文件"""
        try:
            data = {
                'video_info': {
                    'file_name': result.video_info.file_name,
                    'file_path': result.video_info.file_path,
                    'file_size': result.video_info.file_size,
                    'duration': result.video_info.duration,
                    'width': result.video_info.width,
                    'height': result.video_info.height,
                    'fps': result.video_info.fps,
                    'codec': result.video_info.codec,
                    'has_audio': result.video_info.has_audio
                },
                'processing_info': {
                    'parser_type': result.parser_type.value,
                    'processing_time': result.processing_time,
                    'total_segments': len(result.segments),
                    'export_time': datetime.now().isoformat()
                },
                'segments': []
            }
            
            # 添加段落数据
            for segment in result.segments:
                segment_data = {
                    'id': segment.id,
                    'start_time': segment.start_time,
                    'end_time': segment.end_time,
                    'duration': segment.duration,
                    'text': segment.text,
                    'confidence': segment.confidence
                }
                
                if options.include_summary and segment.summary:
                    segment_data['summary'] = segment.summary
                
                if options.include_metadata and segment.metadata:
                    segment_data['metadata'] = segment.metadata
                
                if options.include_timestamps:
                    segment_data['timestamp_formatted'] = f"{segment.start_time:.2f}s - {segment.end_time:.2f}s"
                
                data['segments'].append(segment_data)
            
            # 写入JSON文件
            with open(options.output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出JSON失败: {e}")
            return False
    
    def _export_to_csv(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出为CSV文件"""
        try:
            with open(options.output_path, 'w', newline='', encoding='utf-8') as f:
                # 定义CSV列
                fieldnames = ['id', 'start_time', 'end_time', 'duration', 'text', 'confidence']
                
                if options.include_summary:
                    fieldnames.append('summary')
                
                if options.include_timestamps:
                    fieldnames.append('timestamp_formatted')
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                # 写入段落数据
                for segment in result.segments:
                    row = {
                        'id': segment.id,
                        'start_time': segment.start_time,
                        'end_time': segment.end_time,
                        'duration': segment.duration,
                        'text': segment.text,
                        'confidence': segment.confidence
                    }
                    
                    if options.include_summary:
                        row['summary'] = segment.summary or ''
                    
                    if options.include_timestamps:
                        row['timestamp_formatted'] = f"{segment.start_time:.2f}s - {segment.end_time:.2f}s"
                    
                    writer.writerow(row)
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出CSV失败: {e}")
            return False
    
    def _export_to_srt(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出为SRT字幕文件"""
        try:
            with open(options.output_path, 'w', encoding='utf-8') as f:
                for i, segment in enumerate(result.segments, 1):
                    # SRT格式：序号、时间范围、文本、空行
                    start_time = self._seconds_to_srt_time(segment.start_time)
                    end_time = self._seconds_to_srt_time(segment.end_time)
                    
                    f.write(f"{i}\n")
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{segment.text}\n")
                    f.write("\n")
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出SRT失败: {e}")
            return False
    
    def _export_to_vtt(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出为VTT字幕文件"""
        try:
            with open(options.output_path, 'w', encoding='utf-8') as f:
                f.write("WEBVTT\n\n")
                
                for segment in result.segments:
                    start_time = self._seconds_to_vtt_time(segment.start_time)
                    end_time = self._seconds_to_vtt_time(segment.end_time)
                    
                    f.write(f"{start_time} --> {end_time}\n")
                    f.write(f"{segment.text}\n")
                    f.write("\n")
            
            return True
            
        except Exception as e:
            self.logger.error(f"导出VTT失败: {e}")
            return False
    
    def _export_to_docx(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出为DOCX文档"""
        try:
            # 这里需要安装python-docx库
            from docx import Document
            from docx.shared import Inches
            
            doc = Document()
            
            # 添加标题
            title = doc.add_heading(f'视频分析报告 - {result.video_info.file_name}', 0)
            
            # 添加视频信息
            doc.add_heading('视频信息', level=1)
            info_table = doc.add_table(rows=6, cols=2)
            info_table.style = 'Table Grid'
            
            info_data = [
                ('文件名', result.video_info.file_name),
                ('时长', f"{result.video_info.duration:.2f} 秒"),
                ('分辨率', f"{result.video_info.width}x{result.video_info.height}"),
                ('帧率', f"{result.video_info.fps:.2f} fps"),
                ('解析器', result.parser_type.value),
                ('段落数', str(len(result.segments)))
            ]
            
            for i, (key, value) in enumerate(info_data):
                info_table.cell(i, 0).text = key
                info_table.cell(i, 1).text = value
            
            # 添加段落内容
            doc.add_heading('段落内容', level=1)
            
            for i, segment in enumerate(result.segments, 1):
                doc.add_heading(f'段落 {i}', level=2)
                doc.add_paragraph(f"时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s")
                
                if options.include_summary and segment.summary:
                    doc.add_paragraph(f"摘要: {segment.summary}")
                
                doc.add_paragraph(segment.text)
            
            doc.save(options.output_path)
            return True
            
        except ImportError:
            self.logger.error("导出DOCX需要安装python-docx库: pip install python-docx")
            return False
        except Exception as e:
            self.logger.error(f"导出DOCX失败: {e}")
            return False
    
    def _export_to_pdf(self, result: ProcessResult, options: ExportOptions) -> bool:
        """导出为PDF文档"""
        try:
            # 这里需要安装reportlab库
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            
            doc = SimpleDocTemplate(options.output_path, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # 标题
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # 居中
            )
            story.append(Paragraph(f'视频分析报告 - {result.video_info.file_name}', title_style))
            story.append(Spacer(1, 20))
            
            # 视频信息表格
            story.append(Paragraph('视频信息', styles['Heading2']))
            info_data = [
                ['文件名', result.video_info.file_name],
                ['时长', f"{result.video_info.duration:.2f} 秒"],
                ['分辨率', f"{result.video_info.width}x{result.video_info.height}"],
                ['帧率', f"{result.video_info.fps:.2f} fps"],
                ['解析器', result.parser_type.value],
                ['段落数', str(len(result.segments))]
            ]
            
            info_table = Table(info_data, colWidths=[2*inch, 4*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(info_table)
            story.append(Spacer(1, 20))
            
            # 段落内容
            story.append(Paragraph('段落内容', styles['Heading2']))
            
            for i, segment in enumerate(result.segments, 1):
                story.append(Paragraph(f'段落 {i}', styles['Heading3']))
                story.append(Paragraph(f"时间: {segment.start_time:.2f}s - {segment.end_time:.2f}s", styles['Normal']))
                
                if options.include_summary and segment.summary:
                    story.append(Paragraph(f"摘要: {segment.summary}", styles['Normal']))
                
                story.append(Paragraph(segment.text, styles['Normal']))
                story.append(Spacer(1, 12))
            
            doc.build(story)
            return True
            
        except ImportError:
            self.logger.error("导出PDF需要安装reportlab库: pip install reportlab")
            return False
        except Exception as e:
            self.logger.error(f"导出PDF失败: {e}")
            return False
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _seconds_to_vtt_time(self, seconds: float) -> str:
        """将秒数转换为VTT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
    
    def get_supported_formats(self) -> List[ExportFormat]:
        """获取支持的导出格式"""
        return list(self.supported_formats.keys())
