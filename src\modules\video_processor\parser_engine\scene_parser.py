"""场景变化解析器

基于视频画面变化检测来分割段落。
"""

import numpy as np
from typing import List, Dict, Any, Optional
import cv2

from .base import BaseParser
from ....core.models import VideoInfo, SegmentInfo, ParserType
from ..audio_engine.models import TranscriptSegment
from .models import SceneChangePoint
from ....common.exceptions import ProcessingError
from ....common.logging import get_logger


logger = get_logger(__name__)


class SceneChangeParser(BaseParser):
    """场景变化解析器
    
    通过分析视频帧之间的相似度来检测场景变化点，
    并以此为依据分割视频段落。
    """
    
    def __init__(self):
        super().__init__(ParserType.SCENE_CHANGE)
        self._video_capture = None
    
    def _do_parse(self, video_info: VideoInfo, transcript: List[TranscriptSegment],
                 config: Dict[str, Any]) -> List[SegmentInfo]:
        """执行场景变化解析"""
        try:
            # 获取配置参数
            threshold = config.get('threshold', 0.3)
            min_duration = config.get('min_duration', 2.0)
            max_duration = config.get('max_duration', 300.0)
            frame_skip = config.get('frame_skip', 1)
            use_histogram = config.get('use_histogram', True)
            
            self.logger.info(f"场景变化解析参数: threshold={threshold}, min_duration={min_duration}")
            
            # 检测场景变化点
            scene_changes = self._detect_scene_changes(
                video_info.file_path, threshold, frame_skip, use_histogram
            )
            
            if self.should_cancel():
                raise ProcessingError("解析被取消")
            
            # 生成时间分割点
            split_points = self._generate_split_points(
                scene_changes, video_info.duration, min_duration, max_duration
            )
            
            # 根据分割点创建段落
            segments = self._create_segments_from_splits(
                split_points, transcript, video_info
            )
            
            return segments
            
        except Exception as e:
            self.logger.error(f"场景变化解析失败: {e}")
            raise ProcessingError(f"场景变化解析失败: {e}")
    
    def _detect_scene_changes(self, video_path: str, threshold: float, 
                             frame_skip: int, use_histogram: bool) -> List[SceneChangePoint]:
        """检测场景变化点"""
        scene_changes = []
        
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ProcessingError(f"无法打开视频文件: {video_path}")
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            self.logger.info(f"视频信息: FPS={fps}, 总帧数={total_frames}")
            
            prev_frame = None
            frame_index = 0
            
            while True:
                if self.should_cancel():
                    break
                
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 跳帧处理
                if frame_index % (frame_skip + 1) != 0:
                    frame_index += 1
                    continue
                
                # 预处理帧
                processed_frame = self._preprocess_frame(frame)
                
                if prev_frame is not None:
                    # 计算帧间相似度
                    if use_histogram:
                        similarity = self._calculate_histogram_similarity(prev_frame, processed_frame)
                    else:
                        similarity = self._calculate_pixel_similarity(prev_frame, processed_frame)
                    
                    # 检测场景变化
                    if similarity < (1.0 - threshold):
                        timestamp = frame_index / fps
                        confidence = 1.0 - similarity
                        
                        scene_change = SceneChangePoint(
                            timestamp=timestamp,
                            confidence=confidence,
                            frame_index=frame_index,
                            similarity_score=similarity
                        )
                        
                        scene_changes.append(scene_change)
                        self.logger.debug(f"检测到场景变化: {scene_change}")
                
                prev_frame = processed_frame
                frame_index += 1
                
                # 定期检查取消状态
                if frame_index % 100 == 0:
                    progress = frame_index / total_frames
                    self.logger.debug(f"场景检测进度: {progress:.1%}")
            
            cap.release()
            
            self.logger.info(f"场景变化检测完成: 共检测到 {len(scene_changes)} 个变化点")
            return scene_changes
            
        except Exception as e:
            if cap:
                cap.release()
            raise ProcessingError(f"场景变化检测失败: {e}")
    
    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """预处理视频帧"""
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 调整大小以提高处理速度
        height, width = gray.shape
        if width > 320:
            scale = 320 / width
            new_width = 320
            new_height = int(height * scale)
            gray = cv2.resize(gray, (new_width, new_height))
        
        # 高斯模糊减少噪声
        gray = cv2.GaussianBlur(gray, (5, 5), 0)
        
        return gray
    
    def _calculate_histogram_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """计算直方图相似度"""
        # 计算直方图
        hist1 = cv2.calcHist([frame1], [0], None, [256], [0, 256])
        hist2 = cv2.calcHist([frame2], [0], None, [256], [0, 256])
        
        # 归一化
        cv2.normalize(hist1, hist1, 0, 1, cv2.NORM_MINMAX)
        cv2.normalize(hist2, hist2, 0, 1, cv2.NORM_MINMAX)
        
        # 计算相关性
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        
        return max(0.0, correlation)
    
    def _calculate_pixel_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """计算像素级相似度"""
        # 计算均方误差
        mse = np.mean((frame1.astype(float) - frame2.astype(float)) ** 2)
        
        # 转换为相似度（0-1）
        max_mse = 255 ** 2
        similarity = 1.0 - (mse / max_mse)
        
        return max(0.0, similarity)
    
    def _generate_split_points(self, scene_changes: List[SceneChangePoint],
                              total_duration: float, min_duration: float,
                              max_duration: float) -> List[float]:
        """生成时间分割点"""
        if not scene_changes:
            # 如果没有场景变化，按最大时长分割
            split_points = []
            current_time = 0.0
            while current_time + max_duration < total_duration:
                current_time += max_duration
                split_points.append(current_time)
            return split_points
        
        # 按置信度排序场景变化点
        scene_changes.sort(key=lambda x: x.confidence, reverse=True)
        
        split_points = [0.0]  # 起始点
        
        for change in scene_changes:
            timestamp = change.timestamp
            
            # 检查是否满足最小时长要求
            if timestamp - split_points[-1] >= min_duration:
                # 检查是否会导致过长的段落
                next_valid_point = None
                for next_change in scene_changes:
                    if (next_change.timestamp > timestamp and 
                        next_change.timestamp - timestamp >= min_duration):
                        next_valid_point = next_change.timestamp
                        break
                
                if next_valid_point is None:
                    next_valid_point = total_duration
                
                # 如果段落不会过长，添加分割点
                if next_valid_point - timestamp <= max_duration:
                    split_points.append(timestamp)
        
        # 添加结束点
        if split_points[-1] < total_duration:
            split_points.append(total_duration)
        
        # 移除过近的分割点
        filtered_points = [split_points[0]]
        for point in split_points[1:]:
            if point - filtered_points[-1] >= min_duration:
                filtered_points.append(point)
        
        return filtered_points
    
    def _create_segments_from_splits(self, split_points: List[float],
                                   transcript: List[TranscriptSegment],
                                   video_info: VideoInfo) -> List[SegmentInfo]:
        """根据分割点创建段落"""
        segments = []
        
        for i in range(len(split_points) - 1):
            start_time = split_points[i]
            end_time = split_points[i + 1]
            
            # 获取时间范围内的转录文本
            segment_transcript = self._find_transcript_segments_in_range(
                transcript, start_time, end_time
            )
            
            # 合并文本
            text = self._merge_transcript_segments(transcript, start_time, end_time)
            
            # 生成摘要
            summary = self._generate_summary(text)
            
            # 计算置信度
            confidence = self._calculate_confidence(segment_transcript)
            
            # 创建段落信息
            segment = SegmentInfo(
                id=i,
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                text=text,
                summary=summary,
                confidence=confidence,
                key_frame_path="",  # 稍后生成
                thumbnail_path="",  # 稍后生成
                metadata={
                    'parser_type': self.parser_type.value,
                    'transcript_segments': len(segment_transcript)
                }
            )
            
            segments.append(segment)
        
        return segments
