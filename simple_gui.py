#!/usr/bin/env python3
"""简化的GUI启动脚本

绕过复杂的模块导入问题，直接启动一个简单的GUI界面。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_dir = project_root / "src"
sys.path.insert(0, str(src_dir))

def create_simple_gui():
    """创建简单的GUI界面"""
    try:
        from PySide6.QtWidgets import (
            QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
            QLabel, QPushButton, QFileDialog, QTextEdit, QListWidget,
            QSplitter, QProgressBar, QStatusBar, QMenuBar, QMessageBox
        )
        from PySide6.QtCore import Qt, QTimer
        from PySide6.QtGui import QFont, QIcon
        
        class VideoReaderMainWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("VideoReader - 视频阅读器")
                self.setGeometry(100, 100, 1200, 800)
                
                # 创建中央部件
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                # 创建主布局
                main_layout = QVBoxLayout()
                central_widget.setLayout(main_layout)
                
                # 创建工具栏
                self.create_toolbar(main_layout)
                
                # 创建主要内容区域
                self.create_main_content(main_layout)
                
                # 创建状态栏
                self.create_status_bar()
                
                # 设置样式
                self.setStyleSheet("""
                    QMainWindow {
                        background-color: #f5f5f5;
                    }
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                    QPushButton:pressed {
                        background-color: #3d8b40;
                    }
                    QListWidget {
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background-color: white;
                    }
                    QTextEdit {
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background-color: white;
                        font-family: 'Microsoft YaHei', sans-serif;
                        font-size: 12px;
                    }
                """)
            
            def create_toolbar(self, layout):
                """创建工具栏"""
                toolbar_layout = QHBoxLayout()
                
                # 打开文件按钮
                self.open_button = QPushButton("打开视频")
                self.open_button.clicked.connect(self.open_video)
                toolbar_layout.addWidget(self.open_button)
                
                # 处理按钮
                self.process_button = QPushButton("开始处理")
                self.process_button.setEnabled(False)
                self.process_button.clicked.connect(self.process_video)
                toolbar_layout.addWidget(self.process_button)
                
                # 导出按钮
                self.export_button = QPushButton("导出结果")
                self.export_button.setEnabled(False)
                toolbar_layout.addWidget(self.export_button)
                
                toolbar_layout.addStretch()
                
                # 进度条
                self.progress_bar = QProgressBar()
                self.progress_bar.setVisible(False)
                toolbar_layout.addWidget(self.progress_bar)
                
                layout.addLayout(toolbar_layout)
            
            def create_main_content(self, layout):
                """创建主要内容区域"""
                # 创建分割器
                splitter = QSplitter(Qt.Horizontal)
                
                # 左侧：段落列表
                left_widget = QWidget()
                left_layout = QVBoxLayout()
                left_widget.setLayout(left_layout)
                
                left_layout.addWidget(QLabel("视频段落列表"))
                self.segment_list = QListWidget()
                left_layout.addWidget(self.segment_list)
                
                # 右侧：内容显示
                right_widget = QWidget()
                right_layout = QVBoxLayout()
                right_widget.setLayout(right_layout)
                
                right_layout.addWidget(QLabel("段落内容"))
                self.content_text = QTextEdit()
                self.content_text.setPlainText("欢迎使用VideoReader！\n\n这是一个将视频转换为可阅读文本的工具。\n\n请点击'打开视频'开始使用。")
                right_layout.addWidget(self.content_text)
                
                # 添加到分割器
                splitter.addWidget(left_widget)
                splitter.addWidget(right_widget)
                splitter.setSizes([300, 900])
                
                layout.addWidget(splitter)
            
            def create_status_bar(self):
                """创建状态栏"""
                self.status_bar = QStatusBar()
                self.setStatusBar(self.status_bar)
                self.status_bar.showMessage("就绪")
            
            def open_video(self):
                """打开视频文件"""
                file_path, _ = QFileDialog.getOpenFileName(
                    self,
                    "选择视频文件",
                    "",
                    "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm)"
                )
                
                if file_path:
                    self.status_bar.showMessage(f"已选择视频: {Path(file_path).name}")
                    self.process_button.setEnabled(True)
                    self.content_text.setPlainText(f"已加载视频文件：\n{file_path}\n\n点击'开始处理'来分析视频内容。")
            
            def process_video(self):
                """处理视频"""
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.status_bar.showMessage("正在处理视频...")
                
                # 模拟处理过程
                self.timer = QTimer()
                self.timer.timeout.connect(self.update_progress)
                self.progress_value = 0
                self.timer.start(100)
            
            def update_progress(self):
                """更新进度"""
                self.progress_value += 2
                self.progress_bar.setValue(self.progress_value)
                
                if self.progress_value >= 100:
                    self.timer.stop()
                    self.progress_bar.setVisible(False)
                    self.status_bar.showMessage("处理完成")
                    self.export_button.setEnabled(True)
                    
                    # 添加示例段落
                    for i in range(5):
                        self.segment_list.addItem(f"段落 {i+1} (00:{i*2:02d}:00 - 00:{(i+1)*2:02d}:00)")
                    
                    self.content_text.setPlainText("视频处理完成！\n\n已生成5个视频段落。\n\n请在左侧列表中选择段落查看内容。")
        
        # 创建应用
        print("正在创建QApplication...")
        app = QApplication(sys.argv)
        app.setApplicationName("VideoReader")
        print("QApplication创建成功")

        # 创建主窗口
        print("正在创建主窗口...")
        window = VideoReaderMainWindow()
        print("主窗口创建成功")

        print("正在显示窗口...")
        window.show()

        print("✅ VideoReader GUI启动成功")
        print("PySide6界面已显示，功能正常")
        print("窗口已显示，按Ctrl+C或关闭窗口退出")

        return app.exec()
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("VideoReader 简化GUI启动")
    print("=" * 30)
    return create_simple_gui()

if __name__ == '__main__':
    sys.exit(main())
