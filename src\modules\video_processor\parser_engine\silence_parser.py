"""静音分段解析器

基于音频静音检测来分割段落。
"""

from typing import List, Dict, Any, Tuple

from .base import BaseParser
from ....core.models import VideoInfo, SegmentInfo, ParserType
from ..audio_engine.models import TranscriptSegment
from .models import SilenceSegmentInfo
from ....common.exceptions import ProcessingError


class SilenceBasedParser(BaseParser):
    """静音分段解析器
    
    通过检测音频中的静音段来分割视频段落。
    """
    
    def __init__(self):
        super().__init__(ParserType.SILENCE_BASED)
    
    def _do_parse(self, video_info: VideoInfo, transcript: List[TranscriptSegment],
                 config: Dict[str, Any]) -> List[SegmentInfo]:
        """执行静音分段解析"""
        try:
            # 获取配置参数
            silence_threshold = config.get('silence_threshold', -40.0)
            min_silence_duration = config.get('min_silence_duration', 1.0)
            min_segment_duration = config.get('min_segment_duration', 5.0)
            max_segment_duration = config.get('max_segment_duration', 300.0)
            merge_short_segments = config.get('merge_short_segments', True)
            silence_padding = config.get('silence_padding', 0.1)
            
            self.logger.info(f"静音分段解析参数: threshold={silence_threshold}dB, "
                           f"min_silence={min_silence_duration}s")
            
            # 从转录数据推断静音段（简化实现）
            # 实际应用中应该使用音频分析
            silence_segments = self._detect_silence_from_transcript(
                transcript, min_silence_duration, video_info.duration
            )
            
            # 生成分割点
            split_points = self._generate_splits_from_silence(
                silence_segments, min_segment_duration, max_segment_duration,
                silence_padding, video_info.duration
            )
            
            # 创建段落
            segments = self._create_segments_from_silence_splits(
                split_points, transcript, video_info, merge_short_segments,
                min_segment_duration
            )
            
            return segments
            
        except Exception as e:
            self.logger.error(f"静音分段解析失败: {e}")
            raise ProcessingError(f"静音分段解析失败: {e}")
    
    def _detect_silence_from_transcript(self, transcript: List[TranscriptSegment],
                                       min_silence_duration: float,
                                       total_duration: float) -> List[SilenceSegmentInfo]:
        """从转录数据检测静音段"""
        silence_segments = []
        
        if not transcript:
            return silence_segments
        
        # 按时间排序转录段落
        sorted_transcript = sorted(transcript, key=lambda x: x.start_time)
        
        # 检查开头是否有静音
        if sorted_transcript[0].start_time > min_silence_duration:
            silence_segments.append(SilenceSegmentInfo(
                start_time=0.0,
                end_time=sorted_transcript[0].start_time,
                duration=sorted_transcript[0].start_time,
                confidence=0.8,
                threshold_db=-40.0
            ))
        
        # 检查转录段落之间的间隙
        for i in range(len(sorted_transcript) - 1):
            current_end = sorted_transcript[i].end_time
            next_start = sorted_transcript[i + 1].start_time
            gap_duration = next_start - current_end
            
            if gap_duration >= min_silence_duration:
                silence_segments.append(SilenceSegmentInfo(
                    start_time=current_end,
                    end_time=next_start,
                    duration=gap_duration,
                    confidence=0.9,
                    threshold_db=-40.0
                ))
        
        # 检查结尾是否有静音
        last_end = sorted_transcript[-1].end_time
        if total_duration - last_end > min_silence_duration:
            silence_segments.append(SilenceSegmentInfo(
                start_time=last_end,
                end_time=total_duration,
                duration=total_duration - last_end,
                confidence=0.8,
                threshold_db=-40.0
            ))
        
        return silence_segments
    
    def _generate_splits_from_silence(self, silence_segments: List[SilenceSegmentInfo],
                                     min_segment_duration: float, max_segment_duration: float,
                                     silence_padding: float, total_duration: float) -> List[float]:
        """从静音段生成分割点"""
        split_points = [0.0]
        
        for silence in silence_segments:
            # 在静音段中间作为分割点
            split_time = (silence.start_time + silence.end_time) / 2
            
            # 检查是否满足最小段落时长要求
            if split_time - split_points[-1] >= min_segment_duration:
                split_points.append(split_time)
        
        # 添加结束点
        if split_points[-1] < total_duration:
            split_points.append(total_duration)
        
        # 处理过长的段落
        final_splits = [split_points[0]]
        
        for i in range(1, len(split_points)):
            prev_split = final_splits[-1]
            current_split = split_points[i]
            
            # 如果段落过长，强制分割
            if current_split - prev_split > max_segment_duration:
                # 在中间添加分割点
                num_splits = int((current_split - prev_split) / max_segment_duration)
                for j in range(1, num_splits + 1):
                    intermediate_split = prev_split + j * max_segment_duration
                    if intermediate_split < current_split:
                        final_splits.append(intermediate_split)
            
            final_splits.append(current_split)
        
        return final_splits
    
    def _create_segments_from_silence_splits(self, split_points: List[float],
                                           transcript: List[TranscriptSegment],
                                           video_info: VideoInfo,
                                           merge_short_segments: bool,
                                           min_segment_duration: float) -> List[SegmentInfo]:
        """根据静音分割点创建段落"""
        segments = []
        
        for i in range(len(split_points) - 1):
            start_time = split_points[i]
            end_time = split_points[i + 1]
            
            # 获取时间范围内的转录文本
            segment_transcript = self._find_transcript_segments_in_range(
                transcript, start_time, end_time
            )
            
            # 合并文本
            text = self._merge_transcript_segments(transcript, start_time, end_time)
            
            # 生成摘要
            summary = self._generate_summary(text)
            
            # 计算置信度
            confidence = self._calculate_confidence(segment_transcript)
            
            # 创建段落信息
            segment = SegmentInfo(
                id=i,
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                text=text,
                summary=summary,
                confidence=confidence,
                key_frame_path="",
                thumbnail_path="",
                metadata={
                    'parser_type': self.parser_type.value,
                    'transcript_segments': len(segment_transcript),
                    'has_speech': len(segment_transcript) > 0
                }
            )
            
            segments.append(segment)
        
        # 合并短段落
        if merge_short_segments:
            segments = self._merge_short_segments(segments, min_segment_duration)
        
        return segments
    
    def _merge_short_segments(self, segments: List[SegmentInfo],
                             min_duration: float) -> List[SegmentInfo]:
        """合并过短的段落"""
        if not segments:
            return segments
        
        merged_segments = []
        current_segment = segments[0]
        
        for i in range(1, len(segments)):
            next_segment = segments[i]
            
            # 如果当前段落过短，尝试与下一个段落合并
            if current_segment.duration < min_duration:
                # 合并段落
                merged_text = f"{current_segment.text} {next_segment.text}".strip()
                merged_summary = self._generate_summary(merged_text)
                
                current_segment = SegmentInfo(
                    id=current_segment.id,
                    start_time=current_segment.start_time,
                    end_time=next_segment.end_time,
                    duration=next_segment.end_time - current_segment.start_time,
                    text=merged_text,
                    summary=merged_summary,
                    confidence=(current_segment.confidence + next_segment.confidence) / 2,
                    key_frame_path=current_segment.key_frame_path,
                    thumbnail_path=current_segment.thumbnail_path,
                    metadata={
                        'parser_type': self.parser_type.value,
                        'merged': True,
                        'original_segments': [current_segment.id, next_segment.id]
                    }
                )
            else:
                # 当前段落足够长，添加到结果中
                merged_segments.append(current_segment)
                current_segment = next_segment
        
        # 添加最后一个段落
        merged_segments.append(current_segment)
        
        # 重新分配ID
        for i, segment in enumerate(merged_segments):
            segment.id = i
        
        return merged_segments
