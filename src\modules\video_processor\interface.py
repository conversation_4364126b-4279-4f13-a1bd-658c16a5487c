"""视频预处理模块对外接口

定义视频预处理模块的统一接口，隐藏内部实现细节。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Callable
import numpy as np

try:
    from ...core.models import (
        VideoInfo, ProcessResult, ProcessConfig, ParserType,
        SegmentInfo, TranscriptSegment
    )
    from ...common.exceptions import VideoLoadError, ProcessingError
except ImportError:
    from core.models import (
        VideoInfo, ProcessResult, ProcessConfig, ParserType,
        SegmentInfo, TranscriptSegment
    )
    from common.exceptions import VideoLoadError, ProcessingError


class VideoProcessorInterface(ABC):
    """视频预处理器接口
    
    统一的视频预处理接口，负责协调视频处理、音频处理和解析三个子引擎，
    将原始视频转换为结构化的可阅读数据。
    """
    
    @abstractmethod
    def process_video(self, video_path: str, config: ProcessConfig,
                     progress_callback: Optional[Callable[[float, str], None]] = None) -> ProcessResult:
        """处理视频文件
        
        Args:
            video_path: 视频文件路径
            config: 处理配置
            progress_callback: 进度回调函数 (progress: float, message: str)
        
        Returns:
            ProcessResult: 处理结果
        
        Raises:
            VideoLoadError: 视频加载失败
            ProcessingError: 处理过程出错
        """
        pass
    
    @abstractmethod
    def get_video_info(self, video_path: str) -> VideoInfo:
        """获取视频信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            VideoInfo: 视频信息
            
        Raises:
            VideoLoadError: 视频加载失败
        """
        pass
    
    @abstractmethod
    def extract_key_frame(self, video_path: str, timestamp: float,
                         size: Optional[tuple] = None) -> np.ndarray:
        """提取关键帧
        
        Args:
            video_path: 视频文件路径
            timestamp: 时间戳（秒）
            size: 输出尺寸 (width, height)
            
        Returns:
            np.ndarray: 关键帧图像
            
        Raises:
            ProcessingError: 提取失败
        """
        pass
    
    @abstractmethod
    def get_available_parsers(self) -> List[ParserType]:
        """获取可用的解析器
        
        Returns:
            List[ParserType]: 可用的解析器类型列表
        """
        pass
    
    @abstractmethod
    def get_parser_default_config(self, parser_type: ParserType) -> Dict[str, Any]:
        """获取解析器默认配置
        
        Args:
            parser_type: 解析器类型
            
        Returns:
            Dict[str, Any]: 默认配置
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: ProcessConfig) -> bool:
        """验证配置
        
        Args:
            config: 处理配置
            
        Returns:
            bool: 配置是否有效
        """
        pass
    
    @abstractmethod
    def estimate_processing_time(self, video_path: str, config: ProcessConfig) -> float:
        """估算处理时间（秒）
        
        Args:
            video_path: 视频文件路径
            config: 处理配置
            
        Returns:
            float: 预计处理时间（秒）
        """
        pass
    
    @abstractmethod
    def cancel_processing(self) -> bool:
        """取消当前处理
        
        Returns:
            bool: 是否成功取消
        """
        pass
    
    @abstractmethod
    def is_processing(self) -> bool:
        """检查是否正在处理
        
        Returns:
            bool: 是否正在处理
        """
        pass
    
    @abstractmethod
    def get_processing_progress(self) -> tuple:
        """获取处理进度
        
        Returns:
            tuple: (progress: float, message: str)
        """
        pass


class VideoEngineInterface(ABC):
    """视频引擎接口"""
    
    @abstractmethod
    def load_video(self, video_path: str) -> VideoInfo:
        """加载视频文件"""
        pass
    
    @abstractmethod
    def extract_frame(self, video_path: str, timestamp: float, 
                     size: Optional[tuple] = None) -> np.ndarray:
        """提取指定时间戳的帧"""
        pass
    
    @abstractmethod
    def extract_frames_batch(self, video_path: str, timestamps: List[float],
                           size: Optional[tuple] = None) -> List[np.ndarray]:
        """批量提取帧"""
        pass
    
    @abstractmethod
    def analyze_scene_changes(self, video_path: str, threshold: float = 0.3) -> List[float]:
        """分析场景变化"""
        pass


class AudioEngineInterface(ABC):
    """音频引擎接口"""
    
    @abstractmethod
    def extract_audio(self, video_path: str, output_path: Optional[str] = None) -> str:
        """从视频中提取音频"""
        pass
    
    @abstractmethod
    def recognize_speech(self, audio_path: str, language: str = 'zh',
                        engine: str = 'whisper') -> List[TranscriptSegment]:
        """语音识别"""
        pass
    
    @abstractmethod
    def detect_silence(self, audio_path: str, threshold: float = -40.0,
                      min_duration: float = 1.0) -> List[tuple]:
        """检测静音段"""
        pass
    
    @abstractmethod
    def get_audio_info(self, audio_path: str) -> Dict[str, Any]:
        """获取音频信息"""
        pass


class ParserEngineInterface(ABC):
    """解析引擎接口"""
    
    @abstractmethod
    def parse(self, video_info: VideoInfo, transcript: List[TranscriptSegment],
             config: Dict[str, Any]) -> List[SegmentInfo]:
        """解析生成段落"""
        pass
    
    @abstractmethod
    def get_parser_type(self) -> ParserType:
        """获取解析器类型"""
        pass
    
    @abstractmethod
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        pass


# 导出的接口类型别名
VideoProcessor = VideoProcessorInterface
VideoEngine = VideoEngineInterface
AudioEngine = AudioEngineInterface
ParserEngine = ParserEngineInterface
