"""基础异常定义

定义VideoReader应用的基础异常类。
"""


class VideoReaderError(Exception):
    """VideoReader应用基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }


class ConfigurationError(VideoReaderError):
    """配置错误"""
    pass


class ValidationError(VideoReaderError):
    """验证错误"""
    pass


class ProcessingError(VideoReaderError):
    """处理过程错误"""
    pass


class FileOperationError(VideoReaderError):
    """文件操作错误"""
    pass


class NetworkError(VideoReaderError):
    """网络错误"""
    pass
