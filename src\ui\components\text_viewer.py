"""文本查看器组件

显示段落的详细文本内容，支持格式化显示和编辑功能。
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLabel, QPushButton,
    QFrame, QGroupBox, QSplitter, QTabWidget, QPlainTextEdit, QScrollArea,
    QCheckBox, QSpinBox, QComboBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QTextCharFormat, QColor, QTextCursor, QPalette
from typing import Optional, Dict, Any

try:
    from ...core.models import SegmentInfo
    from ...common.logging import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from src.core.models import SegmentInfo
    from src.common.logging import get_logger


logger = get_logger(__name__)


class TextViewerWidget(QWidget):
    """文本查看器组件"""
    
    # 信号定义
    text_edited = Signal(str)  # 文本被编辑
    summary_edited = Signal(str)  # 摘要被编辑
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 当前段落
        self.current_segment: Optional[SegmentInfo] = None
        
        # 编辑状态
        self.is_editing = False
        self.auto_save_enabled = True
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()
        
        self.logger.info("文本查看器组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # 编辑模式切换
        self.edit_mode_checkbox = QCheckBox("编辑模式")
        self.edit_mode_checkbox.toggled.connect(self._on_edit_mode_toggled)
        toolbar_layout.addWidget(self.edit_mode_checkbox)
        
        # 字体大小控制
        toolbar_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(8, 24)
        self.font_size_spinbox.setValue(12)
        self.font_size_spinbox.valueChanged.connect(self._on_font_size_changed)
        toolbar_layout.addWidget(self.font_size_spinbox)
        
        # 自动保存
        self.auto_save_checkbox = QCheckBox("自动保存")
        self.auto_save_checkbox.setChecked(True)
        self.auto_save_checkbox.toggled.connect(self._on_auto_save_toggled)
        toolbar_layout.addWidget(self.auto_save_checkbox)
        
        toolbar_layout.addStretch()
        
        # 保存按钮
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self._on_save_clicked)
        self.save_button.setEnabled(False)
        toolbar_layout.addWidget(self.save_button)
        
        layout.addWidget(toolbar_frame)
        
        # 主要内容区域
        content_widget = QTabWidget()
        
        # 文本内容标签页
        text_tab = QWidget()
        text_layout = QVBoxLayout(text_tab)
        
        # 段落信息
        self.info_label = QLabel("未选择段落")
        self.info_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; }")
        text_layout.addWidget(self.info_label)
        
        # 文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(True)
        self.text_edit.textChanged.connect(self._on_text_changed)
        text_layout.addWidget(self.text_edit)
        
        content_widget.addTab(text_tab, "文本内容")
        
        # 摘要标签页
        summary_tab = QWidget()
        summary_layout = QVBoxLayout(summary_tab)
        
        summary_layout.addWidget(QLabel("段落摘要:"))
        
        self.summary_edit = QTextEdit()
        self.summary_edit.setReadOnly(True)
        self.summary_edit.setMaximumHeight(150)
        self.summary_edit.textChanged.connect(self._on_summary_changed)
        summary_layout.addWidget(self.summary_edit)
        
        # 摘要生成按钮
        summary_button_layout = QHBoxLayout()
        self.generate_summary_button = QPushButton("生成摘要")
        self.generate_summary_button.clicked.connect(self._on_generate_summary_clicked)
        summary_button_layout.addWidget(self.generate_summary_button)
        summary_button_layout.addStretch()
        summary_layout.addLayout(summary_button_layout)
        
        summary_layout.addStretch()
        content_widget.addTab(summary_tab, "摘要")
        
        # 元数据标签页
        metadata_tab = QWidget()
        metadata_layout = QVBoxLayout(metadata_tab)
        
        self.metadata_text = QPlainTextEdit()
        self.metadata_text.setReadOnly(True)
        metadata_layout.addWidget(self.metadata_text)
        
        content_widget.addTab(metadata_tab, "元数据")
        
        layout.addWidget(content_widget)
        
        # 状态栏
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.word_count_label = QLabel("字数: 0")
        status_layout.addWidget(self.word_count_label)
        
        layout.addWidget(status_frame)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 自动保存定时器
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self._auto_save)
        self.auto_save_timer.setSingleShot(True)
    
    def load_segment(self, segment: SegmentInfo):
        """加载段落内容
        
        Args:
            segment: 段落信息
        """
        try:
            self.logger.debug(f"加载段落: {segment.id}")
            
            self.current_segment = segment
            
            # 更新段落信息
            info_text = f"段落 {segment.id + 1} | "
            info_text += f"时间: {self._format_time(segment.start_time)} - {self._format_time(segment.end_time)} | "
            info_text += f"时长: {self._format_time(segment.duration)} | "
            info_text += f"置信度: {segment.confidence:.2f}"
            self.info_label.setText(info_text)
            
            # 更新文本内容
            self.text_edit.setPlainText(segment.text)
            
            # 更新摘要
            self.summary_edit.setPlainText(segment.summary or "")
            
            # 更新元数据
            metadata_text = self._format_metadata(segment.metadata)
            self.metadata_text.setPlainText(metadata_text)
            
            # 更新字数统计
            self._update_word_count()
            
            # 重置编辑状态
            self.is_editing = False
            self.save_button.setEnabled(False)
            self.status_label.setText("段落已加载")
            
        except Exception as e:
            self.logger.error(f"加载段落失败: {e}")
            self.status_label.setText(f"加载失败: {e}")
    
    def clear_content(self):
        """清空内容"""
        self.current_segment = None
        self.text_edit.clear()
        self.summary_edit.clear()
        self.metadata_text.clear()
        self.info_label.setText("未选择段落")
        self.word_count_label.setText("字数: 0")
        self.status_label.setText("就绪")
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def _format_metadata(self, metadata: Dict[str, Any]) -> str:
        """格式化元数据显示"""
        if not metadata:
            return "无元数据"
        
        lines = []
        for key, value in metadata.items():
            lines.append(f"{key}: {value}")
        
        return "\n".join(lines)
    
    def _update_word_count(self):
        """更新字数统计"""
        text = self.text_edit.toPlainText()
        word_count = len(text)
        char_count = len(text.replace(" ", "").replace("\n", ""))
        
        self.word_count_label.setText(f"字数: {word_count} | 字符: {char_count}")
    
    def _on_edit_mode_toggled(self, checked: bool):
        """编辑模式切换处理"""
        self.text_edit.setReadOnly(not checked)
        self.summary_edit.setReadOnly(not checked)
        
        if checked:
            self.status_label.setText("编辑模式")
        else:
            self.status_label.setText("只读模式")
            # 如果有未保存的更改，提示保存
            if self.is_editing:
                self._auto_save()
    
    def _on_font_size_changed(self, size: int):
        """字体大小改变处理"""
        font = QFont()
        font.setPointSize(size)
        
        self.text_edit.setFont(font)
        self.summary_edit.setFont(font)
    
    def _on_auto_save_toggled(self, checked: bool):
        """自动保存切换处理"""
        self.auto_save_enabled = checked
        
        if checked:
            self.status_label.setText("自动保存已启用")
        else:
            self.status_label.setText("自动保存已禁用")
    
    def _on_text_changed(self):
        """文本内容改变处理"""
        if not self.edit_mode_checkbox.isChecked():
            return
        
        self.is_editing = True
        self.save_button.setEnabled(True)
        self._update_word_count()
        
        # 启动自动保存定时器
        if self.auto_save_enabled:
            self.auto_save_timer.start(2000)  # 2秒后自动保存
    
    def _on_summary_changed(self):
        """摘要内容改变处理"""
        if not self.edit_mode_checkbox.isChecked():
            return
        
        self.is_editing = True
        self.save_button.setEnabled(True)
        
        # 启动自动保存定时器
        if self.auto_save_enabled:
            self.auto_save_timer.start(2000)
    
    def _on_save_clicked(self):
        """保存按钮点击处理"""
        self._save_changes()
    
    def _on_generate_summary_clicked(self):
        """生成摘要按钮点击处理"""
        if not self.current_segment:
            return
        
        try:
            # 简单的摘要生成：取前100个字符
            text = self.text_edit.toPlainText()
            if len(text) > 100:
                summary = text[:100] + "..."
            else:
                summary = text
            
            self.summary_edit.setPlainText(summary)
            self.status_label.setText("摘要已生成")
            
        except Exception as e:
            self.logger.error(f"生成摘要失败: {e}")
            self.status_label.setText(f"生成摘要失败: {e}")
    
    def _auto_save(self):
        """自动保存"""
        if self.is_editing and self.auto_save_enabled:
            self._save_changes()
    
    def _save_changes(self):
        """保存更改"""
        if not self.current_segment or not self.is_editing:
            return
        
        try:
            # 获取编辑后的内容
            new_text = self.text_edit.toPlainText()
            new_summary = self.summary_edit.toPlainText()
            
            # 发送信号通知更改
            if new_text != self.current_segment.text:
                self.text_edited.emit(new_text)
            
            if new_summary != (self.current_segment.summary or ""):
                self.summary_edited.emit(new_summary)
            
            # 更新状态
            self.is_editing = False
            self.save_button.setEnabled(False)
            self.status_label.setText("更改已保存")
            
            self.logger.debug(f"保存段落更改: {self.current_segment.id}")
            
        except Exception as e:
            self.logger.error(f"保存更改失败: {e}")
            self.status_label.setText(f"保存失败: {e}")
    
    def get_current_text(self) -> str:
        """获取当前文本内容"""
        return self.text_edit.toPlainText()
    
    def get_current_summary(self) -> str:
        """获取当前摘要内容"""
        return self.summary_edit.toPlainText()
    
    def has_unsaved_changes(self) -> bool:
        """检查是否有未保存的更改"""
        return self.is_editing
    
    def set_read_only(self, read_only: bool):
        """设置只读模式"""
        self.edit_mode_checkbox.setChecked(not read_only)
    
    def highlight_text(self, search_text: str):
        """高亮显示搜索文本"""
        if not search_text:
            return
        
        # 清除之前的高亮
        cursor = self.text_edit.textCursor()
        cursor.select(QTextCursor.Document)
        format = QTextCharFormat()
        cursor.setCharFormat(format)
        
        # 高亮搜索文本
        cursor = self.text_edit.textCursor()
        cursor.movePosition(QTextCursor.Start)
        
        format = QTextCharFormat()
        format.setBackground(QColor("yellow"))
        
        while True:
            cursor = self.text_edit.document().find(search_text, cursor)
            if cursor.isNull():
                break
            cursor.setCharFormat(format)
