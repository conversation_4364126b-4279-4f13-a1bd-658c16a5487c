#!/usr/bin/env python3
"""Cloudreve实际功能测试

测试Cloudreve的上传、下载、获取公开URL等实际功能。
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config
from src.modules.video_processor.audio_engine.file_uploader import CloudreveUploader, FileUploadManager
from src.common.logging import get_logger

logger = get_logger(__name__)


def check_config():
    """检查Cloudreve配置"""
    print("=== 检查Cloudreve配置 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    token = get_config('file_uploader.uploaders.cloudreve.token', '')
    timeout = get_config('file_uploader.uploaders.cloudreve.timeout', 60)
    chunk_size = get_config('file_uploader.uploaders.cloudreve.chunk_size', 5242880)
    
    print(f"Base URL: {base_url}")
    print(f"Username: {username}")
    print(f"Password: {'已配置' if password else '未配置'}")
    print(f"Token: {'已配置' if token else '未配置'}")
    print(f"Timeout: {timeout}秒")
    print(f"Chunk Size: {chunk_size} bytes ({chunk_size/1024/1024:.1f}MB)")
    
    if not base_url:
        print("❌ Base URL未配置")
        return False
    
    if not (username and password) and not token:
        print("❌ 认证信息未配置（需要用户名密码或访问令牌）")
        return False
    
    print("✅ 配置检查通过")
    return True


def test_connection():
    """测试连接"""
    print("\n=== 测试Cloudreve连接 ===")
    
    try:
        uploader = CloudreveUploader()
        
        if uploader.is_available():
            print("✅ Cloudreve连接成功")
            return uploader
        else:
            print("❌ Cloudreve连接失败")
            return None
            
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return None


def create_test_file():
    """创建测试文件"""
    print("\n=== 创建测试文件 ===")
    
    try:
        # 创建一个小的测试文件
        test_content = f"""
这是一个测试文件
创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
用于测试Cloudreve上传功能

测试内容包括：
1. 文件上传
2. 获取公开URL
3. 文件下载验证

文件大小约为几百字节，便于快速测试。
""".encode('utf-8')
        
        test_file = Path("test_cloudreve_upload.txt")
        with open(test_file, 'wb') as f:
            f.write(test_content)
        
        print(f"✅ 测试文件已创建: {test_file}")
        print(f"   文件大小: {len(test_content)} bytes")
        
        return test_file
        
    except Exception as e:
        print(f"❌ 创建测试文件失败: {e}")
        return None


def test_upload(uploader, test_file):
    """测试文件上传"""
    print("\n=== 测试文件上传 ===")
    
    try:
        # 生成唯一的远程文件名
        timestamp = int(time.time())
        remote_filename = f"test_upload_{timestamp}.txt"
        
        print(f"开始上传文件: {test_file} -> {remote_filename}")
        
        # 执行上传
        file_url = uploader.upload(str(test_file), remote_filename)
        
        print(f"✅ 文件上传成功!")
        print(f"   公开URL: {file_url}")
        
        return file_url
        
    except Exception as e:
        print(f"❌ 文件上传失败: {e}")
        return None


def test_download(file_url):
    """测试文件下载"""
    print("\n=== 测试文件下载 ===")
    
    try:
        import requests
        
        print(f"尝试下载文件: {file_url}")
        
        # 下载文件
        response = requests.get(file_url, timeout=30)
        
        if response.status_code == 200:
            content = response.text
            print("✅ 文件下载成功!")
            print(f"   文件大小: {len(content)} 字符")
            print(f"   内容预览: {content[:100]}...")
            
            # 验证内容
            if "这是一个测试文件" in content:
                print("✅ 文件内容验证通过")
                return True
            else:
                print("❌ 文件内容验证失败")
                return False
        else:
            print(f"❌ 下载失败，HTTP状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 下载测试异常: {e}")
        return False


def test_large_file_upload(uploader):
    """测试大文件上传（分块上传）"""
    print("\n=== 测试大文件上传（分块上传） ===")
    
    try:
        # 创建一个较大的测试文件（约10MB）
        large_content = b"0123456789" * 1024 * 1024  # 10MB
        
        large_file = Path("test_large_file.bin")
        with open(large_file, 'wb') as f:
            f.write(large_content)
        
        print(f"✅ 大文件已创建: {large_file}")
        print(f"   文件大小: {len(large_content) / 1024 / 1024:.1f}MB")
        
        # 上传大文件
        timestamp = int(time.time())
        remote_filename = f"test_large_{timestamp}.bin"
        
        print(f"开始上传大文件: {large_file} -> {remote_filename}")
        
        file_url = uploader.upload(str(large_file), remote_filename)
        
        print(f"✅ 大文件上传成功!")
        print(f"   公开URL: {file_url}")
        
        # 清理本地大文件
        large_file.unlink()
        print("✅ 本地大文件已清理")
        
        return file_url
        
    except Exception as e:
        print(f"❌ 大文件上传失败: {e}")
        # 清理文件
        if 'large_file' in locals() and large_file.exists():
            large_file.unlink()
        return None


def test_file_upload_manager():
    """测试文件上传管理器"""
    print("\n=== 测试文件上传管理器 ===")
    
    try:
        manager = FileUploadManager()
        
        # 检查可用的上传器
        available_uploaders = manager.get_available_uploaders()
        print(f"可用的上传器: {available_uploaders}")
        
        if 'cloudreve' not in available_uploaders:
            print("❌ Cloudreve上传器在管理器中不可用")
            return False
        
        # 创建小测试文件
        test_content = f"FileUploadManager测试 - {time.strftime('%Y-%m-%d %H:%M:%S')}".encode('utf-8')
        test_file = Path("test_manager_upload.txt")
        with open(test_file, 'wb') as f:
            f.write(test_content)
        
        # 使用管理器上传（自动选择）
        print("使用FileUploadManager自动选择上传器...")
        file_url = manager.upload(str(test_file), uploader='auto')
        
        print(f"✅ 管理器上传成功!")
        print(f"   公开URL: {file_url}")
        
        # 清理测试文件
        test_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ 文件上传管理器测试失败: {e}")
        return False


def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    test_files = [
        "test_cloudreve_upload.txt",
        "test_large_file.bin",
        "test_manager_upload.txt"
    ]
    
    for file_name in test_files:
        file_path = Path(file_name)
        if file_path.exists():
            file_path.unlink()
            print(f"✅ 已删除: {file_name}")


def main():
    """主函数"""
    print("Cloudreve实际功能测试")
    print("=" * 60)
    
    # 检查配置
    if not check_config():
        print("\n❌ 配置检查失败，请检查config.toml文件")
        return
    
    # 测试连接
    uploader = test_connection()
    if not uploader:
        print("\n❌ 连接测试失败，请检查Cloudreve服务器状态和配置")
        return
    
    # 创建测试文件
    test_file = create_test_file()
    if not test_file:
        print("\n❌ 创建测试文件失败")
        return
    
    try:
        # 测试小文件上传
        file_url = test_upload(uploader, test_file)
        if file_url:
            # 测试下载
            test_download(file_url)
        
        # 测试大文件上传
        test_large_file_upload(uploader)
        
        # 测试文件上传管理器
        test_file_upload_manager()
        
    finally:
        # 清理测试文件
        cleanup_test_files()
    
    print("\n" + "=" * 60)
    print("🎉 Cloudreve功能测试完成!")
    print("\n测试项目:")
    print("✅ 配置检查")
    print("✅ 连接测试")
    print("✅ 小文件上传")
    print("✅ 文件下载验证")
    print("✅ 大文件分块上传")
    print("✅ 文件上传管理器")
    print("\n如果所有测试都通过，说明Cloudreve集成工作正常！")


if __name__ == "__main__":
    main()
