#!/usr/bin/env python3
"""VideoReader 项目状态检查脚本

检查项目的完整性、代码质量和开发进度。
"""

import sys
import os
from pathlib import Path
import subprocess
import json

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def count_lines_of_code():
    """统计代码行数"""
    total_lines = 0
    python_files = 0
    
    for file_path in src_path.rglob("*.py"):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
                total_lines += lines
                python_files += 1
        except Exception:
            pass
    
    return total_lines, python_files

def count_test_files():
    """统计测试文件"""
    test_files = 0
    test_lines = 0
    
    test_dir = project_root / "tests"
    if test_dir.exists():
        for file_path in test_dir.rglob("test_*.py"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    test_lines += lines
                    test_files += 1
            except Exception:
                pass
    
    return test_files, test_lines

def count_documentation():
    """统计文档文件"""
    doc_files = 0
    doc_lines = 0
    
    # 统计doc目录
    doc_dir = project_root / "doc"
    if doc_dir.exists():
        for file_path in doc_dir.rglob("*.md"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    doc_lines += lines
                    doc_files += 1
            except Exception:
                pass
    
    # 统计README
    readme_file = project_root / "README.md"
    if readme_file.exists():
        try:
            with open(readme_file, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
                doc_lines += lines
                doc_files += 1
        except Exception:
            pass
    
    return doc_files, doc_lines

def check_module_completeness():
    """检查模块完整性"""
    modules = {
        "core": ["events.py", "models.py"],
        "common": ["config.py", "logging.py", "utils.py", "exceptions.py"],
        "modules/video_processor": ["interface.py", "models.py", "coordinator.py"],
        "modules/storage_engine": ["interface.py", "models.py", "metadata.py"],
        "modules/search_engine": ["interface.py", "models.py", "searcher.py"],
        "ui": ["interface.py", "main_window.py"]
    }
    
    completed_modules = 0
    total_modules = len(modules)
    module_status = {}
    
    for module_name, required_files in modules.items():
        module_path = src_path / module_name
        completed_files = 0
        
        for file_name in required_files:
            file_path = module_path / file_name
            if file_path.exists():
                completed_files += 1
        
        completion_rate = completed_files / len(required_files)
        module_status[module_name] = {
            "completed_files": completed_files,
            "total_files": len(required_files),
            "completion_rate": completion_rate
        }
        
        if completion_rate >= 0.8:  # 80%以上认为完成
            completed_modules += 1
    
    return completed_modules, total_modules, module_status

def check_configuration_files():
    """检查配置文件"""
    config_files = [
        "requirements.txt",
        "requirements-dev.txt", 
        "setup.py",
        "pyproject.toml",
        "pytest.ini",
        ".gitignore",
        "MANIFEST.in"
    ]
    
    existing_files = 0
    for file_name in config_files:
        if (project_root / file_name).exists():
            existing_files += 1
    
    return existing_files, len(config_files)

def generate_project_report():
    """生成项目报告"""
    print("📊 VideoReader 项目状态报告")
    print("=" * 60)
    
    # 代码统计
    total_lines, python_files = count_lines_of_code()
    test_files, test_lines = count_test_files()
    doc_files, doc_lines = count_documentation()
    
    print(f"\n📝 代码统计:")
    print(f"   Python文件数量: {python_files}")
    print(f"   代码总行数: {total_lines:,}")
    print(f"   平均每文件行数: {total_lines // python_files if python_files > 0 else 0}")
    
    print(f"\n🧪 测试统计:")
    print(f"   测试文件数量: {test_files}")
    print(f"   测试代码行数: {test_lines:,}")
    if total_lines > 0:
        test_coverage_estimate = (test_lines / total_lines) * 100
        print(f"   测试覆盖率估算: {test_coverage_estimate:.1f}%")
    
    print(f"\n📚 文档统计:")
    print(f"   文档文件数量: {doc_files}")
    print(f"   文档总行数: {doc_lines:,}")
    
    # 模块完整性
    completed_modules, total_modules, module_status = check_module_completeness()
    completion_percentage = (completed_modules / total_modules) * 100
    
    print(f"\n🏗️  模块完整性:")
    print(f"   已完成模块: {completed_modules}/{total_modules} ({completion_percentage:.1f}%)")
    
    for module_name, status in module_status.items():
        rate = status["completion_rate"] * 100
        status_icon = "✅" if rate >= 80 else "🔄" if rate >= 50 else "❌"
        print(f"   {status_icon} {module_name}: {status['completed_files']}/{status['total_files']} ({rate:.0f}%)")
    
    # 配置文件
    config_existing, config_total = check_configuration_files()
    config_percentage = (config_existing / config_total) * 100
    
    print(f"\n⚙️  配置文件:")
    print(f"   配置文件完整性: {config_existing}/{config_total} ({config_percentage:.1f}%)")
    
    # 项目结构
    print(f"\n📁 项目结构:")
    key_directories = [
        "src/core",
        "src/common", 
        "src/modules",
        "src/ui",
        "tests",
        "doc",
        "scripts"
    ]
    
    for dir_name in key_directories:
        dir_path = project_root / dir_name
        status_icon = "✅" if dir_path.exists() else "❌"
        print(f"   {status_icon} {dir_name}")
    
    # 总体评估
    print(f"\n🎯 总体评估:")
    
    # 计算总体完成度
    code_score = min(100, (total_lines / 5000) * 100)  # 假设目标5000行代码
    module_score = completion_percentage
    config_score = config_percentage
    doc_score = min(100, (doc_lines / 1000) * 100)  # 假设目标1000行文档
    
    overall_score = (code_score + module_score + config_score + doc_score) / 4
    
    print(f"   代码完成度: {code_score:.1f}%")
    print(f"   模块完成度: {module_score:.1f}%")
    print(f"   配置完成度: {config_score:.1f}%")
    print(f"   文档完成度: {doc_score:.1f}%")
    print(f"   总体完成度: {overall_score:.1f}%")
    
    # 状态评级
    if overall_score >= 90:
        status = "🌟 优秀"
        color = "绿色"
    elif overall_score >= 75:
        status = "✅ 良好"
        color = "蓝色"
    elif overall_score >= 60:
        status = "🔄 进行中"
        color = "黄色"
    else:
        status = "❌ 需要改进"
        color = "红色"
    
    print(f"\n🏆 项目状态: {status} ({overall_score:.1f}%)")
    
    # 下一步建议
    print(f"\n📋 下一步建议:")
    
    if code_score < 80:
        print("   - 继续完善核心功能实现")
    
    if module_score < 90:
        print("   - 完成未完成的模块文件")
    
    if doc_score < 80:
        print("   - 补充项目文档")
    
    if test_lines < total_lines * 0.3:
        print("   - 增加测试用例覆盖率")
    
    print("   - 运行测试验证功能")
    print("   - 进行代码质量检查")
    print("   - 准备第一个可用版本")
    
    return overall_score

def main():
    """主函数"""
    try:
        score = generate_project_report()
        
        print(f"\n" + "=" * 60)
        print(f"📈 项目开发进度: {score:.1f}%")
        
        if score >= 75:
            print("🎉 项目基础架构已基本完成，可以开始功能开发！")
            return 0
        else:
            print("🔧 项目仍在开发中，继续完善基础架构...")
            return 1
            
    except Exception as e:
        print(f"❌ 生成报告时发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
