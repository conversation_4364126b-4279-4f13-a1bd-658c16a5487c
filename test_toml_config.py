#!/usr/bin/env python3
"""TOML配置文件测试脚本

测试TOML配置文件的读取和使用功能。
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config_manager, get_config
from src.modules.video_processor.audio_engine.file_uploader import CloudreveUploader, FileUploadManager
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer
from src.common.logging import get_logger

logger = get_logger(__name__)


def test_config_manager():
    """测试配置管理器"""
    print("=== 测试配置管理器 ===")
    
    try:
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write("""
[audio.speech_engines.paraformer]
api_key = "test-paraformer-key"
model = "paraformer-v2"

[file_uploader.uploaders.cloudreve]
base_url = "http://test-server.com:5212"
username = "test-user"
password = "test-pass"
timeout = 120
""")
            temp_config_file = f.name
        
        # 使用临时配置文件创建配置管理器
        config_manager = get_config_manager()
        config_manager.config_file = Path(temp_config_file)
        config_manager.reload()
        
        # 测试读取配置
        paraformer_key = config_manager.get('audio.speech_engines.paraformer.api_key')
        cloudreve_url = config_manager.get('file_uploader.uploaders.cloudreve.base_url')
        cloudreve_timeout = config_manager.get('file_uploader.uploaders.cloudreve.timeout')
        
        print(f"✓ Paraformer API密钥: {paraformer_key}")
        print(f"✓ Cloudreve URL: {cloudreve_url}")
        print(f"✓ Cloudreve超时: {cloudreve_timeout}")
        
        # 清理临时文件
        os.unlink(temp_config_file)
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False


def test_cloudreve_config():
    """测试Cloudreve配置读取"""
    print("\n=== 测试Cloudreve配置读取 ===")
    
    try:
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write("""
[file_uploader.uploaders.cloudreve]
base_url = "http://config-test-server.com:5212"
username = "config-test-user"
password = "config-test-pass"
token = "config-test-token"
timeout = 90
chunk_size = 1048576
max_retries = 5
""")
            temp_config_file = f.name
        
        # 使用临时配置文件
        config_manager = get_config_manager()
        config_manager.config_file = Path(temp_config_file)
        config_manager.reload()
        
        # 创建Cloudreve上传器（应该从配置文件读取）
        uploader = CloudreveUploader()
        
        print(f"✓ Base URL: {uploader.base_url}")
        print(f"✓ Username: {uploader.username}")
        print(f"✓ Password: {'*' * len(uploader.password) if uploader.password else 'None'}")
        print(f"✓ Token: {'*' * len(uploader.token) if uploader.token else 'None'}")
        print(f"✓ Timeout: {uploader.timeout}")
        print(f"✓ Chunk Size: {uploader.chunk_size}")
        print(f"✓ Max Retries: {uploader.max_retries}")
        
        # 验证配置是否正确读取
        assert uploader.base_url == "http://config-test-server.com:5212"
        assert uploader.username == "config-test-user"
        assert uploader.password == "config-test-pass"
        assert uploader.token == "config-test-token"
        assert uploader.timeout == 90
        assert uploader.chunk_size == 1048576
        assert uploader.max_retries == 5
        
        print("✓ 所有配置项验证通过")
        
        # 清理临时文件
        os.unlink(temp_config_file)
        
        return True
        
    except Exception as e:
        print(f"✗ Cloudreve配置测试失败: {e}")
        return False


def test_paraformer_config():
    """测试Paraformer配置读取"""
    print("\n=== 测试Paraformer配置读取 ===")
    
    try:
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write("""
[audio.speech_engines.paraformer]
api_key = "config-test-paraformer-key"
model = "paraformer-8k-v2"
endpoint = "https://test-dashscope.aliyuncs.com"
""")
            temp_config_file = f.name
        
        # 使用临时配置文件
        config_manager = get_config_manager()
        config_manager.config_file = Path(temp_config_file)
        config_manager.reload()
        
        # 创建Paraformer识别器（应该从配置文件读取）
        recognizer = ParaformerRecognizer()
        
        print(f"✓ API Key: {'*' * len(recognizer.api_key) if recognizer.api_key else 'None'}")
        print(f"✓ Model: {recognizer.model}")
        print(f"✓ Endpoint: {recognizer.endpoint}")
        
        # 验证配置是否正确读取
        assert recognizer.api_key == "config-test-paraformer-key"
        assert recognizer.model == "paraformer-8k-v2"
        assert recognizer.endpoint == "https://test-dashscope.aliyuncs.com"
        
        print("✓ 所有配置项验证通过")
        
        # 清理临时文件
        os.unlink(temp_config_file)
        
        return True
        
    except Exception as e:
        print(f"✗ Paraformer配置测试失败: {e}")
        return False


def test_config_priority():
    """测试配置优先级（参数 > 配置文件 > 环境变量）"""
    print("\n=== 测试配置优先级 ===")
    
    try:
        # 设置环境变量
        os.environ['CLOUDREVE_BASE_URL'] = 'http://env-server.com'
        os.environ['CLOUDREVE_USERNAME'] = 'env-user'
        
        # 创建配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write("""
[file_uploader.uploaders.cloudreve]
base_url = "http://config-server.com:5212"
username = "config-user"
password = "config-pass"
""")
            temp_config_file = f.name
        
        # 使用临时配置文件
        config_manager = get_config_manager()
        config_manager.config_file = Path(temp_config_file)
        config_manager.reload()
        
        # 测试1: 只有环境变量和配置文件（配置文件应该优先）
        uploader1 = CloudreveUploader()
        print(f"配置文件 vs 环境变量:")
        print(f"  Base URL: {uploader1.base_url} (应该是config-server)")
        print(f"  Username: {uploader1.username} (应该是config-user)")
        
        # 测试2: 传入参数（参数应该优先）
        uploader2 = CloudreveUploader(
            base_url="http://param-server.com",
            username="param-user"
        )
        print(f"参数 vs 配置文件 vs 环境变量:")
        print(f"  Base URL: {uploader2.base_url} (应该是param-server)")
        print(f"  Username: {uploader2.username} (应该是param-user)")
        print(f"  Password: {uploader2.password} (应该是config-pass)")
        
        # 验证优先级
        assert uploader1.base_url == "http://config-server.com:5212"
        assert uploader1.username == "config-user"
        assert uploader2.base_url == "http://param-server.com"
        assert uploader2.username == "param-user"
        assert uploader2.password == "config-pass"
        
        print("✓ 配置优先级验证通过")
        
        # 清理
        os.unlink(temp_config_file)
        del os.environ['CLOUDREVE_BASE_URL']
        del os.environ['CLOUDREVE_USERNAME']
        
        return True
        
    except Exception as e:
        print(f"✗ 配置优先级测试失败: {e}")
        return False


def test_default_config_creation():
    """测试默认配置文件创建"""
    print("\n=== 测试默认配置文件创建 ===")
    
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "test_config.toml"
            
            # 创建配置管理器
            config_manager = get_config_manager()
            config_manager.config_file = config_file
            
            # 保存默认配置
            config_manager.save()
            
            print(f"✓ 配置文件已创建: {config_file}")
            print(f"✓ 文件存在: {config_file.exists()}")
            
            # 读取并验证配置文件内容
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"✓ 配置文件大小: {len(content)} 字符")
                
                # 检查是否包含关键配置段
                assert '[app]' in content
                assert '[file_uploader]' in content
                assert '[audio]' in content
                print("✓ 配置文件包含必要的配置段")
        
        return True
        
    except Exception as e:
        print(f"✗ 默认配置文件创建测试失败: {e}")
        return False


def main():
    """主函数"""
    print("TOML配置文件测试")
    print("=" * 50)
    
    tests = [
        ("配置管理器", test_config_manager),
        ("Cloudreve配置读取", test_cloudreve_config),
        ("Paraformer配置读取", test_paraformer_config),
        ("配置优先级", test_config_priority),
        ("默认配置文件创建", test_default_config_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！TOML配置功能正常工作。")
    else:
        print("❌ 部分测试失败，请检查配置功能。")
    
    print("\n使用说明:")
    print("1. 运行 'python scripts/create_config.py' 创建配置文件")
    print("2. 编辑 ~/.videoreader/config.toml 配置您的服务")
    print("3. 运行 VideoReader，配置将自动生效")


if __name__ == "__main__":
    main()
