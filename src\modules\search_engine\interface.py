"""搜索引擎对外接口

定义搜索引擎模块的统一接口，负责构建搜索索引和执行搜索功能。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
from enum import Enum

from core.models import SegmentInfo, SearchQuery, SearchResult
from common.exceptions import SearchIndexError


class SearchType(Enum):
    """搜索类型枚举"""
    FULL_TEXT = "full_text"
    REGEX = "regex"
    TIME_RANGE = "time_range"
    KEYWORD = "keyword"
    FUZZY = "fuzzy"
    SEMANTIC = "semantic"


class SortOrder(Enum):
    """排序方式枚举"""
    RELEVANCE = "relevance"
    TIME_ASC = "time_asc"
    TIME_DESC = "time_desc"
    CONFIDENCE = "confidence"
    LENGTH = "length"


class SearchEngineInterface(ABC):
    """搜索引擎接口"""
    
    @abstractmethod
    def build_index(self, segments: List[SegmentInfo]) -> bool:
        """构建搜索索引
        
        Args:
            segments: 段落信息列表
            
        Returns:
            bool: 是否构建成功
            
        Raises:
            SearchIndexError: 索引构建失败
        """
        pass
    
    @abstractmethod
    def search(self, query: SearchQuery) -> List[SearchResult]:
        """执行搜索
        
        Args:
            query: 搜索查询
            
        Returns:
            List[SearchResult]: 搜索结果列表
            
        Raises:
            SearchIndexError: 搜索执行失败
        """
        pass
    
    @abstractmethod
    def get_suggestions(self, partial_query: str, max_suggestions: int = 10) -> List[str]:
        """获取搜索建议
        
        Args:
            partial_query: 部分查询字符串
            max_suggestions: 最大建议数量
            
        Returns:
            List[str]: 搜索建议列表
        """
        pass
    
    @abstractmethod
    def clear_index(self) -> bool:
        """清理索引
        
        Returns:
            bool: 是否清理成功
        """
        pass
    
    @abstractmethod
    def get_index_stats(self) -> Dict[str, Any]:
        """获取索引统计信息
        
        Returns:
            Dict[str, Any]: 索引统计信息
        """
        pass
    
    @abstractmethod
    def update_segment(self, segment: SegmentInfo) -> bool:
        """更新段落索引
        
        Args:
            segment: 段落信息
            
        Returns:
            bool: 是否更新成功
        """
        pass
    
    @abstractmethod
    def remove_segment(self, segment_id: int) -> bool:
        """从索引中移除段落
        
        Args:
            segment_id: 段落ID
            
        Returns:
            bool: 是否移除成功
        """
        pass
    
    @abstractmethod
    def save_index(self, file_path: str) -> bool:
        """保存索引到文件
        
        Args:
            file_path: 索引文件路径
            
        Returns:
            bool: 是否保存成功
        """
        pass
    
    @abstractmethod
    def load_index(self, file_path: str) -> bool:
        """从文件加载索引
        
        Args:
            file_path: 索引文件路径
            
        Returns:
            bool: 是否加载成功
        """
        pass


class IndexerInterface(ABC):
    """索引构建器接口"""
    
    @abstractmethod
    def create_index(self, segments: List[SegmentInfo]) -> Dict[str, Any]:
        """创建索引"""
        pass
    
    @abstractmethod
    def add_document(self, segment: SegmentInfo) -> bool:
        """添加文档到索引"""
        pass
    
    @abstractmethod
    def remove_document(self, segment_id: int) -> bool:
        """从索引中移除文档"""
        pass
    
    @abstractmethod
    def update_document(self, segment: SegmentInfo) -> bool:
        """更新索引中的文档"""
        pass
    
    @abstractmethod
    def optimize_index(self) -> bool:
        """优化索引"""
        pass


class SearcherInterface(ABC):
    """搜索器接口"""
    
    @abstractmethod
    def search_full_text(self, query: str, max_results: int = 100) -> List[SearchResult]:
        """全文搜索"""
        pass
    
    @abstractmethod
    def search_regex(self, pattern: str, max_results: int = 100) -> List[SearchResult]:
        """正则表达式搜索"""
        pass
    
    @abstractmethod
    def search_time_range(self, start_time: float, end_time: float) -> List[SearchResult]:
        """时间范围搜索"""
        pass
    
    @abstractmethod
    def search_keyword(self, keywords: List[str], max_results: int = 100) -> List[SearchResult]:
        """关键词搜索"""
        pass
    
    @abstractmethod
    def search_fuzzy(self, query: str, max_distance: int = 2, 
                    max_results: int = 100) -> List[SearchResult]:
        """模糊搜索"""
        pass
    
    @abstractmethod
    def search_semantic(self, query: str, max_results: int = 100) -> List[SearchResult]:
        """语义搜索"""
        pass
    
    @abstractmethod
    def highlight_matches(self, text: str, query: str) -> str:
        """高亮匹配文本"""
        pass
    
    @abstractmethod
    def calculate_relevance(self, segment: SegmentInfo, query: str) -> float:
        """计算相关性分数"""
        pass


class QueryParserInterface(ABC):
    """查询解析器接口"""
    
    @abstractmethod
    def parse_query(self, query_string: str) -> SearchQuery:
        """解析查询字符串"""
        pass
    
    @abstractmethod
    def validate_query(self, query: SearchQuery) -> bool:
        """验证查询"""
        pass
    
    @abstractmethod
    def expand_query(self, query: SearchQuery) -> SearchQuery:
        """扩展查询（同义词、词干等）"""
        pass
    
    @abstractmethod
    def get_query_terms(self, query: SearchQuery) -> List[str]:
        """获取查询词项"""
        pass


class FilterInterface(ABC):
    """搜索过滤器接口"""
    
    @abstractmethod
    def filter_by_time(self, results: List[SearchResult], 
                      start_time: float, end_time: float) -> List[SearchResult]:
        """按时间过滤"""
        pass
    
    @abstractmethod
    def filter_by_confidence(self, results: List[SearchResult], 
                           min_confidence: float) -> List[SearchResult]:
        """按置信度过滤"""
        pass
    
    @abstractmethod
    def filter_by_length(self, results: List[SearchResult], 
                        min_length: int, max_length: int) -> List[SearchResult]:
        """按文本长度过滤"""
        pass
    
    @abstractmethod
    def sort_results(self, results: List[SearchResult], 
                    sort_by: SortOrder) -> List[SearchResult]:
        """排序结果"""
        pass
    
    @abstractmethod
    def deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重结果"""
        pass


class SuggestionInterface(ABC):
    """搜索建议接口"""
    
    @abstractmethod
    def build_suggestion_index(self, segments: List[SegmentInfo]) -> bool:
        """构建建议索引"""
        pass
    
    @abstractmethod
    def get_term_suggestions(self, partial_term: str, max_suggestions: int = 10) -> List[str]:
        """获取词项建议"""
        pass
    
    @abstractmethod
    def get_phrase_suggestions(self, partial_phrase: str, max_suggestions: int = 10) -> List[str]:
        """获取短语建议"""
        pass
    
    @abstractmethod
    def get_popular_queries(self, max_queries: int = 10) -> List[str]:
        """获取热门查询"""
        pass
    
    @abstractmethod
    def record_query(self, query: str) -> bool:
        """记录查询历史"""
        pass


# 导出的接口类型别名
SearchEngine = SearchEngineInterface
Indexer = IndexerInterface
Searcher = SearcherInterface
QueryParser = QueryParserInterface
Filter = FilterInterface
Suggestion = SuggestionInterface
