"""视频预处理模块

负责将原始视频文件转换为结构化的可阅读数据，包括视频处理、音频处理和解析功能。
对外暴露统一的预处理接口，隐藏内部三个子引擎的实现细节。
"""

from .interface import VideoProcessorInterface as VideoProcessor
from .coordinator import VideoProcessorCoordinator
try:
    from ...core.models import VideoInfo, ProcessResult, ProcessConfig
except ImportError:
    from core.models import VideoInfo, ProcessResult, ProcessConfig

# 创建默认实例
def create_video_processor() -> VideoProcessor:
    """创建视频预处理器实例"""
    return VideoProcessorCoordinator()

__all__ = [
    'VideoProcessor',
    'VideoProcessorCoordinator',
    'VideoInfo',
    'ProcessResult',
    'ProcessConfig',
    'create_video_processor'
]
