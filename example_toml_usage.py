#!/usr/bin/env python3
"""TOML配置使用示例

演示如何使用TOML配置文件进行VideoReader的配置和使用。
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config_manager, get_config
from src.modules.video_processor.audio_engine.file_uploader import CloudreveUploader, FileUploadManager
from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer
from src.common.logging import get_logger

logger = get_logger(__name__)


def example_1_basic_usage():
    """示例1：基本使用 - 使用默认配置文件"""
    print("=== 示例1：基本使用 ===")
    
    # 直接使用，会自动从 ~/.videoreader/config.toml 读取配置
    try:
        # 创建Cloudreve上传器（从配置文件读取）
        uploader = CloudreveUploader()
        print(f"Cloudreve URL: {uploader.base_url or '未配置'}")
        print(f"认证方式: {'Token' if uploader.token else 'Username/Password' if uploader.username else '未配置'}")
        
        # 创建Paraformer识别器（从配置文件读取）
        recognizer = ParaformerRecognizer()
        print(f"Paraformer模型: {recognizer.model}")
        print(f"API密钥: {'已配置' if recognizer.api_key else '未配置'}")
        
        # 创建文件上传管理器
        manager = FileUploadManager()
        available_uploaders = manager.get_available_uploaders()
        print(f"可用上传器: {available_uploaders}")
        
    except Exception as e:
        print(f"示例1失败: {e}")


def example_2_custom_config():
    """示例2：使用自定义配置文件"""
    print("\n=== 示例2：使用自定义配置文件 ===")
    
    try:
        # 创建临时配置文件
        config_content = """
[audio.speech_engines.paraformer]
api_key = "demo-paraformer-key"
model = "paraformer-v2"
endpoint = "https://dashscope.aliyuncs.com"

[file_uploader.uploaders.cloudreve]
base_url = "http://demo-server.com:5212"
username = "demo-user"
password = "demo-pass"
timeout = 90
chunk_size = 1048576
max_retries = 5

[file_uploader]
default_uploader = "cloudreve"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(config_content)
            temp_config_file = f.name
        
        # 使用自定义配置文件创建配置管理器
        from src.common.config.manager import ConfigManager
        custom_config = ConfigManager(temp_config_file)
        
        print(f"自定义配置文件: {temp_config_file}")
        print(f"Paraformer API密钥: {custom_config.get('audio.speech_engines.paraformer.api_key')}")
        print(f"Cloudreve URL: {custom_config.get('file_uploader.uploaders.cloudreve.base_url')}")
        print(f"默认上传器: {custom_config.get('file_uploader.default_uploader')}")
        
        # 清理临时文件
        os.unlink(temp_config_file)
        
    except Exception as e:
        print(f"示例2失败: {e}")


def example_3_runtime_config():
    """示例3：运行时配置修改"""
    print("\n=== 示例3：运行时配置修改 ===")
    
    try:
        config_manager = get_config_manager()
        
        # 读取当前配置
        current_timeout = config_manager.get('file_uploader.uploaders.cloudreve.timeout', 60)
        print(f"当前Cloudreve超时设置: {current_timeout}秒")
        
        # 修改配置
        config_manager.set('file_uploader.uploaders.cloudreve.timeout', 120)
        new_timeout = config_manager.get('file_uploader.uploaders.cloudreve.timeout')
        print(f"修改后的超时设置: {new_timeout}秒")
        
        # 批量修改配置
        config_manager.update_section('file_uploader.uploaders.cloudreve', {
            'chunk_size': 2097152,  # 2MB
            'max_retries': 5
        })
        
        print(f"新的块大小: {config_manager.get('file_uploader.uploaders.cloudreve.chunk_size')}")
        print(f"新的重试次数: {config_manager.get('file_uploader.uploaders.cloudreve.max_retries')}")
        
        # 注意：这些修改只在内存中，需要调用save()才能持久化
        print("注意：配置修改仅在内存中，需要调用save()方法才能保存到文件")
        
    except Exception as e:
        print(f"示例3失败: {e}")


def example_4_config_validation():
    """示例4：配置验证"""
    print("\n=== 示例4：配置验证 ===")
    
    try:
        config_manager = get_config_manager()
        
        # 验证配置
        is_valid = config_manager.validate()
        print(f"配置验证结果: {'通过' if is_valid else '失败'}")
        
        # 检查必要的配置项
        required_configs = [
            ('audio.speech_engines.paraformer.api_key', 'Paraformer API密钥'),
            ('file_uploader.uploaders.cloudreve.base_url', 'Cloudreve服务器地址'),
        ]
        
        print("\n必要配置检查:")
        for config_key, description in required_configs:
            value = config_manager.get(config_key, '')
            status = '✓ 已配置' if value else '✗ 未配置'
            print(f"  {description}: {status}")
        
        # 检查可选配置
        optional_configs = [
            ('file_uploader.uploaders.oss.access_key_id', '阿里云OSS'),
            ('audio.speech_engines.azure.api_key', 'Azure语音服务'),
        ]
        
        print("\n可选配置检查:")
        for config_key, description in optional_configs:
            value = config_manager.get(config_key, '')
            status = '✓ 已配置' if value else '- 未配置'
            print(f"  {description}: {status}")
        
    except Exception as e:
        print(f"示例4失败: {e}")


def example_5_integration_test():
    """示例5：集成测试 - 完整的工作流程"""
    print("\n=== 示例5：集成测试 ===")
    
    try:
        # 检查配置是否完整
        config_manager = get_config_manager()
        
        paraformer_key = config_manager.get('audio.speech_engines.paraformer.api_key', '')
        cloudreve_url = config_manager.get('file_uploader.uploaders.cloudreve.base_url', '')
        
        if not paraformer_key:
            print("⚠️  Paraformer API密钥未配置，跳过实际测试")
            print("   请在配置文件中设置 audio.speech_engines.paraformer.api_key")
            return
        
        if not cloudreve_url:
            print("⚠️  Cloudreve服务器未配置，跳过实际测试")
            print("   请在配置文件中设置 file_uploader.uploaders.cloudreve.base_url")
            return
        
        print("✓ 配置检查通过，开始集成测试")
        
        # 创建组件
        recognizer = ParaformerRecognizer()
        print(f"✓ Paraformer识别器已创建，使用模型: {recognizer.model}")
        
        # 检查上传器
        if recognizer.file_uploader.is_uploader_available('cloudreve'):
            print("✓ Cloudreve上传器可用")
        else:
            print("✗ Cloudreve上传器不可用")
            return
        
        # 模拟文件处理流程
        test_file = "test.mp4"
        if os.path.exists(test_file):
            print(f"✓ 找到测试文件: {test_file}")
            print("📤 准备上传文件到Cloudreve...")
            print("🎤 准备调用Paraformer进行语音识别...")
            print("注意：这是模拟流程，实际执行需要有效的API密钥和网络连接")
        else:
            print(f"⚠️  测试文件不存在: {test_file}")
            print("   请在项目根目录放置test.mp4文件进行完整测试")
        
        print("✓ 集成测试配置验证完成")
        
    except Exception as e:
        print(f"示例5失败: {e}")


def show_config_file_example():
    """显示配置文件示例"""
    print("\n=== 配置文件示例 ===")
    
    example_config = """
# VideoReader TOML配置文件示例
# 位置: ~/.videoreader/config.toml

[audio.speech_engines.paraformer]
api_key = "your-dashscope-api-key"
model = "paraformer-v2"
endpoint = "https://dashscope.aliyuncs.com"

[audio.speech_engines.azure]
api_key = "your-azure-speech-key"
region = "eastasia"

[file_uploader]
default_uploader = "auto"

[file_uploader.uploaders.cloudreve]
base_url = "http://your-cloudreve-server.com:5212"
username = "your-username"
password = "your-password"
# 或者使用访问令牌（推荐）
# token = "your-access-token"
timeout = 60
chunk_size = 5242880  # 5MB
max_retries = 3

[file_uploader.uploaders.oss]
access_key_id = "your-oss-access-key-id"
access_key_secret = "your-oss-access-key-secret"
endpoint = "your-oss-endpoint"
bucket_name = "your-bucket-name"
"""
    
    print(example_config)


def main():
    """主函数"""
    print("VideoReader TOML配置使用示例")
    print("=" * 60)
    
    # 运行所有示例
    example_1_basic_usage()
    example_2_custom_config()
    example_3_runtime_config()
    example_4_config_validation()
    example_5_integration_test()
    
    # 显示配置文件示例
    show_config_file_example()
    
    print("\n" + "=" * 60)
    print("📚 更多信息:")
    print("- 配置向导: python scripts/create_config.py")
    print("- 配置测试: python test_toml_config.py")
    print("- 详细文档: doc/toml_configuration_guide.md")
    print("- 快速开始: CLOUDREVE_QUICK_START.md")


if __name__ == "__main__":
    main()
