"""通用工具模块

提供文件操作、图像处理、验证和辅助功能。
"""

from .file_ops import (
    ensure_dir, safe_remove, copy_file, move_file, get_file_size,
    get_file_hash, get_mime_type, is_video_file, is_audio_file,
    find_files, get_available_filename, format_file_size,
    create_temp_file, create_temp_dir, read_chunks, backup_file
)

from .image_ops import (
    load_image, save_image, resize_image, crop_image, create_thumbnail,
    add_text_overlay, calculate_image_similarity, calculate_mse_similarity,
    extract_frame_from_video, create_image_grid
)

from .validators import (
    validate_file_path, validate_video_file, validate_audio_file,
    validate_image_file, validate_directory, validate_url, validate_email,
    validate_numeric_range, validate_string_length, validate_choice,
    validate_regex, validate_file_size, validate_timestamp,
    validate_config_dict, Validator
)

from .helpers import (
    format_duration, format_timestamp, parse_time_string, seconds_to_time_string,
    safe_json_loads, safe_json_dumps, retry_on_exception, timing_decorator,
    thread_safe_singleton, ProgressTracker, Cache, deep_merge_dict,
    flatten_dict, chunk_list, ensure_list, safe_cast
)

__all__ = [
    # 文件操作
    'ensure_dir', 'safe_remove', 'copy_file', 'move_file', 'get_file_size',
    'get_file_hash', 'get_mime_type', 'is_video_file', 'is_audio_file',
    'find_files', 'get_available_filename', 'format_file_size',
    'create_temp_file', 'create_temp_dir', 'read_chunks', 'backup_file',

    # 图像操作
    'load_image', 'save_image', 'resize_image', 'crop_image', 'create_thumbnail',
    'add_text_overlay', 'calculate_image_similarity', 'calculate_mse_similarity',
    'extract_frame_from_video', 'create_image_grid',

    # 验证工具
    'validate_file_path', 'validate_video_file', 'validate_audio_file',
    'validate_image_file', 'validate_directory', 'validate_url', 'validate_email',
    'validate_numeric_range', 'validate_string_length', 'validate_choice',
    'validate_regex', 'validate_file_size', 'validate_timestamp',
    'validate_config_dict', 'Validator',

    # 辅助工具
    'format_duration', 'format_timestamp', 'parse_time_string', 'seconds_to_time_string',
    'safe_json_loads', 'safe_json_dumps', 'retry_on_exception', 'timing_decorator',
    'thread_safe_singleton', 'ProgressTracker', 'Cache', 'deep_merge_dict',
    'flatten_dict', 'chunk_list', 'ensure_list', 'safe_cast'
]
