"""图像操作工具

提供图像处理相关的工具函数。
"""

import numpy as np
from pathlib import Path
from typing import Tuple, Union, Optional
from PIL import Image, ImageDraw, ImageFont

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

from ..exceptions import ProcessingError
from ..logging import get_logger


logger = get_logger(__name__)


def load_image(path: Union[str, Path]) -> np.ndarray:
    """加载图像文件"""
    path = Path(path)
    if not path.exists():
        raise ProcessingError(f"图像文件不存在: {path}")

    try:
        if CV2_AVAILABLE:
            # 使用OpenCV加载图像
            image = cv2.imread(str(path))
            if image is None:
                raise ProcessingError(f"无法加载图像: {path}")

            # 转换为RGB格式
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            return image
        else:
            # 使用PIL加载图像
            pil_image = Image.open(path)
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            return np.array(pil_image)
    except Exception as e:
        raise ProcessingError(f"加载图像失败: {e}")


def save_image(image: np.ndarray, path: Union[str, Path], quality: int = 95):
    """保存图像"""
    path = Path(path)
    
    try:
        # 确保目录存在
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # 转换为PIL Image
        if len(image.shape) == 3:
            pil_image = Image.fromarray(image)
        else:
            pil_image = Image.fromarray(image, mode='L')
        
        # 保存图像
        if path.suffix.lower() in ['.jpg', '.jpeg']:
            pil_image.save(path, 'JPEG', quality=quality)
        else:
            pil_image.save(path)
            
    except Exception as e:
        raise ProcessingError(f"保存图像失败: {e}")


def resize_image(image: np.ndarray, size: Tuple[int, int],
                keep_aspect_ratio: bool = True) -> np.ndarray:
    """调整图像大小"""
    try:
        height, width = image.shape[:2]
        target_width, target_height = size

        if keep_aspect_ratio:
            # 保持宽高比
            aspect_ratio = width / height
            if target_width / target_height > aspect_ratio:
                target_width = int(target_height * aspect_ratio)
            else:
                target_height = int(target_width / aspect_ratio)

        if CV2_AVAILABLE:
            resized = cv2.resize(image, (target_width, target_height),
                               interpolation=cv2.INTER_AREA)
            return resized
        else:
            # 使用PIL调整大小
            pil_image = Image.fromarray(image)
            resized_pil = pil_image.resize((target_width, target_height), Image.Resampling.LANCZOS)
            return np.array(resized_pil)

    except Exception as e:
        raise ProcessingError(f"调整图像大小失败: {e}")


def crop_image(image: np.ndarray, x: int, y: int, width: int, height: int) -> np.ndarray:
    """裁剪图像"""
    try:
        img_height, img_width = image.shape[:2]
        
        # 确保裁剪区域在图像范围内
        x = max(0, min(x, img_width))
        y = max(0, min(y, img_height))
        width = min(width, img_width - x)
        height = min(height, img_height - y)
        
        return image[y:y+height, x:x+width]
        
    except Exception as e:
        raise ProcessingError(f"裁剪图像失败: {e}")


def create_thumbnail(image: np.ndarray, size: Tuple[int, int] = (320, 240)) -> np.ndarray:
    """创建缩略图"""
    return resize_image(image, size, keep_aspect_ratio=True)


def add_text_overlay(image: np.ndarray, text: str, position: Tuple[int, int] = (10, 10),
                    font_size: int = 20, color: Tuple[int, int, int] = (255, 255, 255),
                    background_color: Optional[Tuple[int, int, int]] = None) -> np.ndarray:
    """在图像上添加文字覆盖"""
    try:
        # 转换为PIL Image
        pil_image = Image.fromarray(image)
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载字体
        try:
            # Windows系统字体
            font = ImageFont.truetype("msyh.ttc", font_size)
        except:
            try:
                # 默认字体
                font = ImageFont.load_default()
            except:
                font = None
        
        # 获取文字尺寸
        if font:
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        else:
            text_width = len(text) * 10
            text_height = 15
        
        # 绘制背景
        if background_color:
            draw.rectangle([position[0]-2, position[1]-2, 
                          position[0]+text_width+2, position[1]+text_height+2],
                         fill=background_color)
        
        # 绘制文字
        draw.text(position, text, fill=color, font=font)
        
        return np.array(pil_image)
        
    except Exception as e:
        logger.warning(f"添加文字覆盖失败: {e}")
        return image


def calculate_image_similarity(img1: np.ndarray, img2: np.ndarray) -> float:
    """计算两个图像的相似度（使用结构相似性指数）"""
    try:
        from skimage.metrics import structural_similarity as ssim
        
        # 转换为灰度图像
        if len(img1.shape) == 3:
            gray1 = cv2.cvtColor(img1, cv2.COLOR_RGB2GRAY)
        else:
            gray1 = img1
            
        if len(img2.shape) == 3:
            gray2 = cv2.cvtColor(img2, cv2.COLOR_RGB2GRAY)
        else:
            gray2 = img2
        
        # 确保图像大小相同
        if gray1.shape != gray2.shape:
            gray2 = cv2.resize(gray2, (gray1.shape[1], gray1.shape[0]))
        
        # 计算SSIM
        similarity = ssim(gray1, gray2)
        return similarity
        
    except ImportError:
        # 如果没有skimage，使用简单的均方误差
        logger.warning("skimage未安装，使用简化的相似度计算")
        return calculate_mse_similarity(img1, img2)
    except Exception as e:
        logger.error(f"计算图像相似度失败: {e}")
        return 0.0


def calculate_mse_similarity(img1: np.ndarray, img2: np.ndarray) -> float:
    """使用均方误差计算图像相似度"""
    try:
        # 转换为灰度图像
        if len(img1.shape) == 3:
            gray1 = cv2.cvtColor(img1, cv2.COLOR_RGB2GRAY)
        else:
            gray1 = img1
            
        if len(img2.shape) == 3:
            gray2 = cv2.cvtColor(img2, cv2.COLOR_RGB2GRAY)
        else:
            gray2 = img2
        
        # 确保图像大小相同
        if gray1.shape != gray2.shape:
            gray2 = cv2.resize(gray2, (gray1.shape[1], gray1.shape[0]))
        
        # 计算MSE
        mse = np.mean((gray1.astype(float) - gray2.astype(float)) ** 2)
        
        # 转换为相似度（0-1，1表示完全相同）
        max_mse = 255.0 ** 2
        similarity = 1.0 - (mse / max_mse)
        return max(0.0, similarity)
        
    except Exception as e:
        logger.error(f"计算MSE相似度失败: {e}")
        return 0.0


def extract_frame_from_video(video_path: Union[str, Path], timestamp: float, 
                           size: Optional[Tuple[int, int]] = None) -> np.ndarray:
    """从视频中提取指定时间戳的帧"""
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ProcessingError(f"无法打开视频文件: {video_path}")
        
        # 设置时间戳
        cap.set(cv2.CAP_PROP_POS_MSEC, timestamp * 1000)
        
        # 读取帧
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            raise ProcessingError(f"无法读取时间戳 {timestamp} 处的帧")
        
        # 转换颜色格式
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 调整大小
        if size:
            frame = resize_image(frame, size)
        
        return frame
        
    except Exception as e:
        raise ProcessingError(f"提取视频帧失败: {e}")


def create_image_grid(images: list, grid_size: Tuple[int, int], 
                     image_size: Tuple[int, int] = (160, 120)) -> np.ndarray:
    """创建图像网格"""
    try:
        rows, cols = grid_size
        img_height, img_width = image_size
        
        # 创建空白画布
        grid_height = rows * img_height
        grid_width = cols * img_width
        grid = np.zeros((grid_height, grid_width, 3), dtype=np.uint8)
        
        # 填充图像
        for i, image in enumerate(images[:rows * cols]):
            row = i // cols
            col = i % cols
            
            # 调整图像大小
            resized_img = resize_image(image, (img_width, img_height))
            
            # 放置到网格中
            y_start = row * img_height
            y_end = y_start + img_height
            x_start = col * img_width
            x_end = x_start + img_width
            
            grid[y_start:y_end, x_start:x_end] = resized_img
        
        return grid
        
    except Exception as e:
        raise ProcessingError(f"创建图像网格失败: {e}")
