"""Paraformer语音识别器测试

测试阿里云Paraformer语音识别器的功能。
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.video_processor.audio_engine.recognizer import ParaformerRecognizer
from src.modules.video_processor.audio_engine.models import TranscriptSegment
from src.common.exceptions import SpeechRecognitionError


class TestParaformerRecognizer(unittest.TestCase):
    """Paraformer识别器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.recognizer = ParaformerRecognizer()
        
        # 模拟API密钥
        self.recognizer.api_key = "test_api_key"
    
    def test_init(self):
        """测试初始化"""
        # 测试默认参数
        recognizer = ParaformerRecognizer()
        self.assertEqual(recognizer.model, 'paraformer-v2')
        self.assertIsNotNone(recognizer.file_uploader)
        
        # 测试自定义参数
        recognizer = ParaformerRecognizer(api_key="custom_key", model="paraformer-8k-v2")
        self.assertEqual(recognizer.api_key, "custom_key")
        self.assertEqual(recognizer.model, "paraformer-8k-v2")
    
    def test_supported_models(self):
        """测试支持的模型列表"""
        expected_models = [
            'paraformer-v2',
            'paraformer-8k-v2', 
            'paraformer-v1',
            'paraformer-8k-v1',
            'paraformer-mtl-v1'
        ]
        
        for model in expected_models:
            self.assertIn(model, self.recognizer.supported_models)
            
        # 检查模型信息完整性
        for model, info in self.recognizer.supported_models.items():
            self.assertIn('description', info)
            self.assertIn('languages', info)
            self.assertIn('sample_rates', info)
            self.assertIn('features', info)
    
    def test_is_available_with_sdk(self):
        """测试SDK可用时的可用性检查"""
        # 模拟SDK可用且有API密钥
        with patch('builtins.__import__') as mock_import:
            mock_import.return_value = Mock()  # 模拟成功导入dashscope
            self.recognizer.api_key = "test_key"
            result = self.recognizer.is_available()
            self.assertTrue(result)
    
    def test_is_available_without_api_key(self):
        """测试没有API密钥时的可用性检查"""
        # 模拟SDK可用但没有API密钥
        with patch('builtins.__import__') as mock_import:
            mock_import.return_value = Mock()  # 模拟成功导入dashscope
            self.recognizer.api_key = None
            result = self.recognizer.is_available()
            self.assertFalse(result)
    
    def test_is_available_without_sdk(self):
        """测试SDK不可用时的可用性检查"""
        # 模拟SDK不可用
        with patch('builtins.__import__', side_effect=ImportError("No module named 'dashscope'")):
            result = self.recognizer.is_available()
            self.assertFalse(result)
    
    def test_convert_language_hints(self):
        """测试语言代码转换"""
        test_cases = [
            ('zh', ['zh']),
            ('en', ['en']),
            ('ja', ['ja']),
            ('ko', ['ko']),
            ('zh-cn', ['zh']),
            ('zh-tw', ['zh']),
            ('en-us', ['en']),
            ('yue', ['yue']),
            ('unknown', ['zh', 'en'])  # 默认值
        ]
        
        for input_lang, expected in test_cases:
            result = self.recognizer._convert_language_hints(input_lang)
            self.assertEqual(result, expected)
    
    @patch('src.modules.video_processor.audio_engine.recognizer.requests')
    def test_download_transcription_result_success(self, mock_requests):
        """测试成功下载识别结果"""
        # 模拟成功的HTTP响应
        mock_response = Mock()
        mock_response.json.return_value = {
            'transcripts': [
                {
                    'sentences': [
                        {
                            'begin_time': 1000,
                            'end_time': 3000,
                            'text': '测试文本',
                            'speaker_id': 0
                        }
                    ]
                }
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_requests.get.return_value = mock_response
        
        result = self.recognizer._download_transcription_result('http://test.url')
        
        self.assertIn('transcripts', result)
        mock_requests.get.assert_called_once_with('http://test.url', timeout=30)
    
    @patch('src.modules.video_processor.audio_engine.recognizer.requests')
    def test_download_transcription_result_failure(self, mock_requests):
        """测试下载识别结果失败"""
        # 模拟HTTP请求失败
        mock_requests.get.side_effect = Exception("Network error")
        
        with self.assertRaises(SpeechRecognitionError):
            self.recognizer._download_transcription_result('http://test.url')
    
    def test_parse_transcription_results(self):
        """测试解析识别结果"""
        # 模拟API输出
        mock_output = Mock()
        mock_output.results = [
            Mock(
                subtask_status='SUCCEEDED',
                file_url='http://test.url',
                transcription_url='http://result.url'
            )
        ]
        
        # 模拟下载的识别结果
        transcription_data = {
            'transcripts': [
                {
                    'sentences': [
                        {
                            'begin_time': 1000,
                            'end_time': 3000,
                            'text': '你好世界',
                            'speaker_id': 0
                        },
                        {
                            'begin_time': 3500,
                            'end_time': 5000,
                            'text': 'Hello World',
                            'speaker_id': 1
                        }
                    ]
                }
            ]
        }
        
        with patch.object(self.recognizer, '_download_transcription_result', return_value=transcription_data):
            segments = self.recognizer._parse_transcription_results(mock_output, 'zh')
            
            self.assertEqual(len(segments), 2)
            
            # 检查第一个段落
            self.assertEqual(segments[0].id, 0)
            self.assertEqual(segments[0].start_time, 1.0)
            self.assertEqual(segments[0].end_time, 3.0)
            self.assertEqual(segments[0].text, '你好世界')
            self.assertEqual(segments[0].speaker_id, '0')
            self.assertEqual(segments[0].language, 'zh')
            
            # 检查第二个段落
            self.assertEqual(segments[1].id, 1)
            self.assertEqual(segments[1].start_time, 3.5)
            self.assertEqual(segments[1].end_time, 5.0)
            self.assertEqual(segments[1].text, 'Hello World')
            self.assertEqual(segments[1].speaker_id, '1')
    
    def test_parse_transcription_results_empty(self):
        """测试解析空的识别结果"""
        mock_output = Mock()
        mock_output.results = []
        
        segments = self.recognizer._parse_transcription_results(mock_output, 'zh')
        self.assertEqual(len(segments), 0)
    
    def test_parse_transcription_results_failed_subtask(self):
        """测试解析包含失败子任务的识别结果"""
        mock_output = Mock()
        mock_output.results = [
            Mock(
                subtask_status='FAILED',
                file_url='http://test.url'
            )
        ]
        
        segments = self.recognizer._parse_transcription_results(mock_output, 'zh')
        self.assertEqual(len(segments), 0)
    
    @patch('os.path.exists')
    @patch('time.time')
    def test_upload_audio_file_success(self, mock_time, mock_exists):
        """测试成功上传音频文件"""
        mock_exists.return_value = True
        mock_time.return_value = 1234567890
        
        # 模拟文件上传管理器
        mock_uploader = Mock()
        mock_uploader.upload.return_value = 'http://test.url/audio.wav'
        self.recognizer.file_uploader = mock_uploader
        
        result = self.recognizer._upload_audio_file('/path/to/audio.wav')
        
        self.assertEqual(result, 'http://test.url/audio.wav')
        mock_uploader.upload.assert_called_once_with(
            '/path/to/audio.wav',
            uploader='auto',
            object_name='paraformer/1234567890_audio.wav'
        )
    
    @patch('os.path.exists')
    def test_upload_audio_file_not_exists(self, mock_exists):
        """测试上传不存在的音频文件"""
        mock_exists.return_value = False
        
        with self.assertRaises(SpeechRecognitionError):
            self.recognizer._upload_audio_file('/path/to/nonexistent.wav')
    
    def test_recognize_not_available(self):
        """测试识别器不可用时的行为"""
        with patch.object(self.recognizer, 'is_available', return_value=False):
            with self.assertRaises(SpeechRecognitionError):
                self.recognizer.recognize('/path/to/audio.wav')


class TestParaformerIntegration(unittest.TestCase):
    """Paraformer集成测试"""
    
    def test_recognizer_registration(self):
        """测试识别器是否正确注册到管理器中"""
        from src.modules.video_processor.audio_engine.recognizer import SpeechRecognizer
        
        speech_recognizer = SpeechRecognizer()
        
        # 检查paraformer是否在识别器列表中
        self.assertIn('paraformer', speech_recognizer.recognizers)
        
        # 检查识别器类型
        paraformer = speech_recognizer.recognizers['paraformer']
        self.assertIsInstance(paraformer, ParaformerRecognizer)
    
    def test_get_available_engines(self):
        """测试获取可用引擎列表"""
        from src.modules.video_processor.audio_engine.recognizer import SpeechRecognizer
        
        speech_recognizer = SpeechRecognizer()
        available_engines = speech_recognizer.get_available_engines()
        
        # 由于测试环境可能没有安装相关SDK，这里只检查方法是否正常工作
        self.assertIsInstance(available_engines, list)


if __name__ == '__main__':
    unittest.main()
