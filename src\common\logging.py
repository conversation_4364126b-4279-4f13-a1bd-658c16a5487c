"""日志系统模块主入口

提供统一的日志记录接口。
"""

# 从子模块导入主要功能
from .logging.logger import get_logger, setup_logging, LogLevel
from .logging.formatters import ColoredFormatter, StructuredFormatter

# 全局日志配置
_logging_initialized = False

def init_logging(level: LogLevel = LogLevel.INFO, 
                log_file: str = None,
                use_colors: bool = True):
    """初始化日志系统"""
    global _logging_initialized
    if not _logging_initialized:
        setup_logging(level, log_file, use_colors)
        _logging_initialized = True

def get_module_logger(name: str):
    """获取模块日志记录器"""
    if not _logging_initialized:
        init_logging()
    return get_logger(name)

# 导出主要接口
__all__ = [
    'get_logger',
    'setup_logging', 
    'init_logging',
    'get_module_logger',
    'LogLevel',
    'ColoredFormatter',
    'StructuredFormatter'
]
