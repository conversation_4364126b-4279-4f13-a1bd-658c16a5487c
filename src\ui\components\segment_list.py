"""段落列表组件

显示视频段落列表，支持选择、搜索和过滤功能。
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QLabel, QPushButton, QGroupBox
)
from PySide6.QtCore import Signal
from typing import List, Optional, Dict, Any
from pathlib import Path

try:
    from ...core.models import SegmentInfo, ProcessResult
    from ...common.logging import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from src.core.models import SegmentInfo, ProcessResult
    from src.common.logging import get_logger


logger = get_logger(__name__)


class SegmentListItem(QListWidgetItem):
    """段落列表项"""
    
    def __init__(self, segment: SegmentInfo):
        super().__init__()
        self.segment = segment
        self._update_display()
    
    def _update_display(self):
        """更新显示内容"""
        # 格式化时间
        start_time = self._format_time(self.segment.start_time)
        end_time = self._format_time(self.segment.end_time)
        duration = self._format_time(self.segment.duration)
        
        # 截取文本预览
        text_preview = self.segment.text[:50] + "..." if len(self.segment.text) > 50 else self.segment.text
        
        # 设置显示文本
        display_text = f"[{self.segment.id + 1}] {start_time}-{end_time} ({duration})\n{text_preview}"
        self.setText(display_text)
        
        # 设置工具提示
        tooltip = f"段落 {self.segment.id + 1}\n"
        tooltip += f"时间: {start_time} - {end_time}\n"
        tooltip += f"时长: {duration}\n"
        tooltip += f"置信度: {self.segment.confidence:.2f}\n"
        if self.segment.summary:
            tooltip += f"摘要: {self.segment.summary}\n"
        tooltip += f"内容: {self.segment.text}"
        self.setToolTip(tooltip)
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"


class SegmentListWidget(QWidget):
    """段落列表组件"""
    
    # 信号定义
    segment_selected = Signal(object)  # 段落被选中
    segment_double_clicked = Signal(object)  # 段落被双击
    play_segment_requested = Signal(object)  # 请求播放段落
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # 数据
        self.segments: List[SegmentInfo] = []
        self.filtered_segments: List[SegmentInfo] = []
        self.current_segment: Optional[SegmentInfo] = None
        
        # 当前选中的文件信息
        self.current_file_path = None
        self.current_file_info = {}

        # 初始化UI
        self._init_ui()
        self._connect_signals()

        self.logger.info("段落列表组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 文件信息显示（置顶）
        info_group = QGroupBox("文件信息")
        info_layout = QVBoxLayout(info_group)

        self.info_label = QLabel("未选择文件")
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("QLabel { padding: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; }")
        info_layout.addWidget(self.info_label)

        layout.addWidget(info_group)

        # 段落列表
        list_group = QGroupBox("段落列表")
        list_layout = QVBoxLayout(list_group)
        
        # 统计信息
        self.stats_label = QLabel("共 0 个段落")
        list_layout.addWidget(self.stats_label)
        
        # 列表控件
        self.segment_list = QListWidget()
        self.segment_list.itemSelectionChanged.connect(self._on_selection_changed)
        self.segment_list.itemDoubleClicked.connect(self._on_item_double_clicked)
        list_layout.addWidget(self.segment_list)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.play_button = QPushButton("播放段落")
        self.play_button.clicked.connect(self._on_play_button_clicked)
        self.play_button.setEnabled(False)
        button_layout.addWidget(self.play_button)
        
        self.export_button = QPushButton("导出选中")
        self.export_button.clicked.connect(self._on_export_button_clicked)
        self.export_button.setEnabled(False)
        button_layout.addWidget(self.export_button)
        
        button_layout.addStretch()
        list_layout.addLayout(button_layout)
        
        layout.addWidget(list_group)
    
    def _connect_signals(self):
        """连接信号和槽"""
        pass
    
    def load_segments(self, process_result: ProcessResult):
        """加载段落数据
        
        Args:
            process_result: 处理结果
        """
        try:
            self.logger.info(f"加载段落数据: {len(process_result.segments)} 个段落")
            
            self.segments = process_result.segments
            self.filtered_segments = self.segments.copy()
            
            self._update_segment_list()
            self._update_stats()
            
        except Exception as e:
            self.logger.error(f"加载段落数据失败: {e}")
    
    def _update_segment_list(self):
        """更新段落列表显示"""
        self.segment_list.clear()
        
        for segment in self.filtered_segments:
            item = SegmentListItem(segment)
            self.segment_list.addItem(item)
    
    def _update_stats(self):
        """更新统计信息"""
        total_count = len(self.segments)
        filtered_count = len(self.filtered_segments)
        
        if total_count == filtered_count:
            stats_text = f"共 {total_count} 个段落"
        else:
            stats_text = f"显示 {filtered_count} / {total_count} 个段落"
        
        self.stats_label.setText(stats_text)
    

    
    def _on_selection_changed(self):
        """选择改变处理"""
        current_item = self.segment_list.currentItem()
        
        if isinstance(current_item, SegmentListItem):
            self.current_segment = current_item.segment
            self.play_button.setEnabled(True)
            self.export_button.setEnabled(True)
            
            # 发送选择信号
            self.segment_selected.emit(self.current_segment)
        else:
            self.current_segment = None
            self.play_button.setEnabled(False)
            self.export_button.setEnabled(False)
    
    def _on_item_double_clicked(self, item: QListWidgetItem):
        """列表项双击处理"""
        if isinstance(item, SegmentListItem):
            self.segment_double_clicked.emit(item.segment)
    
    def _on_play_button_clicked(self):
        """播放按钮点击处理"""
        if self.current_segment:
            self.play_segment_requested.emit(self.current_segment)
    
    def _on_export_button_clicked(self):
        """导出按钮点击处理"""
        if self.current_segment:
            # 这里可以实现导出功能
            self.logger.info(f"导出段落: {self.current_segment.id}")
    
    def select_segment_by_id(self, segment_id: int):
        """根据ID选择段落"""
        for i in range(self.segment_list.count()):
            item = self.segment_list.item(i)
            if isinstance(item, SegmentListItem) and item.segment.id == segment_id:
                self.segment_list.setCurrentItem(item)
                break
    
    def select_segment_by_time(self, time_seconds: float):
        """根据时间选择段落"""
        for i in range(self.segment_list.count()):
            item = self.segment_list.item(i)
            if isinstance(item, SegmentListItem):
                segment = item.segment
                if segment.start_time <= time_seconds <= segment.end_time:
                    self.segment_list.setCurrentItem(item)
                    break
    
    def get_selected_segment(self) -> Optional[SegmentInfo]:
        """获取当前选中的段落"""
        return self.current_segment
    
    def get_all_segments(self) -> List[SegmentInfo]:
        """获取所有段落"""
        return self.segments.copy()
    
    def get_filtered_segments(self) -> List[SegmentInfo]:
        """获取过滤后的段落"""
        return self.filtered_segments.copy()

    def update_file_info(self, file_path: str, file_info: Dict[str, Any] = None):
        """更新文件信息显示

        Args:
            file_path: 文件路径
            file_info: 文件信息字典，包含文件类型、状态、元信息路径等
        """
        self.current_file_path = file_path
        self.current_file_info = file_info or {}

        if not file_path:
            self.info_label.setText("未选择文件")
            return

        path_obj = Path(file_path)

        # 构建信息显示
        info_lines = [
            f"文件名: {path_obj.name}",
            f"路径: {file_path}",
        ]

        # 添加文件类型信息（不显示状态信息）
        if file_info:
            file_type = file_info.get('file_type', '')
            if file_type == 'video':
                info_lines.append("类型: 视频文件")
            elif file_type == 'metadata':
                info_lines.append("类型: 元信息文件")
        else:
            # 根据文件扩展名推断类型
            ext = path_obj.suffix.lower()
            if ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']:
                info_lines.append("类型: 视频文件")
            elif ext == '.json':
                info_lines.append("类型: 元信息文件")

        # 添加文件大小信息
        try:
            file_size = path_obj.stat().st_size
            if file_size > 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            elif file_size > 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size} B"
            info_lines.append(f"大小: {size_str}")
        except Exception:
            pass

        self.info_label.setText("\n".join(info_lines))
        self.logger.debug(f"更新文件信息: {path_obj.name}")

    def clear_file_info(self):
        """清除文件信息显示"""
        self.current_file_path = None
        self.current_file_info = {}
        self.info_label.setText("未选择文件")

    def apply_search_filter(self, search_params: Dict[str, Any]):
        """应用搜索和过滤条件

        Args:
            search_params: 搜索参数字典，包含text、scope、duration_filter、confidence_filter
        """
        try:
            search_text = search_params.get('text', '').lower()
            duration_filter = search_params.get('duration_filter', '全部')
            confidence_filter = search_params.get('confidence_filter', '全部')

            self.filtered_segments = []

            for segment in self.segments:
                # 检查搜索条件
                if self._match_search_text(segment, search_text):
                    # 检查过滤条件
                    if self._match_filters(segment, duration_filter, confidence_filter):
                        self.filtered_segments.append(segment)

            self._update_segment_list()
            self._update_stats()

            self.logger.debug(f"应用搜索过滤: {len(self.filtered_segments)}/{len(self.segments)} 个段落")

        except Exception as e:
            self.logger.error(f"应用搜索过滤失败: {e}")

    def _match_search_text(self, segment: SegmentInfo, search_text: str) -> bool:
        """检查段落是否匹配搜索文本"""
        if not search_text:
            return True

        # 在文本内容中搜索
        if search_text in segment.text.lower():
            return True

        # 在摘要中搜索
        if segment.summary and search_text in segment.summary.lower():
            return True

        return False

    def _match_filters(self, segment: SegmentInfo, duration_filter: str, confidence_filter: str) -> bool:
        """检查段落是否匹配过滤条件"""
        # 时长过滤
        if duration_filter != "全部":
            duration = segment.duration
            if duration_filter == "短段落(<30s)" and duration >= 30:
                return False
            elif duration_filter == "中等段落(30s-2m)" and (duration < 30 or duration > 120):
                return False
            elif duration_filter == "长段落(>2m)" and duration <= 120:
                return False

        # 置信度过滤
        if confidence_filter != "全部":
            confidence = segment.confidence
            if confidence_filter == "高(>0.8)" and confidence <= 0.8:
                return False
            elif confidence_filter == "中(0.5-0.8)" and (confidence < 0.5 or confidence > 0.8):
                return False
            elif confidence_filter == "低(<0.5)" and confidence >= 0.5:
                return False

        return True
