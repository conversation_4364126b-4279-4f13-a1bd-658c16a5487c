"""视频预处理模块数据模型

定义视频预处理模块内部使用的数据结构。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
import numpy as np

try:
    from ...core.models import VideoInfo, TranscriptSegment
except ImportError:
    from core.models import VideoInfo, TranscriptSegment


class ProcessingStage(Enum):
    """处理阶段枚举"""
    INITIALIZING = "initializing"
    LOADING_VIDEO = "loading_video"
    EXTRACTING_AUDIO = "extracting_audio"
    RECOGNIZING_SPEECH = "recognizing_speech"
    ANALYZING_VIDEO = "analyzing_video"
    PARSING_SEGMENTS = "parsing_segments"
    GENERATING_THUMBNAILS = "generating_thumbnails"
    FINALIZING = "finalizing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProcessingProgress:
    """处理进度数据模型"""
    stage: ProcessingStage
    progress: float  # 0.0 - 1.0
    message: str
    details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'stage': self.stage.value,
            'progress': self.progress,
            'message': self.message,
            'details': self.details or {}
        }


@dataclass
class VideoFrame:
    """视频帧数据模型"""
    timestamp: float
    frame_data: np.ndarray
    frame_index: int
    width: int
    height: int
    
    @property
    def shape(self) -> Tuple[int, int, int]:
        return self.frame_data.shape


@dataclass
class SceneChange:
    """场景变化数据模型"""
    timestamp: float
    frame_index: int
    similarity_score: float
    change_type: str = "cut"  # cut, fade, dissolve
    
    def __post_init__(self):
        # 确保相似度分数在合理范围内
        self.similarity_score = max(0.0, min(1.0, self.similarity_score))


@dataclass
class AudioSegment:
    """音频段落数据模型"""
    start_time: float
    end_time: float
    audio_data: Optional[np.ndarray] = None
    sample_rate: int = 16000
    is_silence: bool = False
    volume_level: float = 0.0
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time


@dataclass
class SpeechRecognitionResult:
    """语音识别结果数据模型"""
    segments: List[TranscriptSegment]
    language: str
    confidence: float
    processing_time: float
    engine: str
    model_info: Optional[Dict[str, Any]] = None
    
    @property
    def total_duration(self) -> float:
        if not self.segments:
            return 0.0
        return max(seg.end_time for seg in self.segments)
    
    @property
    def total_text_length(self) -> int:
        return sum(len(seg.text) for seg in self.segments)


@dataclass
class ParsingContext:
    """解析上下文数据模型"""
    video_info: VideoInfo
    transcript: List[TranscriptSegment]
    scene_changes: List[SceneChange] = field(default_factory=list)
    audio_segments: List[AudioSegment] = field(default_factory=list)
    config: Dict[str, Any] = field(default_factory=dict)
    
    def get_transcript_at_time(self, timestamp: float) -> Optional[TranscriptSegment]:
        """获取指定时间的转录段落"""
        for segment in self.transcript:
            if segment.start_time <= timestamp <= segment.end_time:
                return segment
        return None
    
    def get_scene_changes_in_range(self, start_time: float, end_time: float) -> List[SceneChange]:
        """获取时间范围内的场景变化"""
        return [sc for sc in self.scene_changes 
                if start_time <= sc.timestamp <= end_time]


@dataclass
class ThumbnailInfo:
    """缩略图信息数据模型"""
    timestamp: float
    file_path: str
    width: int
    height: int
    file_size: int
    created_time: Optional[str] = None
    
    def __post_init__(self):
        if self.created_time is None:
            from datetime import datetime
            self.created_time = datetime.now().isoformat()


@dataclass
class ProcessingStatistics:
    """处理统计信息数据模型"""
    total_processing_time: float
    video_loading_time: float
    audio_extraction_time: float
    speech_recognition_time: float
    parsing_time: float
    thumbnail_generation_time: float
    total_segments: int
    total_frames_processed: int
    total_audio_duration: float
    memory_usage_peak: float  # MB
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'total_processing_time': self.total_processing_time,
            'video_loading_time': self.video_loading_time,
            'audio_extraction_time': self.audio_extraction_time,
            'speech_recognition_time': self.speech_recognition_time,
            'parsing_time': self.parsing_time,
            'thumbnail_generation_time': self.thumbnail_generation_time,
            'total_segments': self.total_segments,
            'total_frames_processed': self.total_frames_processed,
            'total_audio_duration': self.total_audio_duration,
            'memory_usage_peak': self.memory_usage_peak
        }


@dataclass
class ProcessingTask:
    """处理任务数据模型"""
    task_id: str
    video_path: str
    config: Dict[str, Any]
    status: ProcessingStage
    progress: ProcessingProgress
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    statistics: Optional[ProcessingStatistics] = None
    
    @property
    def is_completed(self) -> bool:
        return self.status in [ProcessingStage.COMPLETED, ProcessingStage.FAILED, ProcessingStage.CANCELLED]
    
    @property
    def processing_time(self) -> Optional[float]:
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


@dataclass
class CacheEntry:
    """缓存条目数据模型"""
    key: str
    data: Any
    created_time: float
    access_count: int = 0
    last_access_time: Optional[float] = None
    size_bytes: int = 0
    
    def update_access(self):
        """更新访问信息"""
        import time
        self.access_count += 1
        self.last_access_time = time.time()
    
    @property
    def age(self) -> float:
        """获取缓存年龄（秒）"""
        import time
        return time.time() - self.created_time


@dataclass
class EngineConfig:
    """引擎配置数据模型"""
    video_engine: Dict[str, Any] = field(default_factory=dict)
    audio_engine: Dict[str, Any] = field(default_factory=dict)
    parser_engine: Dict[str, Any] = field(default_factory=dict)
    
    def get_video_config(self, key: str, default: Any = None) -> Any:
        return self.video_engine.get(key, default)
    
    def get_audio_config(self, key: str, default: Any = None) -> Any:
        return self.audio_engine.get(key, default)
    
    def get_parser_config(self, key: str, default: Any = None) -> Any:
        return self.parser_engine.get(key, default)
    
    def set_video_config(self, key: str, value: Any):
        self.video_engine[key] = value
    
    def set_audio_config(self, key: str, value: Any):
        self.audio_engine[key] = value
    
    def set_parser_config(self, key: str, value: Any):
        self.parser_engine[key] = value
