"""异常定义模块主入口

提供统一的异常定义接口。
"""

# 从子模块导入主要功能
from .exceptions.base import VideoReaderError, ValidationError
from .exceptions.custom import (
    VideoLoadError,
    AudioProcessingError,
    ParsingError,
    StorageError,
    SearchIndexError,
    UIError,
    ConfigurationError,
    NetworkError,
    PermissionError as VideoReaderPermissionError
)

# 导出主要接口
__all__ = [
    # 基础异常
    'VideoReaderError',
    'ValidationError',
    
    # 具体异常
    'VideoLoadError',
    'AudioProcessingError', 
    'ParsingError',
    'StorageError',
    'SearchIndexError',
    'UIError',
    'ConfigurationError',
    'NetworkError',
    'VideoReaderPermissionError'
]
