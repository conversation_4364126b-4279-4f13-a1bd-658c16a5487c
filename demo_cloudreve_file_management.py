#!/usr/bin/env python3
"""Cloudreve文件管理功能演示"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config
from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


def print_file_list(files: list, title: str = "文件列表"):
    """打印文件列表"""
    print(f"\n📁 {title}")
    print("-" * 80)
    
    if not files:
        print("   (空目录)")
        return
    
    for i, file_info in enumerate(files, 1):
        file_type = "📁" if file_info.get('type') == 'folder' else "📄"
        name = file_info.get('name', 'Unknown')
        size = format_size(file_info.get('size', 0))
        modified = file_info.get('date', 'Unknown')
        
        print(f"{i:3d}. {file_type} {name:<30} {size:>10} {modified}")


def demo_file_operations(client: CloudreveClient):
    """演示文件操作功能"""
    print("\n" + "="*60)
    print("📂 文件管理功能演示")
    print("="*60)
    
    try:
        # 1. 获取存储信息
        print("\n1️⃣ 存储空间信息")
        storage_info = client.get_storage_info()
        used = storage_info.get('used', 0)
        total = storage_info.get('total', 0)
        
        print(f"   已使用: {format_size(used)}")
        print(f"   总容量: {format_size(total)}")
        if total > 0:
            usage_percent = (used / total) * 100
            print(f"   使用率: {usage_percent:.1f}%")
        
        # 2. 列出根目录文件
        print("\n2️⃣ 根目录文件列表")
        file_list = client.list_files("/")
        files = file_list.get('items', [])
        print_file_list(files, "根目录")
        
        if not files:
            print("   根目录为空，跳过后续文件操作演示")
            return
        
        # 3. 选择第一个文件进行操作演示
        first_file = files[0]
        file_id = first_file.get('id')
        file_name = first_file.get('name')
        file_type = first_file.get('type', 'file')
        
        print(f"\n3️⃣ 文件详细信息 - {file_name}")
        file_info = client.get_file_info(file_id)
        
        print(f"   文件ID: {file_info.get('id', 'N/A')}")
        print(f"   文件名: {file_info.get('name', 'N/A')}")
        print(f"   大小: {format_size(file_info.get('size', 0))}")
        print(f"   类型: {file_info.get('type', 'N/A')}")
        print(f"   创建时间: {file_info.get('date', 'N/A')}")
        
        # 4. 创建下载链接（仅对文件）
        if file_type == 'file':
            print(f"\n4️⃣ 创建下载链接 - {file_name}")
            try:
                download_url = client.create_download_url(file_id)
                print(f"   下载链接: {download_url}")
            except CloudreveError as e:
                print(f"   ❌ 创建下载链接失败: {e}")
        
        # 5. 创建分享链接
        print(f"\n5️⃣ 创建分享链接 - {file_name}")
        try:
            share_info = client.create_share_link(
                file_ids=[file_id],
                password="test123",  # 设置密码
                expire_days=7,       # 7天后过期
                download_limit=10,   # 限制下载10次
                preview_enabled=True
            )
            
            share_key = share_info.get('key', '')
            share_url = f"{client.base_url}/s/{share_key}"
            
            print(f"   分享链接: {share_url}")
            print(f"   分享密码: test123")
            print(f"   有效期: 7天")
            print(f"   下载限制: 10次")
            
            # 6. 获取分享链接信息
            print(f"\n6️⃣ 验证分享链接信息")
            try:
                share_detail = client.get_share_info(share_key, "test123")
                print(f"   分享者: {share_detail.get('creator', 'N/A')}")
                print(f"   创建时间: {share_detail.get('create_date', 'N/A')}")
                print(f"   文件数量: {len(share_detail.get('items', []))}")
            except CloudreveError as e:
                print(f"   ⚠️ 获取分享信息失败: {e}")
            
            # 7. 删除分享链接
            print(f"\n7️⃣ 清理分享链接")
            share_id = share_info.get('id')
            if share_id:
                try:
                    client.delete_share_link(share_id)
                    print("   ✅ 分享链接已删除")
                except CloudreveError as e:
                    print(f"   ⚠️ 删除分享链接失败: {e}")
            
        except CloudreveError as e:
            print(f"   ❌ 创建分享链接失败: {e}")
        
        # 8. 搜索功能演示
        print(f"\n8️⃣ 搜索功能演示")
        try:
            # 搜索包含文件名关键词的文件
            keyword = file_name[:3] if len(file_name) >= 3 else file_name
            search_results = client.search_files(keyword, page_size=5)
            search_files = search_results.get('items', [])
            
            print(f"   搜索关键词: '{keyword}'")
            print(f"   搜索结果: {len(search_files)} 个文件")
            
            if search_files:
                print_file_list(search_files[:3], f"搜索结果 (前3个)")
                
        except CloudreveError as e:
            print(f"   ⚠️ 搜索失败: {e}")
        
        # 9. 获取我的分享链接列表
        print(f"\n9️⃣ 我的分享链接")
        try:
            my_shares = client.list_share_links(page_size=5)
            shares = my_shares.get('items', [])
            
            print(f"   分享链接数量: {len(shares)}")
            
            if shares:
                print("\n   最近的分享链接:")
                for i, share in enumerate(shares[:3], 1):
                    share_key = share.get('key', 'N/A')
                    create_date = share.get('create_date', 'N/A')
                    downloads = share.get('downloads', 0)
                    print(f"   {i}. {share_key} (创建: {create_date}, 下载: {downloads}次)")
                    
        except CloudreveError as e:
            print(f"   ⚠️ 获取分享链接列表失败: {e}")
        
        print(f"\n✅ 文件管理功能演示完成!")
        
    except CloudreveError as e:
        print(f"\n❌ 文件操作演示失败: {e}")


def main():
    """主函数"""
    print("Cloudreve文件管理功能演示")
    print("=" * 60)
    
    # 从配置文件获取连接信息
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    if not all([base_url, username, password]):
        print("❌ 配置信息不完整，请检查config.toml文件")
        return
    
    if '@' not in username:
        print(f"❌ 用户名不是email格式: {username}")
        print("请运行 python fix_cloudreve_config.py 修正配置")
        return
    
    try:
        # 创建客户端并登录
        client = CloudreveClient(base_url, username, password)
        
        print(f"🔗 连接服务器: {base_url}")
        print(f"👤 用户账户: {username}")
        
        # 测试连接
        version = client.ping()
        print(f"📡 服务器版本: {version}")
        
        # 登录
        print("\n🔐 正在登录...")
        login_result = client.login()
        user_info = login_result['user']
        print(f"✅ 登录成功: {user_info['nickname']}")
        
        # 演示文件管理功能
        demo_file_operations(client)
        
        # 登出
        print(f"\n🚪 正在登出...")
        client.logout()
        print("✅ 登出成功")
        
    except CloudreveError as e:
        print(f"\n❌ 操作失败: {e}")
        if e.code == 40003:
            print("💡 提示: 服务器要求验证码，请手动处理验证码登录")


if __name__ == "__main__":
    main()
