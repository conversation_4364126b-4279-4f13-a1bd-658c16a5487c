"""VideoReader 安装配置文件"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# 读取requirements.txt
def read_requirements(filename):
    """读取依赖文件"""
    requirements = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                # 跳过注释和空行
                if line and not line.startswith('#') and not line.startswith('-r'):
                    # 移除行内注释
                    if '#' in line:
                        line = line.split('#')[0].strip()
                    requirements.append(line)
    except FileNotFoundError:
        pass
    return requirements

# 基础依赖
install_requires = read_requirements('requirements.txt')

# 可选依赖
extras_require = {
    'gui': [
        'PySide6>=6.0.0',
        'qt-material>=2.14',
        'qdarkstyle>=3.0.0',
    ],
    'ml': [
        'torch>=1.9.0',
        'transformers>=4.0.0',
        'scikit-learn>=1.0.0',
    ],
    'dev': read_requirements('requirements-dev.txt'),
    'docs': [
        'sphinx>=5.0.0',
        'sphinx-rtd-theme>=1.0.0',
        'myst-parser>=0.18.0',
    ],
    'test': [
        'pytest>=7.0.0',
        'pytest-cov>=4.0.0',
        'pytest-xdist>=2.5.0',
        'pytest-mock>=3.8.0',
    ]
}

# 所有可选依赖
extras_require['all'] = list(set(
    dep for deps in extras_require.values() for dep in deps
))

setup(
    name="videoreader",
    version="1.0.0",
    author="VideoReader Team",
    author_email="<EMAIL>",
    description="将视频转换为可阅读文本和图片格式的应用程序",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/videoreader",
    project_urls={
        "Bug Tracker": "https://github.com/your-org/videoreader/issues",
        "Documentation": "https://videoreader.readthedocs.io/",
        "Source Code": "https://github.com/your-org/videoreader",
    },
    
    # 包配置
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    # 包含非Python文件
    include_package_data=True,
    package_data={
        "videoreader": [
            "ui/styles/*.qss",
            "ui/icons/*.png",
            "ui/icons/*.ico",
            "common/config/*.json",
            "common/config/*.yaml",
        ],
    },
    
    # 依赖
    python_requires=">=3.8",
    install_requires=install_requires,
    extras_require=extras_require,
    
    # 入口点
    entry_points={
        "console_scripts": [
            "videoreader=videoreader.main:main",
            "videoreader-gui=videoreader.app:main",
            "videoreader-cli=videoreader.cli:main",
        ],
    },
    
    # 分类信息
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "Intended Audience :: Developers",
        "Topic :: Multimedia :: Video",
        "Topic :: Text Processing",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Environment :: X11 Applications :: Qt",
        "Environment :: Console",
    ],
    
    # 关键词
    keywords=[
        "video", "audio", "speech-recognition", "text-processing",
        "multimedia", "transcription", "subtitles", "gui", "cli"
    ],
    
    # 许可证
    license="MIT",
    
    # 最小Python版本
    python_requires=">=3.8",
    
    # 平台支持
    platforms=["any"],
    
    # 项目状态
    zip_safe=False,
)
