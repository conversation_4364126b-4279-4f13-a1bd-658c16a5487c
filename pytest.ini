[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# Python文件模式
python_files = test_*.py *_test.py

# Python类模式
python_classes = Test*

# Python函数模式
python_functions = test_*

# 最小版本要求
minversion = 6.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    -ra

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试（可能需要较长时间）
    gui: GUI相关测试
    cli: CLI相关测试
    network: 需要网络连接的测试
    external: 需要外部依赖的测试

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 日志配置
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = tests/logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s (%(filename)s:%(lineno)d)
log_file_date_format = %Y-%m-%d %H:%M:%S

# 测试发现
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    htmlcov
    .coverage

# 并行测试配置（需要pytest-xdist）
# -n auto 会自动检测CPU核心数
# 可以通过 -n 4 指定具体的进程数

# 覆盖率配置（需要pytest-cov）
# --cov=src 指定覆盖率检查的源码目录
# --cov-report=html 生成HTML格式的覆盖率报告
# --cov-report=term-missing 在终端显示缺失的行号

# HTML报告配置（需要pytest-html）
# --html=reports/report.html 生成HTML测试报告
# --self-contained-html 生成自包含的HTML文件
