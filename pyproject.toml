[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "videoreader"
version = "1.0.0"
description = "将视频转换为可阅读文本和图片格式的应用程序"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "VideoReader Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "VideoReader Team", email = "<EMAIL>"}
]
keywords = [
    "video", "audio", "speech-recognition", "text-processing",
    "multimedia", "transcription", "subtitles", "gui", "cli"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "Intended Audience :: Developers",
    "Topic :: Multimedia :: Video",
    "Topic :: Text Processing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Operating System :: OS Independent",
    "Environment :: X11 Applications :: Qt",
    "Environment :: Console",
]
requires-python = ">=3.8"
dependencies = [
    "numpy>=1.21.0",
    "opencv-python>=4.5.0",
    "librosa>=0.9.0",
    "soundfile>=0.10.0",
    "openai-whisper>=20230314",
    "Pillow>=9.0.0",
    "pandas>=1.3.0",
    "PyYAML>=6.0",
    "colorlog>=6.0.0",
    "tqdm>=4.64.0",
    "click>=8.0.0",
    "requests>=2.28.0",
    "ffmpeg-python>=0.2.0",
    "python-docx>=0.8.11",
    "scipy>=1.7.0",
    "python-dateutil>=2.8.0",
    "psutil>=5.8.0",
    "jsonschema>=4.0.0",
    "rich>=12.0.0",
]

[project.optional-dependencies]
gui = [
    "PySide6>=6.0.0",
    "qt-material>=2.14",
    "qdarkstyle>=3.0.0",
]
ml = [
    "torch>=1.9.0",
    "transformers>=4.0.0",
    "scikit-learn>=1.0.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=2.5.0",
    "pytest-mock>=3.8.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
    "pre-commit>=2.20.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=2.5.0",
    "pytest-mock>=3.8.0",
    "factory-boy>=3.2.0",
    "faker>=15.0.0",
]

[project.scripts]
videoreader = "videoreader.main:main"
videoreader-gui = "videoreader.app:main"
videoreader-cli = "videoreader.cli:main"

[project.urls]
Homepage = "https://github.com/your-org/videoreader"
Documentation = "https://videoreader.readthedocs.io/"
Repository = "https://github.com/your-org/videoreader"
"Bug Tracker" = "https://github.com/your-org/videoreader/issues"
Changelog = "https://github.com/your-org/videoreader/blob/main/CHANGELOG.md"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
videoreader = [
    "ui/styles/*.qss",
    "ui/icons/*.png",
    "ui/icons/*.ico",
    "common/config/*.json",
    "common/config/*.yaml",
]

# Black 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort 导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["videoreader"]
known_third_party = ["numpy", "opencv", "PySide6", "pytest"]

# MyPy 类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "librosa.*",
    "whisper.*",
    "PySide6.*",
    "ffmpeg.*",
]
ignore_missing_imports = true

# Pytest 配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--tb=short",
    "-ra",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: 单元测试",
    "integration: 集成测试",
    "slow: 慢速测试",
    "gui: GUI测试",
    "cli: CLI测试",
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage 配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

# Flake8 配置（在 setup.cfg 或 .flake8 中配置）

# Bandit 安全检查配置
[tool.bandit]
exclude_dirs = ["tests", "venv", "env"]
skips = ["B101", "B601"]

# Pylint 配置
[tool.pylint.messages_control]
disable = [
    "C0330",  # Wrong hanging indentation
    "C0326",  # Bad whitespace
    "R0903",  # Too few public methods
    "R0913",  # Too many arguments
    "W0613",  # Unused argument
]

[tool.pylint.format]
max-line-length = "88"

# Commitizen 配置
[tool.commitizen]
name = "cz_conventional_commits"
version = "1.0.0"
tag_format = "v$version"
version_files = [
    "src/videoreader/__init__.py",
    "pyproject.toml:version"
]

# Bumpversion 配置
[tool.bumpversion]
current_version = "1.0.0"
parse = "(?P<major>\\d+)\\.(?P<minor>\\d+)\\.(?P<patch>\\d+)"
serialize = ["{major}.{minor}.{patch}"]
search = "{current_version}"
replace = "{new_version}"
commit = true
tag = true

[[tool.bumpversion.files]]
filename = "pyproject.toml"
search = "version = \"{current_version}\""
replace = "version = \"{new_version}\""

[[tool.bumpversion.files]]
filename = "src/videoreader/__init__.py"
search = "__version__ = \"{current_version}\""
replace = "__version__ = \"{new_version}\""
