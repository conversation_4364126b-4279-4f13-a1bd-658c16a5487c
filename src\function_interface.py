"""功能接口层 - 整个应用的中央控制器

负责协调各个模块之间的交互，管理应用状态，处理业务流程。
界面层和命令行都通过这个接口层来调用业务功能。
"""

import threading
import time
from typing import Dict, Any, Optional, Callable, List
from concurrent.futures import ThreadPoolExecutor, Future
from pathlib import Path

# 使用绝对导入
from core.models import (
    ApplicationState, VideoInfo, ProcessResult, ProcessConfig, ParserType,
    SegmentInfo, SearchQuery, SearchResult, ExportOptions, ExportFormat
)
from core.events import EventBus, EventType, publish_event
from modules.video_processor import create_video_processor
from modules.storage_engine import create_metadata_manager
from modules.search_engine import create_searcher
from ui import create_main_window, UIEventType
from common.exceptions import (
    VideoReaderError, VideoLoadError, ProcessingError,
    StorageError, SearchIndexError
)
from common.logging import get_logger
from common.config import get_config


logger = get_logger(__name__)


class FunctionInterface:
    """功能接口类 - VideoReader应用的中央控制器
    
    提供线程安全的异步接口，供界面层和命令行调用。
    负责协调各个模块之间的交互，管理应用状态。
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 初始化各个模块
        self._video_processor = None
        self._metadata_manager = None
        self._searcher = None
        self._ui_manager = None
        
        # 事件总线和应用状态
        self.event_bus = EventBus()
        self.app_state = ApplicationState()
        
        # 线程管理
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.current_task: Optional[Future] = None
        self.cancel_event = threading.Event()
        
        # 回调函数
        self.progress_callback: Optional[Callable[[float, str], None]] = None
        self.error_callback: Optional[Callable[[str, str], None]] = None
        
        # 初始化标志
        self._initialized = False
        self._lock = threading.RLock()
        
        self.logger.info("功能接口层初始化完成")
    
    def initialize(self, mode: str = "gui") -> bool:
        """初始化应用
        
        Args:
            mode: 启动模式 ("gui" 或 "cli")
            
        Returns:
            bool: 是否初始化成功
        """
        with self._lock:
            if self._initialized:
                return True
            
            try:
                # 延迟初始化各个模块
                self._video_processor = create_video_processor()
                self._metadata_manager = create_metadata_manager()
                self._searcher = create_searcher()
                
                if mode == "gui":
                    self._ui_manager = create_main_window()
                    self._ui_manager.initialize()
                    self._setup_ui_event_handlers()
                
                # 注册事件处理器
                self._setup_event_handlers()
                
                self._initialized = True
                self.logger.info(f"应用初始化完成，模式: {mode}")
                
                # 发布应用启动事件
                publish_event(EventType.APP_STARTED, {'mode': mode})
                
                return True
                
            except Exception as e:
                self.logger.error(f"应用初始化失败: {e}")
                return False
    
    def start_application(self, mode: str = "gui") -> None:
        """启动应用
        
        Args:
            mode: 启动模式 ("gui" 或 "cli")
        """
        if not self.initialize(mode):
            raise VideoReaderError("应用初始化失败")
        
        if mode == "gui" and self._ui_manager:
            self._ui_manager.show_main_window()
        elif mode == "cli":
            self.logger.info("CLI模式启动完成")
    
    def shutdown_application(self) -> None:
        """关闭应用"""
        try:
            print("开始关闭功能接口")

            # 取消当前任务
            if self.current_task and not self.current_task.done():
                self.cancel_event.set()
                self.current_task.cancel()

            # 关闭线程池
            self.executor.shutdown(wait=True)

            # 发布应用关闭事件
            try:
                publish_event(EventType.APP_SHUTDOWN, {})
            except:
                pass  # 忽略事件发布失败

            print("功能接口关闭完成")

        except Exception as e:
            print(f"功能接口关闭失败: {e}")
    
    def load_video(self, file_path: str) -> bool:
        """加载视频文件
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            self.logger.info(f"开始加载视频: {file_path}")
            
            # 验证文件路径
            if not Path(file_path).exists():
                raise VideoLoadError(f"视频文件不存在: {file_path}")
            
            # 使用视频预处理模块获取视频信息
            video_info = self._video_processor.get_video_info(file_path)
            self.app_state.current_video = video_info
            
            # 添加到最近文件列表
            self.app_state.add_recent_file(file_path)
            
            # 尝试加载已有的元数据
            metadata = self._metadata_manager.find_metadata_by_video(file_path)
            if metadata:
                self.app_state.current_process_result = metadata.process_result
                self.logger.info("找到已有的处理结果")
                
                # 构建搜索索引
                if metadata.process_result.segments:
                    self._build_search_index(metadata.process_result.segments)
            
            # 发送视频加载完成事件
            publish_event(EventType.VIDEO_LOADED, {
                'video_info': video_info,
                'has_metadata': metadata is not None
            })
            
            self.logger.info(f"视频加载成功: {Path(file_path).name}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载视频失败: {e}")
            if self.error_callback:
                self.error_callback("加载视频失败", str(e))
            return False
    
    def process_video(self, parser_type: ParserType, config: Dict[str, Any]) -> bool:
        """处理视频（完整的预处理流程）
        
        Args:
            parser_type: 解析器类型
            config: 处理配置
            
        Returns:
            bool: 是否开始处理成功
        """
        if not self.app_state.current_video:
            self.logger.error("未加载视频文件")
            return False
        
        if self.current_task and not self.current_task.done():
            self.logger.warning("已有处理任务在进行中")
            return False
        
        try:
            # 创建处理配置
            process_config = ProcessConfig(
                parser_type=parser_type,
                parser_config=config,
                language=get_config('audio.speech_engines.whisper.language', 'zh'),
                speech_engine=get_config('audio.speech_engines.whisper.model', 'whisper'),
                output_dir=get_config('paths.output_dir')
            )
            
            # 验证配置
            if not self._video_processor.validate_config(process_config):
                raise ProcessingError("无效的处理配置")
            
            # 重置取消标志
            self.cancel_event.clear()
            
            # 提交异步任务
            self.current_task = self.executor.submit(
                self._process_video_async,
                self.app_state.current_video.file_path,
                process_config
            )
            
            # 发送处理开始事件
            publish_event(EventType.VIDEO_PROCESSING_STARTED, {
                'parser_type': parser_type.value,
                'config': config
            })
            
            self.logger.info(f"开始处理视频: {parser_type.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动视频处理失败: {e}")
            if self.error_callback:
                self.error_callback("启动视频处理失败", str(e))
            return False
    
    def _process_video_async(self, video_path: str, config: ProcessConfig) -> ProcessResult:
        """异步处理视频"""
        try:
            # 更新应用状态
            from core.models import ProcessingStatus
            self.app_state.processing_status = ProcessingStatus.PROCESSING
            
            # 定义进度回调
            def progress_callback(progress: float, message: str):
                if self.progress_callback:
                    self.progress_callback(progress, message)
                
                # 发布进度事件
                publish_event(EventType.VIDEO_PROCESSING_PROGRESS, {
                    'progress': progress,
                    'message': message
                })
            
            # 执行处理
            process_result = self._video_processor.process_video(
                video_path, config, progress_callback
            )
            
            # 检查是否被取消
            if self.cancel_event.is_set():
                raise ProcessingError("处理被取消")
            
            # 更新应用状态
            self.app_state.current_process_result = process_result
            self.app_state.processing_status = ProcessingStatus.COMPLETED
            
            # 构建搜索索引
            self._build_search_index(process_result.segments)
            
            # 保存元数据
            self._save_metadata(process_result)
            
            # 发送处理完成事件
            publish_event(EventType.VIDEO_PROCESSING_COMPLETED, {
                'process_result': process_result
            })
            
            self.logger.info("视频处理完成")
            return process_result
            
        except Exception as e:
            self.app_state.processing_status = ProcessingStatus.FAILED
            
            # 发送处理失败事件
            publish_event(EventType.VIDEO_PROCESSING_FAILED, {
                'error': str(e)
            })
            
            self.logger.error(f"视频处理失败: {e}")
            raise
    
    def cancel_processing(self) -> bool:
        """取消当前处理
        
        Returns:
            bool: 是否成功取消
        """
        try:
            # 设置取消标志
            self.cancel_event.set()
            
            # 取消视频处理器的处理
            if self._video_processor:
                self._video_processor.cancel_processing()
            
            # 取消异步任务
            if self.current_task and not self.current_task.done():
                self.current_task.cancel()
            
            # 更新状态
            from core.models import ProcessingStatus
            self.app_state.processing_status = ProcessingStatus.CANCELLED
            
            # 发送取消事件
            publish_event(EventType.VIDEO_PROCESSING_CANCELLED, {})
            
            self.logger.info("处理已取消")
            return True
            
        except Exception as e:
            self.logger.error(f"取消处理失败: {e}")
            return False
    
    def search_segments(self, query: SearchQuery) -> List[SearchResult]:
        """搜索段落
        
        Args:
            query: 搜索查询
            
        Returns:
            List[SearchResult]: 搜索结果列表
        """
        try:
            if not self.app_state.current_process_result:
                return []
            
            # 执行搜索
            results = self._searcher.search_full_text(query.query, query.max_results)
            
            # 更新应用状态
            self.app_state.last_search_query = query
            self.app_state.last_search_results = results
            
            # 发送搜索完成事件
            publish_event(EventType.SEARCH_COMPLETED, {
                'query': query.query,
                'results_count': len(results)
            })
            
            self.logger.debug(f"搜索完成: '{query.query}', 结果数: {len(results)}")
            return results
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            return []
    
    def export_data(self, format: ExportFormat, output_path: str, 
                   options: Dict[str, Any] = None) -> bool:
        """导出数据
        
        Args:
            format: 导出格式
            output_path: 输出路径
            options: 导出选项
            
        Returns:
            bool: 是否导出成功
        """
        try:
            if not self.app_state.current_process_result:
                self.logger.error("没有可导出的数据")
                return False
            
            # 创建导出选项
            export_options = ExportOptions(
                format=format,
                output_path=output_path,
                **(options or {})
            )
            
            # 验证导出选项
            if not export_options.validate():
                raise ValueError("无效的导出选项")
            
            # 发送导出开始事件
            publish_event(EventType.EXPORT_STARTED, {
                'format': format.value,
                'output_path': output_path
            })
            
            # 这里应该调用存储引擎的导出功能
            # 暂时使用简化实现
            success = self._export_data_simple(export_options)
            
            if success:
                # 发送导出完成事件
                publish_event(EventType.EXPORT_COMPLETED, {
                    'format': format.value,
                    'output_path': output_path
                })
                
                self.logger.info(f"数据导出成功: {output_path}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            if self.error_callback:
                self.error_callback("导出数据失败", str(e))
            return False
    
    def get_processing_progress(self) -> tuple:
        """获取处理进度
        
        Returns:
            tuple: (progress: float, message: str)
        """
        if self._video_processor:
            return self._video_processor.get_processing_progress()
        return (0.0, "就绪")
    
    def is_processing(self) -> bool:
        """检查是否正在处理
        
        Returns:
            bool: 是否正在处理
        """
        return (self.current_task and not self.current_task.done()) or \
               (self._video_processor and self._video_processor.is_processing())
    
    def get_recent_files(self) -> List[str]:
        """获取最近打开的文件列表
        
        Returns:
            List[str]: 最近文件路径列表
        """
        return self.app_state.recent_files.copy()
    
    def _build_search_index(self, segments: List[SegmentInfo]) -> None:
        """构建搜索索引"""
        try:
            # 这里需要实现搜索索引的构建
            # 暂时使用简化实现
            self.logger.info(f"构建搜索索引: {len(segments)} 个段落")
            
        except Exception as e:
            self.logger.warning(f"构建搜索索引失败: {e}")
    
    def _save_metadata(self, process_result: ProcessResult) -> None:
        """保存元数据"""
        try:
            if not self.app_state.current_video:
                return
            
            metadata = self._metadata_manager.create_metadata(
                self.app_state.current_video.file_path,
                process_result
            )
            
            self._metadata_manager.save_metadata(metadata)
            
            # 发送元数据保存事件
            publish_event(EventType.METADATA_SAVED, {
                'video_path': self.app_state.current_video.file_path
            })
            
            self.logger.info("元数据保存成功")
            
        except Exception as e:
            self.logger.warning(f"保存元数据失败: {e}")
    
    def _export_data_simple(self, options: ExportOptions) -> bool:
        """简化的数据导出实现"""
        try:
            result = self.app_state.current_process_result
            if not result:
                return False
            
            output_path = Path(options.output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if options.format == ExportFormat.TXT:
                # 导出为文本文件
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"视频文件: {result.video_info.file_name}\n")
                    f.write(f"总时长: {result.video_info.duration:.2f}秒\n")
                    f.write(f"段落数量: {len(result.segments)}\n\n")
                    
                    for i, segment in enumerate(result.segments, 1):
                        f.write(f"段落 {i} ({segment.start_time:.2f}s - {segment.end_time:.2f}s):\n")
                        f.write(f"{segment.text}\n\n")
            
            elif options.format == ExportFormat.JSON:
                # 导出为JSON文件
                import json
                data = {
                    'video_info': {
                        'file_name': result.video_info.file_name,
                        'duration': result.video_info.duration,
                        'width': result.video_info.width,
                        'height': result.video_info.height
                    },
                    'segments': [
                        {
                            'id': seg.id,
                            'start_time': seg.start_time,
                            'end_time': seg.end_time,
                            'text': seg.text,
                            'summary': seg.summary
                        }
                        for seg in result.segments
                    ]
                }
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"简化导出失败: {e}")
            return False
    
    def _setup_event_handlers(self) -> None:
        """设置事件处理器"""
        # 这里可以注册全局事件处理器
        pass
    
    def _setup_ui_event_handlers(self) -> None:
        """设置UI事件处理器"""
        if not self._ui_manager:
            return
        
        # 注册UI事件处理器
        self._ui_manager.register_event_handler(
            UIEventType.FILE_OPEN_REQUESTED, self._on_file_open_requested
        )
        self._ui_manager.register_event_handler(
            UIEventType.PROCESS_START_REQUESTED, self._on_process_start_requested
        )
        self._ui_manager.register_event_handler(
            UIEventType.EXPORT_REQUESTED, self._on_export_requested
        )
        self._ui_manager.register_event_handler(
            UIEventType.SEARCH_REQUESTED, self._on_search_requested
        )
    
    def _on_file_open_requested(self, event) -> None:
        """处理文件打开请求"""
        file_path = event.get_data('file_path')
        if file_path:
            self.load_video(file_path)
    
    def _on_process_start_requested(self, event) -> None:
        """处理视频处理请求"""
        config = event.get_data('config', {})
        parser_type = ParserType(config.get('parser_type', 'scene_change'))
        self.process_video(parser_type, config)
    
    def _on_export_requested(self, event) -> None:
        """处理导出请求"""
        # 这里可以打开导出对话框
        pass
    
    def _on_search_requested(self, event) -> None:
        """处理搜索请求"""
        # 这里可以打开搜索对话框
        pass
