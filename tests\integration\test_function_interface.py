"""功能接口集成测试

测试FunctionInterface作为中央控制器的集成功能。
"""

import pytest
import time
from unittest.mock import Mock, patch
from pathlib import Path

from src.function_interface import FunctionInterface
from src.core.models import ParserType, ExportFormat, SearchQuery
from src.common.exceptions import VideoReaderError


@pytest.mark.integration
class TestFunctionInterface:
    """功能接口集成测试"""
    
    @pytest.fixture
    def function_interface(self):
        """功能接口fixture"""
        interface = FunctionInterface()
        # 使用模拟组件进行测试
        interface._video_processor = Mock()
        interface._metadata_manager = Mock()
        interface._searcher = Mock()
        return interface
    
    def test_initialization(self, function_interface):
        """测试初始化"""
        # 测试CLI模式初始化
        result = function_interface.initialize("cli")
        assert result == True
        assert function_interface._initialized == True
    
    def test_load_video_success(self, function_interface, mock_video_file, sample_video_info):
        """测试成功加载视频"""
        # 设置模拟返回值
        function_interface._video_processor.get_video_info.return_value = sample_video_info
        function_interface._metadata_manager.find_metadata_by_video.return_value = None
        
        # 初始化
        function_interface.initialize("cli")
        
        # 加载视频
        result = function_interface.load_video(str(mock_video_file))
        
        assert result == True
        assert function_interface.app_state.current_video == sample_video_info
        assert str(mock_video_file) in function_interface.app_state.recent_files
    
    def test_load_video_failure(self, function_interface):
        """测试加载视频失败"""
        function_interface.initialize("cli")
        
        # 尝试加载不存在的文件
        result = function_interface.load_video("/nonexistent/video.mp4")
        
        assert result == False
        assert function_interface.app_state.current_video is None
    
    def test_process_video_workflow(self, function_interface, mock_video_file, 
                                  sample_video_info, sample_process_result):
        """测试视频处理工作流"""
        # 设置模拟
        function_interface._video_processor.get_video_info.return_value = sample_video_info
        function_interface._video_processor.validate_config.return_value = True
        function_interface._video_processor.process_video.return_value = sample_process_result
        function_interface._video_processor.is_processing.return_value = False
        function_interface._metadata_manager.find_metadata_by_video.return_value = None
        function_interface._metadata_manager.create_metadata.return_value = Mock()
        function_interface._metadata_manager.save_metadata.return_value = True
        
        # 初始化并加载视频
        function_interface.initialize("cli")
        function_interface.load_video(str(mock_video_file))
        
        # 处理视频
        config = {"threshold": 0.3}
        result = function_interface.process_video(ParserType.SCENE_CHANGE, config)
        
        assert result == True
        
        # 等待处理完成（模拟）
        time.sleep(0.1)
        
        # 验证状态更新
        # 注意：由于是异步处理，这里可能需要等待或使用同步版本
    
    def test_cancel_processing(self, function_interface, mock_video_file, sample_video_info):
        """测试取消处理"""
        # 设置模拟
        function_interface._video_processor.get_video_info.return_value = sample_video_info
        function_interface._video_processor.validate_config.return_value = True
        function_interface._video_processor.cancel_processing.return_value = True
        function_interface._video_processor.is_processing.return_value = True
        function_interface._metadata_manager.find_metadata_by_video.return_value = None
        
        # 初始化并加载视频
        function_interface.initialize("cli")
        function_interface.load_video(str(mock_video_file))
        
        # 开始处理
        config = {"threshold": 0.3}
        function_interface.process_video(ParserType.SCENE_CHANGE, config)
        
        # 取消处理
        result = function_interface.cancel_processing()
        assert result == True
    
    def test_search_segments(self, function_interface, sample_process_result):
        """测试段落搜索"""
        from src.core.models import SearchResult
        
        # 设置模拟搜索结果
        mock_results = [
            SearchResult(
                segment=sample_process_result.segments[0],
                matches=[],
                relevance_score=0.8
            )
        ]
        function_interface._searcher.search_full_text.return_value = mock_results
        
        # 设置应用状态
        function_interface.initialize("cli")
        function_interface.app_state.current_process_result = sample_process_result
        
        # 执行搜索
        query = SearchQuery(query="测试", max_results=10)
        results = function_interface.search_segments(query)
        
        assert len(results) == 1
        assert results[0].relevance_score == 0.8
        assert function_interface.app_state.last_search_query == query
        assert function_interface.app_state.last_search_results == results
    
    def test_export_data(self, function_interface, sample_process_result, temp_dir):
        """测试数据导出"""
        # 设置应用状态
        function_interface.initialize("cli")
        function_interface.app_state.current_process_result = sample_process_result
        
        # 导出为文本文件
        output_path = temp_dir / "export_test.txt"
        result = function_interface.export_data(
            ExportFormat.TXT, 
            str(output_path)
        )
        
        assert result == True
        assert output_path.exists()
        
        # 验证导出内容
        content = output_path.read_text(encoding='utf-8')
        assert "视频文件:" in content
        assert "段落数量:" in content
    
    def test_export_data_json(self, function_interface, sample_process_result, temp_dir):
        """测试JSON格式导出"""
        import json
        
        # 设置应用状态
        function_interface.initialize("cli")
        function_interface.app_state.current_process_result = sample_process_result
        
        # 导出为JSON文件
        output_path = temp_dir / "export_test.json"
        result = function_interface.export_data(
            ExportFormat.JSON,
            str(output_path)
        )
        
        assert result == True
        assert output_path.exists()
        
        # 验证JSON内容
        with open(output_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert 'video_info' in data
        assert 'segments' in data
        assert len(data['segments']) == len(sample_process_result.segments)
    
    def test_get_processing_progress(self, function_interface):
        """测试获取处理进度"""
        function_interface._video_processor.get_processing_progress.return_value = (0.5, "处理中")
        
        function_interface.initialize("cli")
        
        progress, message = function_interface.get_processing_progress()
        assert progress == 0.5
        assert message == "处理中"
    
    def test_is_processing(self, function_interface):
        """测试处理状态检查"""
        function_interface._video_processor.is_processing.return_value = True
        
        function_interface.initialize("cli")
        
        assert function_interface.is_processing() == True
    
    def test_get_recent_files(self, function_interface, mock_video_file, sample_video_info):
        """测试获取最近文件"""
        function_interface._video_processor.get_video_info.return_value = sample_video_info
        function_interface._metadata_manager.find_metadata_by_video.return_value = None
        
        function_interface.initialize("cli")
        
        # 加载几个文件
        function_interface.load_video(str(mock_video_file))
        
        recent_files = function_interface.get_recent_files()
        assert len(recent_files) == 1
        assert str(mock_video_file) in recent_files
    
    def test_error_handling(self, function_interface):
        """测试错误处理"""
        function_interface.initialize("cli")
        
        # 测试加载不存在文件的错误处理
        error_occurred = False
        error_message = ""
        
        def error_callback(message, details):
            nonlocal error_occurred, error_message
            error_occurred = True
            error_message = message
        
        function_interface.error_callback = error_callback
        
        # 尝试加载不存在的文件
        result = function_interface.load_video("/nonexistent/file.mp4")
        
        assert result == False
        # 注意：错误回调可能不会在这个简单测试中触发，取决于具体实现
    
    def test_shutdown(self, function_interface):
        """测试应用关闭"""
        function_interface.initialize("cli")
        
        # 关闭应用
        function_interface.shutdown_application()
        
        # 验证线程池已关闭
        assert function_interface.executor._shutdown == True


@pytest.mark.integration
class TestFunctionInterfaceWithRealComponents:
    """使用真实组件的功能接口测试"""
    
    def test_real_component_initialization(self):
        """测试真实组件初始化"""
        interface = FunctionInterface()
        
        # 这个测试可能会失败，因为真实组件可能有依赖
        try:
            result = interface.initialize("cli")
            # 如果成功初始化，验证组件存在
            if result:
                assert interface._video_processor is not None
                assert interface._metadata_manager is not None
                assert interface._searcher is not None
        except Exception as e:
            # 如果初始化失败，记录原因
            pytest.skip(f"真实组件初始化失败: {e}")


@pytest.mark.integration
@pytest.mark.slow
class TestAsyncOperations:
    """异步操作测试"""
    
    def test_async_video_processing(self, function_interface, mock_video_file, 
                                  sample_video_info, sample_process_result):
        """测试异步视频处理"""
        # 设置模拟，使处理需要一些时间
        def slow_process(*args, **kwargs):
            time.sleep(0.1)  # 模拟处理时间
            return sample_process_result
        
        function_interface._video_processor.get_video_info.return_value = sample_video_info
        function_interface._video_processor.validate_config.return_value = True
        function_interface._video_processor.process_video.side_effect = slow_process
        function_interface._metadata_manager.find_metadata_by_video.return_value = None
        function_interface._metadata_manager.create_metadata.return_value = Mock()
        function_interface._metadata_manager.save_metadata.return_value = True
        
        # 初始化并加载视频
        function_interface.initialize("cli")
        function_interface.load_video(str(mock_video_file))
        
        # 开始异步处理
        config = {"threshold": 0.3}
        result = function_interface.process_video(ParserType.SCENE_CHANGE, config)
        
        assert result == True
        
        # 验证处理状态
        assert function_interface.is_processing() == True
        
        # 等待处理完成
        max_wait = 5  # 最多等待5秒
        wait_time = 0
        while function_interface.is_processing() and wait_time < max_wait:
            time.sleep(0.1)
            wait_time += 0.1
        
        # 验证处理完成
        assert function_interface.is_processing() == False
