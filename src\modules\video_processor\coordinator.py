"""视频预处理协调器

负责协调视频处理、音频处理和解析三个子引擎的工作流程。
"""

import time
import threading
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path

from .interface import VideoProcessorInterface
from .models import ProcessingStage, ProcessingProgress, ProcessingStatistics
try:
    from ...core.models import VideoInfo, ProcessResult, ProcessConfig, ParserType, SegmentInfo
    from ...common.exceptions import (
        VideoLoadError, ProcessingError, ProcessCancelledError,
        UnsupportedFormatError, InvalidConfigError
    )
    from ...common.logging import get_logger
    from ...common.utils import timing_decorator, format_duration
except ImportError:
    from core.models import VideoInfo, ProcessResult, ProcessConfig, ParserType, SegmentInfo
    from common.exceptions import (
        VideoLoadError, ProcessingError, ProcessCancelledError,
        UnsupportedFormatError, InvalidConfigError
    )
    from common.logging import get_logger
    from common.utils import timing_decorator, format_duration


logger = get_logger(__name__)


class VideoProcessorCoordinator(VideoProcessorInterface):
    """视频预处理协调器实现
    
    协调视频引擎、音频引擎和解析引擎的工作，提供统一的处理接口。
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 子引擎实例（延迟初始化）
        self._video_engine = None
        self._audio_engine = None
        self._parser_factory = None
        
        # 处理状态
        self._is_processing = False
        self._should_cancel = False
        self._current_progress = ProcessingProgress(
            stage=ProcessingStage.INITIALIZING,
            progress=0.0,
            message="就绪"
        )
        self._processing_lock = threading.RLock()
        
        # 统计信息
        self._statistics = None
        
        self.logger.info("视频预处理协调器初始化完成")
    
    def _initialize_engines(self):
        """延迟初始化子引擎"""
        if self._video_engine is None:
            try:
                from .video_engine.processor import VideoEngineImpl
                self._video_engine = VideoEngineImpl()
                self.logger.debug("视频引擎初始化完成")
            except ImportError as e:
                self.logger.error(f"视频引擎初始化失败: {e}")
                raise ProcessingError(f"视频引擎初始化失败: {e}")
        
        if self._audio_engine is None:
            try:
                from .audio_engine.processor import AudioEngineImpl
                self._audio_engine = AudioEngineImpl()
                self.logger.debug("音频引擎初始化完成")
            except ImportError as e:
                self.logger.error(f"音频引擎初始化失败: {e}")
                raise ProcessingError(f"音频引擎初始化失败: {e}")
        
        if self._parser_factory is None:
            try:
                from .parser_engine.factory import ParserFactory
                self._parser_factory = ParserFactory()
                self.logger.debug("解析器工厂初始化完成")
            except ImportError as e:
                self.logger.error(f"解析器工厂初始化失败: {e}")
                raise ProcessingError(f"解析器工厂初始化失败: {e}")
    
    @timing_decorator
    def process_video(self, video_path: str, config: ProcessConfig,
                     progress_callback: Optional[Callable[[float, str], None]] = None) -> ProcessResult:
        """处理视频文件的完整流程"""
        with self._processing_lock:
            if self._is_processing:
                raise ProcessingError("已有处理任务在进行中")
            
            self._is_processing = True
            self._should_cancel = False
        
        start_time = time.time()
        
        try:
            # 初始化引擎
            self._initialize_engines()
            
            # 验证输入
            self._validate_inputs(video_path, config)
            
            # 初始化统计信息
            self._statistics = ProcessingStatistics(
                total_processing_time=0.0,
                video_loading_time=0.0,
                audio_extraction_time=0.0,
                speech_recognition_time=0.0,
                parsing_time=0.0,
                thumbnail_generation_time=0.0,
                total_segments=0,
                total_frames_processed=0,
                total_audio_duration=0.0,
                memory_usage_peak=0.0
            )
            
            # 执行处理流程
            result = self._execute_processing_pipeline(video_path, config, progress_callback)
            
            # 完成处理
            end_time = time.time()
            self._statistics.total_processing_time = end_time - start_time
            
            self._update_progress(ProcessingStage.COMPLETED, 1.0, "处理完成", progress_callback)
            
            self.logger.info(f"视频处理完成: {video_path}, 耗时: {format_duration(self._statistics.total_processing_time)}")
            
            return result
            
        except ProcessCancelledError:
            self.logger.info(f"视频处理被取消: {video_path}")
            raise
        except Exception as e:
            self.logger.error(f"视频处理失败: {e}")
            self._update_progress(ProcessingStage.FAILED, 0.0, f"处理失败: {str(e)}", progress_callback)
            raise ProcessingError(f"视频处理失败: {e}")
        finally:
            with self._processing_lock:
                self._is_processing = False
    
    def _validate_inputs(self, video_path: str, config: ProcessConfig):
        """验证输入参数"""
        # 检查视频文件
        video_file = Path(video_path)
        if not video_file.exists():
            raise VideoLoadError(f"视频文件不存在: {video_path}")
        
        if not video_file.is_file():
            raise VideoLoadError(f"路径不是文件: {video_path}")
        
        # 检查文件格式
        supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
        if video_file.suffix.lower() not in supported_formats:
            raise UnsupportedFormatError(f"不支持的视频格式: {video_file.suffix}")
        
        # 验证配置
        if not self.validate_config(config):
            raise InvalidConfigError("无效的处理配置")
    
    def _execute_processing_pipeline(self, video_path: str, config: ProcessConfig,
                                   progress_callback: Optional[Callable[[float, str], None]]) -> ProcessResult:
        """执行处理管道"""
        
        # 1. 加载视频信息 (10%)
        self._update_progress(ProcessingStage.LOADING_VIDEO, 0.1, "正在加载视频信息...", progress_callback)
        self._check_cancellation()
        
        stage_start = time.time()
        video_info = self._video_engine.load_video(video_path)
        self._statistics.video_loading_time = time.time() - stage_start
        
        # 2. 提取音频 (20%)
        self._update_progress(ProcessingStage.EXTRACTING_AUDIO, 0.2, "正在提取音频...", progress_callback)
        self._check_cancellation()
        
        stage_start = time.time()
        audio_path = self._audio_engine.extract_audio(video_path)
        self._statistics.audio_extraction_time = time.time() - stage_start
        
        # 3. 语音识别 (50%)
        self._update_progress(ProcessingStage.RECOGNIZING_SPEECH, 0.3, "正在进行语音识别...", progress_callback)
        self._check_cancellation()
        
        stage_start = time.time()
        transcript = self._audio_engine.recognize_speech(
            audio_path, config.language, config.speech_engine
        )
        self._statistics.speech_recognition_time = time.time() - stage_start
        self._statistics.total_audio_duration = video_info.duration
        
        # 4. 解析生成段落 (30%)
        self._update_progress(ProcessingStage.PARSING_SEGMENTS, 0.6, "正在解析视频段落...", progress_callback)
        self._check_cancellation()
        
        stage_start = time.time()
        parser = self._parser_factory.create_parser(config.parser_type)
        segments = parser.parse(video_info, transcript, config.parser_config)
        self._statistics.parsing_time = time.time() - stage_start
        self._statistics.total_segments = len(segments)
        
        # 5. 生成缩略图 (10%)
        if config.generate_thumbnails:
            self._update_progress(ProcessingStage.GENERATING_THUMBNAILS, 0.9, "正在生成缩略图...", progress_callback)
            self._check_cancellation()
            
            stage_start = time.time()
            self._generate_thumbnails(video_info, segments, config.output_dir)
            self._statistics.thumbnail_generation_time = time.time() - stage_start
        
        # 6. 完成处理
        self._update_progress(ProcessingStage.FINALIZING, 0.95, "正在完成处理...", progress_callback)
        
        return ProcessResult(
            video_info=video_info,
            segments=segments,
            parser_type=config.parser_type,
            parser_config=config.parser_config,
            processing_time=self._statistics.total_processing_time,
            transcript_path=audio_path,
            total_segments=len(segments)
        )
    
    def _generate_thumbnails(self, video_info: VideoInfo, segments: List[SegmentInfo], 
                           output_dir: Optional[str]):
        """为每个段落生成缩略图"""
        if not output_dir:
            output_dir = Path(video_info.file_path).parent / "thumbnails"
        else:
            output_dir = Path(output_dir) / "thumbnails"
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for i, segment in enumerate(segments):
            try:
                # 选择段落中间时间点作为缩略图时间
                thumbnail_time = (segment.start_time + segment.end_time) / 2
                
                # 提取帧
                frame = self._video_engine.extract_frame(video_info.file_path, thumbnail_time, (320, 240))
                
                # 保存缩略图
                thumbnail_path = output_dir / f"segment_{i:04d}.jpg"
                try:
                    from ...common.utils import save_image
                except ImportError:
                    from common.utils import save_image
                save_image(frame, thumbnail_path)
                
                # 更新段落信息
                segment.thumbnail_path = str(thumbnail_path)
                
            except Exception as e:
                self.logger.warning(f"生成段落 {i} 的缩略图失败: {e}")
    
    def _update_progress(self, stage: ProcessingStage, progress: float, message: str,
                        callback: Optional[Callable[[float, str], None]]):
        """更新处理进度"""
        self._current_progress = ProcessingProgress(
            stage=stage,
            progress=progress,
            message=message
        )
        
        if callback:
            try:
                callback(progress, message)
            except Exception as e:
                self.logger.warning(f"进度回调执行失败: {e}")
    
    def _check_cancellation(self):
        """检查是否需要取消处理"""
        if self._should_cancel:
            raise ProcessCancelledError("处理已被取消")
    
    def get_video_info(self, video_path: str) -> VideoInfo:
        """获取视频信息"""
        self._initialize_engines()
        return self._video_engine.load_video(video_path)
    
    def extract_key_frame(self, video_path: str, timestamp: float,
                         size: Optional[tuple] = None) -> 'np.ndarray':
        """提取关键帧"""
        self._initialize_engines()
        return self._video_engine.extract_frame(video_path, timestamp, size)
    
    def get_available_parsers(self) -> List[ParserType]:
        """获取可用的解析器"""
        return [ParserType.SCENE_CHANGE, ParserType.TEXT_LENGTH, 
                ParserType.TIME_FIXED, ParserType.SILENCE_BASED]
    
    def get_parser_default_config(self, parser_type: ParserType) -> Dict[str, Any]:
        """获取解析器默认配置"""
        self._initialize_engines()
        parser = self._parser_factory.create_parser(parser_type)
        return parser.get_default_config()
    
    def validate_config(self, config: ProcessConfig) -> bool:
        """验证配置"""
        return config.validate()
    
    def estimate_processing_time(self, video_path: str, config: ProcessConfig) -> float:
        """估算处理时间（秒）"""
        try:
            video_info = self.get_video_info(video_path)
            # 简单估算：视频时长的2-5倍
            base_time = video_info.duration * 3
            
            # 根据解析器类型调整
            if config.parser_type == ParserType.SCENE_CHANGE:
                base_time *= 1.5  # 场景分析需要更多时间
            
            return base_time
        except Exception:
            return 300.0  # 默认5分钟
    
    def cancel_processing(self) -> bool:
        """取消当前处理"""
        with self._processing_lock:
            if self._is_processing:
                self._should_cancel = True
                self.logger.info("处理取消请求已发送")
                return True
            return False
    
    def is_processing(self) -> bool:
        """检查是否正在处理"""
        with self._processing_lock:
            return self._is_processing
    
    def get_processing_progress(self) -> tuple:
        """获取处理进度"""
        return (self._current_progress.progress, self._current_progress.message)
