"""解析器基类

定义所有解析器的通用接口和基础功能。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import time

from ..interface import ParserEngineInterface
from ....core.models import VideoInfo, SegmentInfo, ParserType
from ..audio_engine.models import TranscriptSegment
from .models import ParserConfig, ParseResult, DEFAULT_CONFIGS
from ....common.logging import get_logger
from ....common.exceptions import ProcessingError


class BaseParser(ParserEngineInterface):
    """解析器基类
    
    提供所有解析器的通用功能和接口定义。
    """
    
    def __init__(self, parser_type: ParserType):
        self.parser_type = parser_type
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self._is_processing = False
        self._should_cancel = False
    
    @abstractmethod
    def get_parser_type(self) -> ParserType:
        """获取解析器类型"""
        return self.parser_type
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return DEFAULT_CONFIGS.get(self.parser_type, {}).copy()
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        try:
            parser_config = ParserConfig(self.parser_type, config)
            return parser_config.validate()
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def parse(self, video_info: VideoInfo, transcript: List[TranscriptSegment],
             config: Dict[str, Any]) -> List[SegmentInfo]:
        """解析生成段落
        
        Args:
            video_info: 视频信息
            transcript: 转录文本段落
            config: 解析配置
            
        Returns:
            List[SegmentInfo]: 解析后的段落列表
            
        Raises:
            ProcessingError: 解析失败
        """
        try:
            self.logger.info(f"开始解析: {self.parser_type.value}")
            
            # 验证输入
            if not video_info:
                raise ProcessingError("视频信息不能为空")
            
            if not transcript:
                raise ProcessingError("转录文本不能为空")
            
            # 验证配置
            if not self.validate_config(config):
                raise ProcessingError("无效的解析配置")
            
            # 设置处理状态
            self._is_processing = True
            self._should_cancel = False
            
            start_time = time.time()
            
            # 执行具体的解析逻辑
            segments = self._do_parse(video_info, transcript, config)
            
            # 后处理
            segments = self._post_process_segments(segments, video_info, config)
            
            processing_time = time.time() - start_time
            
            self.logger.info(f"解析完成: {len(segments)} 个段落, 耗时 {processing_time:.2f}s")
            
            return segments
            
        except Exception as e:
            self.logger.error(f"解析失败: {e}")
            raise ProcessingError(f"解析失败: {e}")
        finally:
            self._is_processing = False
    
    @abstractmethod
    def _do_parse(self, video_info: VideoInfo, transcript: List[TranscriptSegment],
                 config: Dict[str, Any]) -> List[SegmentInfo]:
        """执行具体的解析逻辑（子类实现）"""
        pass
    
    def _post_process_segments(self, segments: List[SegmentInfo], 
                              video_info: VideoInfo, config: Dict[str, Any]) -> List[SegmentInfo]:
        """后处理段落"""
        if not segments:
            return segments
        
        # 确保段落ID连续
        for i, segment in enumerate(segments):
            segment.id = i
        
        # 确保时间范围有效
        for segment in segments:
            segment.start_time = max(0.0, segment.start_time)
            segment.end_time = min(video_info.duration, segment.end_time)
            segment.duration = segment.end_time - segment.start_time
        
        # 移除无效段落
        valid_segments = [seg for seg in segments if seg.duration > 0]
        
        # 按时间排序
        valid_segments.sort(key=lambda x: x.start_time)
        
        # 重新分配ID
        for i, segment in enumerate(valid_segments):
            segment.id = i
        
        self.logger.debug(f"后处理完成: {len(segments)} -> {len(valid_segments)} 个段落")
        
        return valid_segments
    
    def _merge_transcript_segments(self, transcript: List[TranscriptSegment],
                                  start_time: float, end_time: float) -> str:
        """合并指定时间范围内的转录文本"""
        merged_text = []
        
        for segment in transcript:
            # 检查段落是否与时间范围重叠
            if (segment.start_time < end_time and segment.end_time > start_time):
                merged_text.append(segment.text.strip())
        
        return ' '.join(merged_text).strip()
    
    def _find_transcript_segments_in_range(self, transcript: List[TranscriptSegment],
                                          start_time: float, end_time: float) -> List[TranscriptSegment]:
        """查找指定时间范围内的转录段落"""
        segments_in_range = []
        
        for segment in transcript:
            # 检查段落是否与时间范围重叠
            if (segment.start_time < end_time and segment.end_time > start_time):
                segments_in_range.append(segment)
        
        return segments_in_range
    
    def _generate_summary(self, text: str, max_length: int = 100) -> str:
        """生成文本摘要"""
        if not text:
            return ""
        
        # 简单的摘要生成：取前几句话
        sentences = []
        current_length = 0
        
        # 分句
        import re
        sentence_pattern = r'[。！？.!?]+'
        parts = re.split(sentence_pattern, text)
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            if current_length + len(part) <= max_length:
                sentences.append(part)
                current_length += len(part)
            else:
                break
        
        summary = '。'.join(sentences)
        if summary and not summary.endswith(('。', '！', '？', '.', '!', '?')):
            summary += '。'
        
        return summary or text[:max_length] + ('...' if len(text) > max_length else '')
    
    def _calculate_confidence(self, segments: List[TranscriptSegment]) -> float:
        """计算段落的置信度"""
        if not segments:
            return 0.0
        
        total_confidence = sum(seg.confidence for seg in segments)
        return total_confidence / len(segments)
    
    def cancel_processing(self) -> bool:
        """取消处理"""
        self._should_cancel = True
        return True
    
    def is_processing(self) -> bool:
        """检查是否正在处理"""
        return self._is_processing
    
    def should_cancel(self) -> bool:
        """检查是否应该取消"""
        return self._should_cancel
