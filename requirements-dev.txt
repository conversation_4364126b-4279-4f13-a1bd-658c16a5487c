# VideoReader 开发环境依赖

# 包含生产依赖
-r requirements.txt

# 测试框架
pytest>=7.0.0
pytest-cov>=4.0.0              # 测试覆盖率
pytest-xdist>=2.5.0            # 并行测试
pytest-mock>=3.8.0             # Mock支持
pytest-html>=3.1.0             # HTML测试报告
pytest-asyncio>=0.21.0         # 异步测试支持

# 代码质量工具
black>=22.0.0                   # 代码格式化
isort>=5.10.0                   # 导入排序
flake8>=5.0.0                   # 代码检查
flake8-docstrings>=1.6.0        # 文档字符串检查
flake8-import-order>=0.18.0     # 导入顺序检查
pylint>=2.15.0                  # 代码分析
bandit>=1.7.0                   # 安全检查

# 类型检查
mypy>=0.991                     # 静态类型检查
types-requests>=2.28.0          # requests类型存根
types-PyYAML>=6.0.0             # PyYAML类型存根
types-python-dateutil>=2.8.0    # dateutil类型存根

# 文档生成
sphinx>=5.0.0                   # 文档生成
sphinx-rtd-theme>=1.0.0         # ReadTheDocs主题
sphinx-autodoc-typehints>=1.19.0 # 类型提示文档
myst-parser>=0.18.0             # Markdown解析器

# 代码分析和度量
coverage>=6.0.0                 # 覆盖率分析
radon>=5.1.0                    # 代码复杂度分析
vulture>=2.6.0                  # 死代码检测
safety>=2.2.0                   # 安全漏洞检查

# 性能分析
memory-profiler>=0.60.0         # 内存分析
line-profiler>=4.0.0            # 行级性能分析
py-spy>=0.3.0                   # 采样分析器

# 开发工具
pre-commit>=2.20.0              # Git钩子管理
commitizen>=2.35.0              # 提交消息规范
bumpversion>=0.6.0              # 版本管理
twine>=4.0.0                    # PyPI上传工具

# 调试工具
ipdb>=0.13.0                    # 增强调试器
pdbpp>=0.10.0                   # 调试器增强
icecream>=2.1.0                 # 调试打印

# 开发服务器和热重载
watchdog>=2.1.0                 # 文件监控
livereload>=2.6.0               # 自动重载

# 数据生成和测试工具
factory-boy>=3.2.0              # 测试数据工厂
faker>=15.0.0                   # 假数据生成
hypothesis>=6.56.0              # 属性测试

# 环境管理
python-dotenv>=0.20.0           # 环境变量管理
environs>=9.5.0                 # 环境配置

# 构建和打包工具
build>=0.8.0                    # 现代构建工具
setuptools-scm>=7.0.0           # 版本管理
wheel>=0.37.0                   # 包构建

# 依赖管理
pip-tools>=6.8.0                # 依赖锁定
pipdeptree>=2.3.0               # 依赖树分析

# 代码格式化配置文件生成
toml>=0.10.0                    # TOML配置文件支持

# Jupyter支持（可选）
jupyter>=1.0.0                  # Jupyter Notebook
ipykernel>=6.15.0               # Jupyter内核
matplotlib>=3.5.0               # 绘图（用于数据分析）

# 数据库工具（开发测试用）
sqlite3                         # SQLite（Python内置）

# 网络和API测试
responses>=0.21.0               # HTTP响应模拟
httpx>=0.23.0                   # 现代HTTP客户端

# 时间和日期测试
freezegun>=1.2.0                # 时间冻结测试
python-dateutil>=2.8.0          # 日期处理

# 配置和设置
dynaconf>=3.1.0                 # 动态配置管理

# 日志和监控
structlog>=22.1.0               # 结构化日志
loguru>=0.6.0                   # 现代日志库

# 异步开发工具
aiofiles>=0.8.0                 # 异步文件操作
asyncio-mqtt>=0.11.0            # 异步MQTT（如果需要）

# GUI开发工具
qt-material>=2.14               # Material Design主题
qdarkstyle>=3.0.0               # 暗色主题

# 国际化工具
babel>=2.10.0                   # 国际化支持

# 缓存工具
redis>=4.3.0                    # Redis客户端（如果使用Redis缓存）
diskcache>=5.4.0                # 磁盘缓存

# 序列化工具
msgpack>=1.0.0                  # 高效序列化
orjson>=3.8.0                   # 快速JSON

# 并发和异步
aiohttp>=3.8.0                  # 异步HTTP
asyncio>=3.4.3                  # 异步IO

# 开发环境检查
check-manifest>=0.48            # 清单文件检查
readme-renderer>=37.0           # README渲染检查

# 许可证检查
licensecheck>=2022.7.1          # 许可证检查

# 代码重构工具
rope>=1.3.0                     # Python重构库
autoflake>=1.4.0                # 自动删除未使用导入

# 依赖安全检查
pip-audit>=2.6.0                # 依赖安全审计

# 开发环境配置
python-decouple>=3.6            # 配置解耦
