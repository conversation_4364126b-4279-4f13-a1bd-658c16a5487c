"""元数据管理器

负责元数据的创建、保存、加载和管理。
"""

import json
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional, List

from .interface import MetadataManagerInterface
from .models import StorageConfig, MetadataIndex
from core.models import ProcessResult, MetadataInfo
from common.exceptions import StorageError
from common.logging import get_logger
from common.utils import ensure_dir, get_file_hash, backup_file


logger = get_logger(__name__)


class MetadataManager(MetadataManagerInterface):
    """元数据管理器实现"""
    
    def __init__(self, storage_config: StorageConfig = None):
        self.logger = get_logger(__name__)
        self.config = storage_config or StorageConfig()
        
        # 设置存储目录
        from common.config import get_config
        self.metadata_dir = Path(get_config('paths.data_dir', Path.home() / '.videoreader')) / 'metadata'
        self.index_file = self.metadata_dir / 'index.json'
        
        # 确保目录存在
        ensure_dir(self.metadata_dir)
        
        # 加载索引
        self._index = self._load_index()
        
        self.logger.info(f"元数据管理器初始化完成，存储目录: {self.metadata_dir}")
    
    def create_metadata(self, video_path: str, process_result: ProcessResult) -> MetadataInfo:
        """创建元数据"""
        try:
            video_path = str(Path(video_path).resolve())
            
            # 计算视频文件校验和
            checksum = self.calculate_checksum(video_path)
            
            # 创建元数据对象
            metadata = MetadataInfo(
                video_path=video_path,
                process_result=process_result,
                created_time=datetime.now(),
                modified_time=datetime.now(),
                version="1.0.0",
                checksum=checksum
            )
            
            self.logger.info(f"元数据创建成功: {Path(video_path).name}")
            return metadata
            
        except Exception as e:
            self.logger.error(f"创建元数据失败: {e}")
            raise StorageError(f"创建元数据失败: {e}")
    
    def update_metadata(self, metadata: MetadataInfo, updates: Dict[str, Any]) -> MetadataInfo:
        """更新元数据"""
        try:
            # 更新字段
            for key, value in updates.items():
                if hasattr(metadata, key):
                    setattr(metadata, key, value)
            
            # 更新修改时间
            metadata.modified_time = datetime.now()
            
            self.logger.debug(f"元数据更新成功: {Path(metadata.video_path).name}")
            return metadata
            
        except Exception as e:
            self.logger.error(f"更新元数据失败: {e}")
            raise StorageError(f"更新元数据失败: {e}")
    
    def get_metadata_path(self, video_path: str) -> str:
        """获取元数据文件路径"""
        video_path = str(Path(video_path).resolve())
        
        # 使用视频文件的哈希值作为元数据文件名
        video_hash = hashlib.md5(video_path.encode()).hexdigest()
        metadata_filename = f"{video_hash}.json"
        
        return str(self.metadata_dir / metadata_filename)
    
    def calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        try:
            return get_file_hash(file_path, 'sha256')
        except Exception as e:
            self.logger.warning(f"计算校验和失败: {e}")
            return ""
    
    def save_metadata(self, metadata: MetadataInfo, file_path: str = None) -> bool:
        """保存元数据到文件"""
        try:
            if file_path is None:
                file_path = self.get_metadata_path(metadata.video_path)
            
            file_path = Path(file_path)
            ensure_dir(file_path.parent)
            
            # 备份现有文件
            if file_path.exists() and self.config.backup_enabled:
                try:
                    backup_file(file_path)
                except Exception as e:
                    self.logger.warning(f"备份元数据文件失败: {e}")
            
            # 转换为字典
            metadata_dict = self._metadata_to_dict(metadata)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(metadata_dict, f, indent=2, ensure_ascii=False, default=str)
            
            # 更新索引
            self._update_index(metadata, str(file_path))
            
            self.logger.info(f"元数据保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存元数据失败: {e}")
            raise StorageError(f"保存元数据失败: {e}")
    
    def load_metadata(self, file_path: str) -> Optional[MetadataInfo]:
        """从文件加载元数据"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
            
            # 转换为MetadataInfo对象
            metadata = self._dict_to_metadata(metadata_dict)
            
            self.logger.debug(f"元数据加载成功: {file_path}")
            return metadata
            
        except Exception as e:
            self.logger.error(f"加载元数据失败: {e}")
            raise StorageError(f"加载元数据失败: {e}")
    
    def find_metadata_by_video(self, video_path: str) -> Optional[MetadataInfo]:
        """根据视频路径查找元数据"""
        metadata_path = self.get_metadata_path(video_path)
        return self.load_metadata(metadata_path)
    
    def list_all_metadata(self) -> List[MetadataInfo]:
        """列出所有元数据"""
        metadata_list = []
        
        try:
            for metadata_file in self.metadata_dir.glob("*.json"):
                if metadata_file.name == "index.json":
                    continue
                
                try:
                    metadata = self.load_metadata(str(metadata_file))
                    if metadata:
                        metadata_list.append(metadata)
                except Exception as e:
                    self.logger.warning(f"加载元数据文件失败 {metadata_file}: {e}")
            
            return metadata_list
            
        except Exception as e:
            self.logger.error(f"列出元数据失败: {e}")
            return []
    
    def delete_metadata(self, video_path: str) -> bool:
        """删除元数据"""
        try:
            metadata_path = Path(self.get_metadata_path(video_path))
            
            if metadata_path.exists():
                # 备份后删除
                if self.config.backup_enabled:
                    backup_file(metadata_path)
                
                metadata_path.unlink()
                
                # 从索引中移除
                self._remove_from_index(video_path)
                
                self.logger.info(f"元数据删除成功: {metadata_path}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"删除元数据失败: {e}")
            raise StorageError(f"删除元数据失败: {e}")
    
    def _metadata_to_dict(self, metadata: MetadataInfo) -> Dict[str, Any]:
        """将MetadataInfo转换为字典"""
        return {
            'video_path': metadata.video_path,
            'process_result': self._process_result_to_dict(metadata.process_result),
            'created_time': metadata.created_time.isoformat(),
            'modified_time': metadata.modified_time.isoformat(),
            'version': metadata.version,
            'checksum': metadata.checksum,
            'tags': metadata.tags,
            'notes': metadata.notes
        }
    
    def _dict_to_metadata(self, data: Dict[str, Any]) -> MetadataInfo:
        """将字典转换为MetadataInfo"""
        from core.models import ProcessResult
        
        return MetadataInfo(
            video_path=data['video_path'],
            process_result=self._dict_to_process_result(data['process_result']),
            created_time=datetime.fromisoformat(data['created_time']),
            modified_time=datetime.fromisoformat(data['modified_time']),
            version=data.get('version', '1.0.0'),
            checksum=data.get('checksum', ''),
            tags=data.get('tags', []),
            notes=data.get('notes', '')
        )
    
    def _process_result_to_dict(self, result: ProcessResult) -> Dict[str, Any]:
        """将ProcessResult转换为字典"""
        # 这里需要实现ProcessResult的序列化
        # 暂时返回基本信息
        return {
            'video_info': {
                'file_path': result.video_info.file_path,
                'duration': result.video_info.duration,
                'width': result.video_info.width,
                'height': result.video_info.height,
                'fps': result.video_info.fps
            },
            'segments': [
                {
                    'id': seg.id,
                    'start_time': seg.start_time,
                    'end_time': seg.end_time,
                    'text': seg.text,
                    'summary': seg.summary,
                    'confidence': seg.confidence
                }
                for seg in result.segments
            ],
            'parser_type': result.parser_type.value,
            'total_segments': result.total_segments,
            'processing_time': result.processing_time
        }
    
    def _dict_to_process_result(self, data: Dict[str, Any]) -> ProcessResult:
        """将字典转换为ProcessResult"""
        # 这里需要实现ProcessResult的反序列化
        # 暂时创建简化版本
        from core.models import ProcessResult, VideoInfo, SegmentInfo, ParserType
        
        video_info = VideoInfo(
            file_path=data['video_info']['file_path'],
            file_name=Path(data['video_info']['file_path']).name,
            file_size=0,
            duration=data['video_info']['duration'],
            width=data['video_info']['width'],
            height=data['video_info']['height'],
            fps=data['video_info']['fps'],
            codec='',
            has_audio=True
        )
        
        segments = [
            SegmentInfo(
                id=seg['id'],
                start_time=seg['start_time'],
                end_time=seg['end_time'],
                text=seg['text'],
                summary=seg.get('summary'),
                confidence=seg.get('confidence', 1.0)
            )
            for seg in data['segments']
        ]
        
        return ProcessResult(
            video_info=video_info,
            segments=segments,
            parser_type=ParserType(data['parser_type']),
            parser_config={},
            processing_time=data.get('processing_time', 0.0),
            total_segments=data.get('total_segments', len(segments))
        )
    
    def _load_index(self) -> Dict[str, MetadataIndex]:
        """加载元数据索引"""
        try:
            if self.index_file.exists():
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                
                return {
                    key: MetadataIndex(
                        video_path=item['video_path'],
                        metadata_path=item['metadata_path'],
                        last_modified=datetime.fromisoformat(item['last_modified']),
                        file_size=item['file_size'],
                        checksum=item['checksum'],
                        tags=item.get('tags', []),
                        summary=item.get('summary', '')
                    )
                    for key, item in index_data.items()
                }
            
            return {}
            
        except Exception as e:
            self.logger.warning(f"加载索引失败: {e}")
            return {}
    
    def _save_index(self):
        """保存元数据索引"""
        try:
            index_data = {
                key: item.to_dict()
                for key, item in self._index.items()
            }
            
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"保存索引失败: {e}")
    
    def _update_index(self, metadata: MetadataInfo, metadata_path: str):
        """更新索引"""
        try:
            key = metadata.video_path
            
            self._index[key] = MetadataIndex(
                video_path=metadata.video_path,
                metadata_path=metadata_path,
                last_modified=metadata.modified_time,
                file_size=Path(metadata_path).stat().st_size if Path(metadata_path).exists() else 0,
                checksum=metadata.checksum,
                tags=metadata.tags,
                summary=metadata.notes[:100] if metadata.notes else ""
            )
            
            self._save_index()
            
        except Exception as e:
            self.logger.warning(f"更新索引失败: {e}")
    
    def _remove_from_index(self, video_path: str):
        """从索引中移除"""
        try:
            if video_path in self._index:
                del self._index[video_path]
                self._save_index()
                
        except Exception as e:
            self.logger.warning(f"从索引中移除失败: {e}")
