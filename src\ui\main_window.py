"""主窗口实现

VideoReader应用的主窗口界面。
"""

import sys
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path

try:
    from PySide6.QtWidgets import (
        QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
        QMenuBar, QStatusBar, QFileDialog, QMessageBox,
        QProgressBar, QLabel
    )
    from PySide6.QtCore import Qt, Signal, QTimer
    from PySide6.QtGui import QIcon, QKeySequence, QAction
    PYSIDE_AVAILABLE = True
    print("MainWindow: PySide6导入成功")
except ImportError as e:
    print(f"MainWindow: PySide6导入失败: {e}")
    PYSIDE_AVAILABLE = False
    # 如果PySide6不可用，创建模拟类
    class QMainWindow:
        def __init__(self): pass
    class QWidget:
        def __init__(self): pass
    class Signal:
        def __init__(self, *args): pass
    class Qt:
        Horizontal = 1
        Vertical = 2
    class QVBoxLayout:
        def __init__(self, parent=None): pass
        def addWidget(self, widget): pass
        def setStretch(self, index, stretch): pass
    class QHBoxLayout:
        def __init__(self, parent=None): pass
        def addWidget(self, widget): pass
    class QSplitter:
        def __init__(self, orientation): pass
        def addWidget(self, widget): pass
        def setSizes(self, sizes): pass
    class QLabel:
        def __init__(self, text=""): pass
        def setStyleSheet(self, style): pass
    class QAction:
        def __init__(self, text, parent): pass
        def setShortcut(self, shortcut): pass
        def triggered(self): return Signal()
    class QFileDialog:
        @staticmethod
        def getOpenFileName(parent, title, dir, filter): return "", ""
        @staticmethod
        def getExistingDirectory(parent, title): return ""
    class QMessageBox:
        @staticmethod
        def critical(parent, title, text): pass
        @staticmethod
        def warning(parent, title, text): pass
        @staticmethod
        def information(parent, title, text): pass
        @staticmethod
        def about(parent, title, text): pass
        @staticmethod
        def question(parent, title, text, buttons, default): return 0
        Yes = 1
        No = 0
    class QProgressBar:
        def __init__(self): pass
        def setVisible(self, visible): pass
        def setValue(self, value): pass
    class QKeySequence:
        Open = "Ctrl+O"
        Quit = "Ctrl+Q"
        Find = "Ctrl+F"

try:
    from .interface import UIManagerInterface, UIEvent, UIEventType
    from ..core.models import VideoInfo, ProcessResult, SegmentInfo
    from ..common.exceptions import UIError
    from ..common.logging import get_logger
    from .dialogs.video_process import VideoProcessDialog
    from .dialogs.export_data import ExportDataDialog
    from .dialogs.settings import SettingsDialog
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from src.ui.interface import UIManagerInterface, UIEvent, UIEventType
    from src.core.models import VideoInfo, ProcessResult, SegmentInfo
    from src.common.exceptions import UIError
    from src.common.logging import get_logger
    from src.ui.dialogs.video_process import VideoProcessDialog
    from src.ui.dialogs.export_data import ExportDataDialog
    from src.ui.dialogs.settings import SettingsDialog


logger = get_logger(__name__)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 定义信号
    file_open_requested = Signal(str)
    process_start_requested = Signal(dict)
    segment_selected = Signal(int)
    play_requested = Signal(float)
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        # 事件处理器
        self._event_handlers: Dict[UIEventType, List[Callable]] = {}
        
        # UI组件
        self.central_widget = None
        self.video_player = None
        self.segment_list = None
        self.text_viewer = None
        self.control_panel = None
        self.search_filter = None
        
        # 状态
        self.current_video: Optional[VideoInfo] = None
        self.current_result: Optional[ProcessResult] = None
        self.is_processing = False
        
        # 状态栏组件
        self.status_label = None
        self.progress_bar = None
        
        self.logger.info("主窗口初始化完成")
    
    def initialize(self) -> bool:
        """初始化界面"""
        try:
            self._setup_ui()
            self._setup_menu()
            self._setup_status_bar()
            self._setup_connections()

            # 在界面初始化完成后隐藏搜索与过滤组件（默认隐藏）
            if self.search_filter:
                self.search_filter.hide()

            self.logger.info("主窗口界面初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"主窗口初始化失败: {e}")
            raise UIError(f"主窗口初始化失败: {e}")
    
    def _setup_ui(self):
        """设置UI布局"""
        # 设置窗口属性
        self.setWindowTitle("VideoReader - 视频阅读器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(self.central_widget)

        # 创建搜索与过滤组件（顶部，可隐藏）
        try:
            from .components.search_filter import SearchFilterWidget
            self.search_filter = SearchFilterWidget()
            # 先设置为可见，让布局分配空间，稍后再隐藏
            self.search_filter.setVisible(True)
            main_layout.addWidget(self.search_filter)
        except ImportError:
            self.logger.warning("搜索与过滤组件未实现")
            self.search_filter = None

        # 创建主分割器（三列布局）
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)

        # 文件列表面板（第一列）
        file_panel = self._create_file_panel()
        main_splitter.addWidget(file_panel)

        # 段落列表面板（第二列）
        segment_panel = self._create_segment_panel()
        main_splitter.addWidget(segment_panel)

        # 内容面板（第三列）- 视频播放器和文本查看器
        content_panel = self._create_content_panel()
        main_splitter.addWidget(content_panel)

        # 设置分割器比例 [文件列表:段落列表:内容面板]
        # 根据屏幕大小动态调整，但设置合理的默认值
        total_width = 1200  # 假设的总宽度
        file_panel_width = 280      # 文件列表宽度
        segment_panel_width = 320   # 段落列表宽度
        content_panel_width = 600   # 内容面板宽度

        main_splitter.setSizes([file_panel_width, segment_panel_width, content_panel_width])

        # 设置最小宽度限制
        main_splitter.setChildrenCollapsible(False)  # 防止面板完全折叠

        # 保存分割器引用以便后续操作
        self.main_splitter = main_splitter
    
    def _create_file_panel(self) -> QWidget:
        """创建文件列表面板（第一列）"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建标题栏和折叠按钮
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(5, 5, 5, 5)

        # 标题
        title_label = QLabel("文件列表")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 折叠/展开按钮
        from PySide6.QtWidgets import QPushButton
        self.file_panel_toggle_button = QPushButton("◀")
        self.file_panel_toggle_button.setFixedSize(20, 20)
        self.file_panel_toggle_button.setStyleSheet("""
            QPushButton {
                border: 1px solid gray;
                border-radius: 3px;
                background-color: #f0f0f0;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        self.file_panel_toggle_button.setToolTip("折叠/展开文件列表")
        self.file_panel_toggle_button.clicked.connect(self._toggle_file_panel)
        header_layout.addWidget(self.file_panel_toggle_button)

        layout.addLayout(header_layout)

        # 文件列表容器
        self.file_list_container = QWidget()
        file_container_layout = QVBoxLayout(self.file_list_container)
        file_container_layout.setContentsMargins(0, 0, 0, 0)

        # 文件列表
        try:
            from .components.file_list import FileListWidget
            self.file_list = FileListWidget()
            file_container_layout.addWidget(self.file_list)
        except ImportError:
            self.logger.warning("文件列表组件未实现，使用占位符")
            placeholder = QLabel("文件列表")
            placeholder.setStyleSheet("border: 1px solid gray; padding: 10px;")
            file_container_layout.addWidget(placeholder)

        layout.addWidget(self.file_list_container)

        # 设置面板最小宽度
        panel.setMinimumWidth(200)

        # 保存面板引用和状态
        self.file_panel = panel
        self.file_panel_collapsed = False
        self.file_panel_original_width = 280
        self.segment_panel_original_width = 320  # 保存段落列表的原始宽度

        return panel

    def _create_segment_panel(self) -> QWidget:
        """创建段落列表面板（第二列）"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)

        # 段落列表
        try:
            from .components.segment_list import SegmentListWidget
            self.segment_list = SegmentListWidget()
            layout.addWidget(self.segment_list)
        except ImportError:
            self.logger.warning("段落列表组件未实现，使用占位符")
            placeholder = QLabel("段落列表")
            placeholder.setStyleSheet("border: 1px solid gray; padding: 10px;")
            layout.addWidget(placeholder)

        # 控制面板（简化版，放在底部）
        try:
            from .components.control_panel import ControlPanelWidget
            self.control_panel = ControlPanelWidget()
            layout.addWidget(self.control_panel)
        except ImportError:
            self.logger.warning("控制面板组件未实现，使用占位符")
            placeholder = QLabel("状态信息")
            placeholder.setStyleSheet("border: 1px solid gray; padding: 10px;")
            layout.addWidget(placeholder)

        # 设置布局比例
        layout.setStretch(0, 1)  # 段落列表可伸缩
        layout.setStretch(1, 0)  # 控制面板固定高度

        # 设置面板最小宽度
        panel.setMinimumWidth(250)

        return panel
    
    def _create_content_panel(self) -> QWidget:
        """创建内容面板（第三列）"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # 设置右边距
        layout.setContentsMargins(0, 0, 15, 0)  # 左、上、右、下边距

        # 创建垂直分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 视频播放器
        try:
            from .components.video_player import VideoPlayerWidget
            self.video_player = VideoPlayerWidget()
            splitter.addWidget(self.video_player)
        except ImportError:
            self.logger.warning("视频播放器组件未实现，使用占位符")
            placeholder = QLabel("视频播放器")
            placeholder.setStyleSheet("border: 1px solid gray; padding: 10px; background: black; color: white;")
            splitter.addWidget(placeholder)
        
        # 文本查看器
        try:
            from .components.text_viewer import TextViewerWidget
            self.text_viewer = TextViewerWidget()
            splitter.addWidget(self.text_viewer)
        except ImportError:
            self.logger.warning("文本查看器组件未实现，使用占位符")
            placeholder = QLabel("文本查看器")
            placeholder.setStyleSheet("border: 1px solid gray; padding: 10px;")
            splitter.addWidget(placeholder)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])

        # 设置面板最小宽度（减小以便看到右边距效果）
        panel.setMinimumWidth(300)

        return panel
    
    def _setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 打开文件
        open_action = QAction('打开视频(&O)', self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self._on_open_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # 处理视频
        process_action = QAction('处理视频(&P)', self)
        process_action.setShortcut('Ctrl+P')
        process_action.triggered.connect(self._on_process_video)
        process_action.setEnabled(False)  # 初始状态禁用
        file_menu.addAction(process_action)
        self.process_action = process_action  # 保存引用以便后续启用/禁用

        file_menu.addSeparator()

        # 导出数据
        export_action = QAction('导出数据(&E)', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self._on_export_data)
        export_action.setEnabled(False)  # 初始状态禁用
        file_menu.addAction(export_action)
        self.export_action = export_action  # 保存引用以便后续启用/禁用

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')
        
        # 搜索
        search_action = QAction('搜索(&S)', self)
        search_action.setShortcut(QKeySequence.Find)
        search_action.triggered.connect(self._on_search)
        edit_menu.addAction(search_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')

        # 搜索与过滤面板
        self.search_filter_action = QAction('搜索与过滤面板(&F)', self)
        self.search_filter_action.setCheckable(True)
        self.search_filter_action.setChecked(False)  # 默认隐藏
        self.search_filter_action.triggered.connect(self._toggle_search_filter_panel)
        view_menu.addAction(self.search_filter_action)

        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')

        # 高级设置
        settings_action = QAction('高级设置(&S)', self)
        settings_action.setShortcut('Ctrl+,')
        settings_action.triggered.connect(self._on_advanced_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self._on_about)
        help_menu.addAction(about_action)
    
    def _setup_status_bar(self):
        """设置状态栏"""
        status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_bar.addPermanentWidget(self.progress_bar)
    
    def _setup_connections(self):
        """设置信号连接"""
        # 连接文件列表信号
        if hasattr(self, 'file_list'):
            if hasattr(self.file_list, 'file_selected'):
                self.file_list.file_selected.connect(self._on_file_selected)
            if hasattr(self.file_list, 'file_double_clicked'):
                self.file_list.file_double_clicked.connect(self._on_file_double_clicked)
            if hasattr(self.file_list, 'process_requested'):
                self.file_list.process_requested.connect(self._on_file_process_requested)
            if hasattr(self.file_list, 'remove_requested'):
                self.file_list.remove_requested.connect(self._on_file_remove_requested)

        # 连接段落列表信号
        if hasattr(self.segment_list, 'segment_selected'):
            self.segment_list.segment_selected.connect(self._on_segment_selected)

        # 连接视频播放器信号
        if hasattr(self.video_player, 'position_changed'):
            self.video_player.position_changed.connect(self._on_position_changed)
        if hasattr(self.video_player, 'state_changed'):
            self.video_player.state_changed.connect(self._on_player_state_changed)

        # 连接控制面板信号
        if hasattr(self.control_panel, 'status_updated'):
            self.control_panel.status_updated.connect(self._on_status_updated)

        # 连接搜索与过滤组件信号
        if self.search_filter:
            self.search_filter.search_requested.connect(self._on_search_requested)
            self.search_filter.filter_changed.connect(self._on_filter_changed)
    
    def show_main_window(self) -> None:
        """显示主窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def hide_main_window(self) -> None:
        """隐藏主窗口"""
        self.hide()
    
    def close_application(self) -> None:
        """关闭应用程序"""
        self.close()
    
    def register_event_handler(self, event_type: UIEventType, handler: Callable[[UIEvent], None]) -> None:
        """注册事件处理器"""
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)
    
    def unregister_event_handler(self, event_type: UIEventType, handler: Callable[[UIEvent], None]) -> None:
        """取消注册事件处理器"""
        if event_type in self._event_handlers:
            try:
                self._event_handlers[event_type].remove(handler)
            except ValueError:
                pass
    
    def emit_event(self, event: UIEvent) -> None:
        """发送事件"""
        if event.event_type in self._event_handlers:
            for handler in self._event_handlers[event.event_type]:
                try:
                    handler(event)
                except Exception as e:
                    self.logger.error(f"事件处理器执行失败: {e}")
    
    def update_progress(self, progress: float, message: str = "") -> None:
        """更新进度"""
        if progress < 0:
            self.progress_bar.setVisible(False)
            self.status_label.setText("就绪")
        else:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(int(progress * 100))
            if message:
                self.status_label.setText(message)

    def set_process_result(self, result: ProcessResult) -> None:
        """设置处理结果"""
        self.current_result = result

        # 启用导出菜单项
        if hasattr(self, 'export_action'):
            self.export_action.setEnabled(True)

        # 更新段落列表
        if hasattr(self.segment_list, 'set_segments') and result.segments:
            self.segment_list.set_segments(result.segments)

        # 更新文件列表中的文件状态
        if hasattr(self, 'file_list') and hasattr(result, 'video_path'):
            from .components.file_list import FileStatus
            self.file_list.update_file_status(
                result.video_path,
                FileStatus.PROCESSED,
                result.metadata_path if hasattr(result, 'metadata_path') else None
            )

        # 不再显示状态信息

        self.logger.info(f"处理结果已设置，包含 {len(result.segments)} 个段落")
    
    def show_error(self, message: str, details: str = "") -> None:
        """显示错误信息"""
        QMessageBox.critical(self, "错误", message)
        if details:
            self.logger.error(f"错误详情: {details}")
    
    def show_warning(self, message: str, details: str = "") -> None:
        """显示警告信息"""
        QMessageBox.warning(self, "警告", message)
        if details:
            self.logger.warning(f"警告详情: {details}")
    
    def show_info(self, message: str, details: str = "") -> None:
        """显示信息"""
        QMessageBox.information(self, "信息", message)
        if details:
            self.logger.info(f"信息详情: {details}")
    
    def ask_confirmation(self, message: str, title: str = "确认") -> bool:
        """询问确认"""
        reply = QMessageBox.question(self, title, message, 
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        return reply == QMessageBox.Yes
    
    def select_file(self, title: str = "选择文件", file_filter: str = "") -> Optional[str]:
        """选择文件"""
        if not file_filter:
            file_filter = "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;所有文件 (*.*)"

        file_path, _ = QFileDialog.getOpenFileName(self, title, "", file_filter)
        return file_path if file_path else None

    def select_files(self, title: str = "选择文件", file_filter: str = "") -> List[str]:
        """选择多个文件"""
        if not file_filter:
            file_filter = "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;JSON文件 (*.json);;所有文件 (*.*)"

        file_paths, _ = QFileDialog.getOpenFileNames(self, title, "", file_filter)
        return file_paths if file_paths else []
    
    def select_directory(self, title: str = "选择目录") -> Optional[str]:
        """选择目录"""
        dir_path = QFileDialog.getExistingDirectory(self, title)
        return dir_path if dir_path else None
    
    # 槽函数
    def _on_open_file(self):
        """打开文件"""
        file_paths = self.select_files("选择视频文件")
        if file_paths:
            # 添加到文件列表
            if hasattr(self, 'file_list'):
                for file_path in file_paths:
                    if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm')):
                        self.file_list.add_video_file(file_path)
                    elif file_path.lower().endswith('.json'):
                        self.file_list.add_metadata_file(file_path)

            # 如果只选择了一个文件，自动加载它
            if len(file_paths) == 1:
                event = UIEvent(UIEventType.FILE_OPEN_REQUESTED, {'file_path': file_paths[0]})
                self.emit_event(event)
    
    def _on_process_video(self):
        """处理视频"""
        current_video_path = ""

        # 从文件列表获取当前选中的视频文件
        if hasattr(self, 'file_list'):
            selected_file = self.file_list.get_selected_file()
            if selected_file and selected_file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm')):
                current_video_path = selected_file

        if not current_video_path:
            self.show_warning("请先选择一个视频文件")
            return

        # 打开视频处理对话框
        try:
            dialog = VideoProcessDialog(current_video_path, self)
            dialog.process_requested.connect(self._on_process_requested)
            dialog.cancel_requested.connect(self._on_process_cancelled)
            dialog.show()
        except Exception as e:
            self.logger.error(f"打开视频处理对话框失败: {e}")
            self.show_error("无法打开视频处理对话框")

    def _on_export_data(self):
        """导出数据"""
        if not self.current_result:
            self.show_warning("请先处理视频文件")
            return

        # 打开数据导出对话框
        dialog = ExportDataDialog(self.current_result, self)
        dialog.export_requested.connect(self._on_export_requested)
        dialog.show()

    def _on_search(self):
        """搜索"""
        # 这里可以打开搜索对话框
        event = UIEvent(UIEventType.SEARCH_REQUESTED, {})
        self.emit_event(event)

    def _on_advanced_settings(self):
        """高级设置"""
        # 打开高级设置对话框
        dialog = SettingsDialog(self)
        dialog.settings_changed.connect(self._on_settings_changed)
        dialog.show()
    
    def _on_about(self):
        """关于"""
        QMessageBox.about(self, "关于 VideoReader", 
                         "VideoReader v1.0.0\n\n"
                         "视频阅读器 - 将视频转换为可阅读的文本和图片格式\n\n"
                         "© 2024 VideoReader Team")
    
    def _on_file_selected(self, file_path: str):
        """文件被选中"""
        # 不再显示状态信息

        # 更新段落列表组件中的文件信息显示
        if hasattr(self, 'segment_list') and hasattr(self.segment_list, 'update_file_info'):
            # 从文件列表获取文件信息
            file_info = {}
            if hasattr(self, 'file_list') and hasattr(self.file_list, 'file_items'):
                file_item = self.file_list.file_items.get(file_path)
                if file_item:
                    file_info = {
                        'file_type': file_item.file_type.value,
                        'status': file_item.status.value,
                        'metadata_path': file_item.metadata_path
                    }

            self.segment_list.update_file_info(file_path, file_info)

        # 发送文件选择事件
        event = UIEvent(UIEventType.FILE_SELECTED, {'file_path': file_path})
        self.emit_event(event)

    def _on_file_double_clicked(self, file_path: str):
        """文件被双击"""
        # 根据文件类型执行不同操作
        if file_path.endswith('.json'):
            # 元信息文件，加载数据
            event = UIEvent(UIEventType.METADATA_LOAD_REQUESTED, {'file_path': file_path})
            self.emit_event(event)
        else:
            # 视频文件，加载到播放器
            event = UIEvent(UIEventType.FILE_OPEN_REQUESTED, {'file_path': file_path})
            self.emit_event(event)

            # 启用处理菜单项
            if hasattr(self, 'process_action'):
                self.process_action.setEnabled(True)

    def _on_file_process_requested(self, file_path: str):
        """文件处理请求"""
        # 打开视频处理对话框
        try:
            from .dialogs.video_process import VideoProcessDialog
            dialog = VideoProcessDialog(file_path, self)
            dialog.process_requested.connect(self._on_process_requested)
            dialog.cancel_requested.connect(self._on_process_cancelled)
            dialog.show()
        except ImportError as e:
            self.logger.error(f"无法打开视频处理对话框: {e}")
            self.show_error("无法打开视频处理对话框")

    def _on_file_remove_requested(self, file_path: str):
        """文件移除请求"""
        # 发送文件移除事件
        event = UIEvent(UIEventType.FILE_REMOVED, {'file_path': file_path})
        self.emit_event(event)

    def _on_status_updated(self, message: str):
        """状态更新"""
        # 可以在这里处理状态更新
        pass

    def _on_play_pause_requested(self):
        """播放/暂停请求"""
        if hasattr(self.video_player, 'toggle_play_pause'):
            self.video_player.toggle_play_pause()

    def _on_stop_requested(self):
        """停止请求"""
        if hasattr(self.video_player, 'stop'):
            self.video_player.stop()

    def _on_seek_requested(self, position: float):
        """跳转请求"""
        if hasattr(self.video_player, 'set_position'):
            self.video_player.set_position(position)

    def _on_player_state_changed(self, state):
        """播放器状态改变"""
        # 更新控制面板的播放状态
        if hasattr(self.control_panel, 'set_playing_state'):
            from PySide6.QtMultimedia import QMediaPlayer
            is_playing = (state == QMediaPlayer.PlayingState)
            self.control_panel.set_playing_state(is_playing)

    def _on_process_requested(self, parser_type, config: Dict[str, Any]):
        """处理请求"""
        event = UIEvent(UIEventType.PROCESS_START_REQUESTED, {
            'parser_type': parser_type,
            'config': config
        })
        self.emit_event(event)

    def _on_process_cancelled(self):
        """处理取消"""
        event = UIEvent(UIEventType.PROCESS_CANCEL_REQUESTED, {})
        self.emit_event(event)

    def _on_export_requested(self, export_format, file_path: str, options: Dict[str, Any]):
        """导出请求"""
        event = UIEvent(UIEventType.EXPORT_REQUESTED, {
            'export_format': export_format,
            'file_path': file_path,
            'options': options
        })
        self.emit_event(event)

    def _on_settings_changed(self, settings: Dict[str, Any]):
        """设置改变"""
        event = UIEvent(UIEventType.SETTINGS_CHANGED, {'settings': settings})
        self.emit_event(event)

    def _on_segment_selected(self, segment_id: int):
        """段落选择"""
        event = UIEvent(UIEventType.SEGMENT_SELECTED, {'segment_id': segment_id})
        self.emit_event(event)

    def _on_position_changed(self, position: float):
        """播放位置改变"""
        # 可以在这里更新相关UI
        pass

    def _toggle_search_filter_panel(self):
        """切换搜索与过滤面板的显示状态"""
        if self.search_filter:
            is_visible = self.search_filter.isVisible()
            new_visible = not is_visible

            if new_visible:
                self.search_filter.show()
            else:
                self.search_filter.hide()

            self.search_filter_action.setChecked(new_visible)

            # 强制更新布局
            if hasattr(self, 'central_widget') and self.central_widget:
                layout = self.central_widget.layout()
                if layout:
                    layout.update()

            status = "显示" if new_visible else "隐藏"
            self.logger.debug(f"{status}搜索与过滤面板")

    def _on_search_requested(self, search_params: Dict[str, Any]):
        """处理搜索请求"""
        try:
            scope = search_params.get('scope', 'current')

            if scope == 'current':
                # 对当前视频进行搜索
                if hasattr(self.segment_list, 'apply_search_filter'):
                    self.segment_list.apply_search_filter(search_params)
            else:
                # 对全部视频进行搜索
                # TODO: 实现全部视频搜索功能
                self.logger.info("全部视频搜索功能待实现")

        except Exception as e:
            self.logger.error(f"处理搜索请求失败: {e}")

    def _on_filter_changed(self, filter_params: Dict[str, Any]):
        """处理过滤条件改变"""
        try:
            # 应用过滤条件到当前段落列表
            if hasattr(self.segment_list, 'apply_search_filter'):
                # 获取当前搜索参数并更新过滤条件
                if self.search_filter:
                    current_params = self.search_filter.get_search_params()
                    current_params.update(filter_params)
                    self.segment_list.apply_search_filter(current_params)

        except Exception as e:
            self.logger.error(f"处理过滤条件改变失败: {e}")

    def _toggle_file_panel(self):
        """切换文件列表面板的折叠状态"""
        if self.file_panel_collapsed:
            # 展开文件列表
            self._expand_file_panel()
        else:
            # 折叠文件列表
            self._collapse_file_panel()

    def _collapse_file_panel(self):
        """折叠文件列表面板"""
        if self.file_panel_collapsed:
            return

        # 隐藏文件列表内容
        self.file_list_container.hide()

        # 更新按钮图标
        self.file_panel_toggle_button.setText("▶")
        self.file_panel_toggle_button.setToolTip("展开文件列表")

        # 设置面板最小宽度
        self.file_panel.setMaximumWidth(30)
        self.file_panel.setMinimumWidth(30)

        # 更新分割器大小，保持段落列表列宽度不变
        current_sizes = self.main_splitter.sizes()
        self.file_panel_original_width = current_sizes[0]
        # 保存段落列表的当前宽度（如果还没保存的话）
        if not hasattr(self, 'segment_panel_saved_width'):
            self.segment_panel_saved_width = current_sizes[1]
        # 将文件列表的宽度转移给内容面板，保持段落列表列宽度不变
        width_diff = current_sizes[0] - 30
        new_sizes = [30, self.segment_panel_saved_width, current_sizes[2] + width_diff]
        self.main_splitter.setSizes(new_sizes)

        self.file_panel_collapsed = True
        self.logger.debug("文件列表面板已折叠")

    def _expand_file_panel(self):
        """展开文件列表面板"""
        if not self.file_panel_collapsed:
            return

        # 恢复面板宽度限制
        self.file_panel.setMaximumWidth(16777215)  # Qt默认最大值
        self.file_panel.setMinimumWidth(200)

        # 显示文件列表内容
        self.file_list_container.show()

        # 更新按钮图标
        self.file_panel_toggle_button.setText("◀")
        self.file_panel_toggle_button.setToolTip("折叠文件列表")

        # 更新分割器大小，保持段落列表列宽度不变
        current_sizes = self.main_splitter.sizes()
        restored_width = min(self.file_panel_original_width, 300)  # 限制最大宽度
        # 从内容面板减去宽度，保持段落列表列宽度不变
        width_diff = restored_width - 30
        # 使用保存的段落列表宽度
        segment_width = getattr(self, 'segment_panel_saved_width', current_sizes[1])
        new_sizes = [restored_width, segment_width, current_sizes[2] - width_diff]
        self.main_splitter.setSizes(new_sizes)

        self.file_panel_collapsed = False
        self.logger.debug("文件列表面板已展开")

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_processing:
            if not self.ask_confirmation("正在处理中，确定要退出吗？"):
                event.ignore()
                return

        ui_event = UIEvent(UIEventType.WINDOW_CLOSED, {})
        self.emit_event(ui_event)
        event.accept()
