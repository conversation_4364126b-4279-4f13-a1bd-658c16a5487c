"""验证工具

提供各种数据验证功能。
"""

import re
import os
from pathlib import Path
from typing import Union, List, Any, Optional
from urllib.parse import urlparse

from ..exceptions import ValidationError


def validate_file_path(path: Union[str, Path], must_exist: bool = True, 
                      allowed_extensions: Optional[List[str]] = None) -> bool:
    """验证文件路径"""
    try:
        path = Path(path)
        
        # 检查路径是否存在
        if must_exist and not path.exists():
            raise ValidationError(f"文件不存在: {path}")
        
        # 检查是否为文件
        if must_exist and not path.is_file():
            raise ValidationError(f"路径不是文件: {path}")
        
        # 检查文件扩展名
        if allowed_extensions:
            if path.suffix.lower() not in [ext.lower() for ext in allowed_extensions]:
                raise ValidationError(f"不支持的文件格式: {path.suffix}")
        
        return True
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"路径验证失败: {e}")


def validate_video_file(path: Union[str, Path]) -> bool:
    """验证视频文件"""
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
    return validate_file_path(path, must_exist=True, allowed_extensions=video_extensions)


def validate_audio_file(path: Union[str, Path]) -> bool:
    """验证音频文件"""
    audio_extensions = ['.mp3', '.wav', '.aac', '.ogg', '.m4a', '.flac', '.wma']
    return validate_file_path(path, must_exist=True, allowed_extensions=audio_extensions)


def validate_image_file(path: Union[str, Path]) -> bool:
    """验证图像文件"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']
    return validate_file_path(path, must_exist=True, allowed_extensions=image_extensions)


def validate_directory(path: Union[str, Path], must_exist: bool = True, 
                      create_if_not_exists: bool = False) -> bool:
    """验证目录路径"""
    try:
        path = Path(path)
        
        if not path.exists():
            if must_exist and not create_if_not_exists:
                raise ValidationError(f"目录不存在: {path}")
            elif create_if_not_exists:
                path.mkdir(parents=True, exist_ok=True)
        elif not path.is_dir():
            raise ValidationError(f"路径不是目录: {path}")
        
        return True
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"目录验证失败: {e}")


def validate_url(url: str) -> bool:
    """验证URL格式"""
    try:
        result = urlparse(url)
        if not all([result.scheme, result.netloc]):
            raise ValidationError(f"无效的URL格式: {url}")
        return True
    except Exception as e:
        raise ValidationError(f"URL验证失败: {e}")


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, email):
        raise ValidationError(f"无效的邮箱格式: {email}")
    return True


def validate_numeric_range(value: Union[int, float], min_val: Optional[Union[int, float]] = None,
                          max_val: Optional[Union[int, float]] = None) -> bool:
    """验证数值范围"""
    try:
        if min_val is not None and value < min_val:
            raise ValidationError(f"值 {value} 小于最小值 {min_val}")
        
        if max_val is not None and value > max_val:
            raise ValidationError(f"值 {value} 大于最大值 {max_val}")
        
        return True
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"数值验证失败: {e}")


def validate_string_length(text: str, min_length: Optional[int] = None,
                          max_length: Optional[int] = None) -> bool:
    """验证字符串长度"""
    try:
        length = len(text)
        
        if min_length is not None and length < min_length:
            raise ValidationError(f"字符串长度 {length} 小于最小长度 {min_length}")
        
        if max_length is not None and length > max_length:
            raise ValidationError(f"字符串长度 {length} 大于最大长度 {max_length}")
        
        return True
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"字符串长度验证失败: {e}")


def validate_choice(value: Any, choices: List[Any]) -> bool:
    """验证值是否在选择列表中"""
    if value not in choices:
        raise ValidationError(f"值 {value} 不在允许的选择中: {choices}")
    return True


def validate_regex(text: str, pattern: str, error_message: str = None) -> bool:
    """使用正则表达式验证文本"""
    try:
        if not re.match(pattern, text):
            message = error_message or f"文本 '{text}' 不匹配模式 '{pattern}'"
            raise ValidationError(message)
        return True
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"正则表达式验证失败: {e}")


def validate_file_size(path: Union[str, Path], max_size: int) -> bool:
    """验证文件大小"""
    try:
        path = Path(path)
        if not path.exists():
            raise ValidationError(f"文件不存在: {path}")
        
        file_size = path.stat().st_size
        if file_size > max_size:
            raise ValidationError(f"文件大小 {file_size} 字节超过限制 {max_size} 字节")
        
        return True
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"文件大小验证失败: {e}")


def validate_timestamp(timestamp: Union[int, float], min_timestamp: Optional[Union[int, float]] = None,
                      max_timestamp: Optional[Union[int, float]] = None) -> bool:
    """验证时间戳"""
    try:
        if timestamp < 0:
            raise ValidationError(f"时间戳不能为负数: {timestamp}")
        
        if min_timestamp is not None and timestamp < min_timestamp:
            raise ValidationError(f"时间戳 {timestamp} 小于最小值 {min_timestamp}")
        
        if max_timestamp is not None and timestamp > max_timestamp:
            raise ValidationError(f"时间戳 {timestamp} 大于最大值 {max_timestamp}")
        
        return True
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"时间戳验证失败: {e}")


def validate_config_dict(config: dict, required_keys: List[str], 
                        optional_keys: Optional[List[str]] = None) -> bool:
    """验证配置字典"""
    try:
        # 检查必需的键
        for key in required_keys:
            if key not in config:
                raise ValidationError(f"配置缺少必需的键: {key}")
        
        # 检查是否有未知的键
        if optional_keys is not None:
            allowed_keys = set(required_keys + optional_keys)
            for key in config.keys():
                if key not in allowed_keys:
                    raise ValidationError(f"配置包含未知的键: {key}")
        
        return True
        
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(f"配置验证失败: {e}")


class Validator:
    """验证器类，支持链式调用"""
    
    def __init__(self, value: Any, name: str = "value"):
        self.value = value
        self.name = name
    
    def required(self):
        """验证值不为空"""
        if self.value is None or (isinstance(self.value, str) and not self.value.strip()):
            raise ValidationError(f"{self.name} 不能为空")
        return self
    
    def type_of(self, expected_type):
        """验证类型"""
        if not isinstance(self.value, expected_type):
            raise ValidationError(f"{self.name} 必须是 {expected_type.__name__} 类型")
        return self
    
    def min_length(self, length: int):
        """验证最小长度"""
        if len(self.value) < length:
            raise ValidationError(f"{self.name} 长度不能小于 {length}")
        return self
    
    def max_length(self, length: int):
        """验证最大长度"""
        if len(self.value) > length:
            raise ValidationError(f"{self.name} 长度不能大于 {length}")
        return self
    
    def min_value(self, value):
        """验证最小值"""
        if self.value < value:
            raise ValidationError(f"{self.name} 不能小于 {value}")
        return self
    
    def max_value(self, value):
        """验证最大值"""
        if self.value > value:
            raise ValidationError(f"{self.name} 不能大于 {value}")
        return self
    
    def in_choices(self, choices: List[Any]):
        """验证值在选择列表中"""
        if self.value not in choices:
            raise ValidationError(f"{self.name} 必须在 {choices} 中选择")
        return self
    
    def matches(self, pattern: str):
        """验证正则表达式匹配"""
        if not re.match(pattern, str(self.value)):
            raise ValidationError(f"{self.name} 格式不正确")
        return self
