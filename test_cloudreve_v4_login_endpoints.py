#!/usr/bin/env python3
"""测试Cloudreve V4登录端点"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def test_all_login_endpoints():
    """测试所有可能的登录端点"""
    print("=== 测试Cloudreve V4登录端点 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')
    
    try:
        import requests
        
        # 根据API文档，可能的登录端点
        login_endpoints = [
            # Session相关端点
            "/session/token/password",  # Password sign-in
            "/session/password",
            "/user/session",
            "/auth/login",
            "/auth/password",
            
            # 可能的V4端点
            "/api/v4/session/token/password",
            "/api/v4/session/password", 
            "/api/v4/user/session",
            "/api/v4/auth/login",
            "/api/v4/auth/password",
        ]
        
        # 不同的登录数据格式
        login_formats = [
            {'userName': username, 'Password': password},
            {'username': username, 'password': password},
            {'email': username, 'password': password},
            {'user': username, 'pass': password},
            {'login': username, 'password': password},
        ]
        
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
        })
        
        for endpoint in login_endpoints:
            url = f"{base_url}{endpoint}"
            print(f"\n测试端点: {endpoint}")
            
            # 先测试GET请求看端点是否存在
            try:
                get_resp = requests.get(url, timeout=5)
                print(f"  GET {get_resp.status_code}: {get_resp.text[:50]}")
                
                # 如果不是404，尝试POST登录
                if get_resp.status_code != 404:
                    for i, login_data in enumerate(login_formats):
                        try:
                            post_resp = session.post(url, json=login_data, timeout=10)
                            print(f"  POST格式{i+1} {post_resp.status_code}: {post_resp.text[:100]}")
                            
                            if post_resp.status_code == 200:
                                try:
                                    result = post_resp.json()
                                    print(f"    ✅ 登录成功! {json.dumps(result, indent=2, ensure_ascii=False)[:200]}")
                                    return endpoint, login_data, result
                                except:
                                    print(f"    响应: {post_resp.text}")
                            elif post_resp.status_code not in [404, 405]:
                                print(f"    可能的端点，状态: {post_resp.status_code}")
                                
                        except Exception as e:
                            print(f"    POST异常: {e}")
                            
            except Exception as e:
                print(f"  GET异常: {e}")
        
        return None, None, None
        
    except ImportError:
        print("需要安装requests库")
        return None, None, None


def test_prepare_login():
    """测试准备登录端点"""
    print("\n=== 测试准备登录 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    
    try:
        import requests
        
        # 根据API文档，可能需要先调用prepare login
        prepare_endpoints = [
            "/session/prepare",
            "/api/v4/session/prepare", 
            "/prepare-login",
            "/api/v4/prepare-login"
        ]
        
        for endpoint in prepare_endpoints:
            url = f"{base_url}{endpoint}"
            print(f"测试准备登录: {endpoint}")
            
            try:
                response = requests.get(url, timeout=10)
                print(f"  状态: {response.status_code}")
                print(f"  响应: {response.text[:200]}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"  JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        return endpoint, result
                    except:
                        pass
                        
            except Exception as e:
                print(f"  异常: {e}")
        
        return None, None
        
    except ImportError:
        print("需要安装requests库")
        return None, None


def test_site_endpoints():
    """测试站点相关端点"""
    print("\n=== 测试站点端点 ===")
    
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    
    try:
        import requests
        
        # 测试站点配置端点
        site_endpoints = [
            "/site/config",
            "/api/v4/site/config",
            "/site/settings", 
            "/api/v4/site/settings"
        ]
        
        for endpoint in site_endpoints:
            url = f"{base_url}{endpoint}"
            print(f"测试站点端点: {endpoint}")
            
            try:
                response = requests.get(url, timeout=10)
                print(f"  状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"  配置信息: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}")
                        
                        # 查看是否有登录相关配置
                        if 'login' in str(result).lower() or 'auth' in str(result).lower():
                            print("  ✅ 找到登录相关配置!")
                            
                    except:
                        print(f"  响应: {response.text[:100]}")
                        
            except Exception as e:
                print(f"  异常: {e}")
                
    except ImportError:
        print("需要安装requests库")


def main():
    """主函数"""
    print("Cloudreve V4登录端点测试")
    print("=" * 60)
    
    # 测试准备登录
    prepare_endpoint, prepare_result = test_prepare_login()
    
    # 测试站点端点
    test_site_endpoints()
    
    # 测试所有登录端点
    login_endpoint, login_data, login_result = test_all_login_endpoints()
    
    if login_endpoint:
        print(f"\n🎉 找到工作的登录端点!")
        print(f"端点: {login_endpoint}")
        print(f"数据格式: {login_data}")
        print(f"响应: {json.dumps(login_result, indent=2, ensure_ascii=False)}")
    else:
        print(f"\n❌ 未找到工作的登录端点")
        print("可能需要:")
        print("1. 检查API文档中的确切端点路径")
        print("2. 确认认证方式是否正确")
        print("3. 检查用户权限")


if __name__ == "__main__":
    main()
