#!/usr/bin/env python3
"""测试Cloudreve V4登录端点"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config


def test_all_login_endpoints():
    """测试所有可能的登录端点"""
    print("=== 测试Cloudreve V4登录端点 ===")

    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    password = get_config('file_uploader.uploaders.cloudreve.password', '')

    try:
        import requests

        # 根据官方API文档和已知工作端点模式测试各种可能的路径
        login_endpoints = [
            # 官方文档中显示的端点路径
            "/session/token",         # Password sign-in (官方文档显示)
            "/api/v4/session/token",  # 带v4前缀的版本

            # 基于已知工作端点的模式尝试其他可能路径
            "/api/v4/user/login",     # 可能的用户登录端点
            "/api/v4/auth/login",     # 可能的认证登录端点
            "/api/v4/login",          # 简化的登录端点
            "/login",                 # 最简单的登录端点
        ]

        # 根据官方文档的正确登录数据格式
        login_formats = [
            # 官方文档中的格式: email + password
            {'email': username, 'password': password},
            # 可能的其他格式作为备选
            {'username': username, 'password': password},
            {'userName': username, 'Password': password},
        ]
        
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'VideoReader-Cloudreve-Client/1.0'
        })
        
        for endpoint in login_endpoints:
            url = f"{base_url}{endpoint}"
            print(f"\n测试端点: {endpoint}")
            
            # 先测试GET请求看端点是否存在
            try:
                get_resp = requests.get(url, timeout=5)
                print(f"  GET {get_resp.status_code}: {get_resp.text[:50]}")
                
                # 如果不是404，尝试POST登录
                if get_resp.status_code != 404:
                    for i, login_data in enumerate(login_formats):
                        try:
                            post_resp = session.post(url, json=login_data, timeout=10)
                            print(f"  POST格式{i+1} {post_resp.status_code}: {post_resp.text[:100]}")
                            
                            if post_resp.status_code == 200:
                                try:
                                    result = post_resp.json()
                                    print(f"    ✅ 登录成功! {json.dumps(result, indent=2, ensure_ascii=False)[:200]}")
                                    return endpoint, login_data, result
                                except:
                                    print(f"    响应: {post_resp.text}")
                            elif post_resp.status_code not in [404, 405]:
                                print(f"    可能的端点，状态: {post_resp.status_code}")
                                
                        except Exception as e:
                            print(f"    POST异常: {e}")
                            
            except Exception as e:
                print(f"  GET异常: {e}")
        
        return None, None, None
        
    except ImportError:
        print("需要安装requests库")
        return None, None, None


def test_prepare_login():
    """测试准备登录端点"""
    print("\n=== 测试准备登录 ===")

    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')

    try:
        import requests

        # 根据官方API文档的正确端点 - 测试有无 /api/v4 前缀
        prepare_endpoints = [
            "/session/prepare",         # 官方文档显示的端点路径
            "/api/v4/session/prepare",  # 带v4前缀的版本
        ]
        
        for endpoint in prepare_endpoints:
            url = f"{base_url}{endpoint}"
            print(f"测试准备登录: {endpoint}")

            try:
                # 根据官方文档，准备登录需要POST请求并传入email
                prepare_data = {'email': username}
                response = requests.post(url, json=prepare_data, timeout=10)
                print(f"  状态: {response.status_code}")
                print(f"  响应: {response.text[:200]}")

                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"  JSON: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        return endpoint, result
                    except:
                        pass

            except Exception as e:
                print(f"  异常: {e}")
        
        return None, None
        
    except ImportError:
        print("需要安装requests库")
        return None, None


def test_site_endpoints():
    """测试站点相关端点"""
    print("\n=== 测试站点端点 ===")

    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')

    try:
        import requests

        # 根据官方文档的正确站点端点 - 需要添加 /api/v4 前缀
        site_endpoints = [
            "/api/v4/site/ping",      # 已知工作的端点
            "/api/v4/site/captcha",   # Get CAPTCHA
            "/api/v4/site/config",    # Get site settings (推测，文档中是 /site/settings)
        ]
        
        for endpoint in site_endpoints:
            url = f"{base_url}{endpoint}"
            print(f"测试站点端点: {endpoint}")
            
            try:
                response = requests.get(url, timeout=10)
                print(f"  状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"  配置信息: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}")
                        
                        # 查看是否有登录相关配置
                        if 'login' in str(result).lower() or 'auth' in str(result).lower():
                            print("  ✅ 找到登录相关配置!")
                            
                    except:
                        print(f"  响应: {response.text[:100]}")
                        
            except Exception as e:
                print(f"  异常: {e}")
                
    except ImportError:
        print("需要安装requests库")


def test_captcha_endpoint():
    """测试验证码端点"""
    print("\n=== 测试验证码端点 ===")

    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')

    try:
        import requests

        # 根据官方文档的验证码端点 - 需要添加 /api/v4 前缀
        captcha_url = f"{base_url}/api/v4/site/captcha"
        print(f"测试验证码端点: /api/v4/site/captcha")

        try:
            response = requests.get(captcha_url, timeout=10)
            print(f"  状态: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"  验证码信息: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}")
                    return result
                except:
                    print(f"  响应: {response.text[:100]}")
            else:
                print(f"  响应: {response.text[:100]}")

        except Exception as e:
            print(f"  异常: {e}")

    except ImportError:
        print("需要安装requests库")

    return None


def main():
    """主函数"""
    print("Cloudreve V4登录端点测试")
    print("=" * 60)

    # 测试站点端点
    test_site_endpoints()

    # 测试验证码端点
    captcha_info = test_captcha_endpoint()

    # 测试准备登录
    test_prepare_login()

    # 测试所有登录端点
    login_endpoint, login_data, login_result = test_all_login_endpoints()

    if login_endpoint:
        print(f"\n🎉 找到工作的登录端点!")
        print(f"端点: {login_endpoint}")
        print(f"数据格式: {login_data}")
        print(f"响应: {json.dumps(login_result, indent=2, ensure_ascii=False)}")
    else:
        print(f"\n❌ 未找到工作的登录端点")
        print("\n📋 测试结果总结:")
        print("✅ 正常工作的API端点:")
        print("   - /api/v4/site/ping (返回版本信息)")
        print("   - /api/v4/site/captcha (返回验证码)")
        print("\n❌ 不存在的API端点:")
        print("   - /api/v4/session/token (404)")
        print("   - /api/v4/session/prepare (404)")
        print("\n🌐 前端页面端点:")
        print("   - /session/token, /login 等返回HTML页面")
        print("\n💡 可能的解决方案:")
        print("1. 检查Cloudreve配置是否完全启用API端点")
        print("2. 确认这个版本的Cloudreve是否使用不同的认证机制")
        print("3. 考虑使用WebDAV或其他文件上传方式")
        print("4. 联系Cloudreve管理员确认API配置")


if __name__ == "__main__":
    main()
