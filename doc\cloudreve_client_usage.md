# Cloudreve客户端使用说明

## 概述

`CloudreveClient` 是一个基于Cloudreve V4 API的Python客户端，提供了完整的文件上传和管理功能。

## 功能特性

- ✅ 用户认证（登录/登出）
- ✅ 自动token刷新
- ✅ 验证码支持
- ✅ 文件分块上传
- ✅ 上传进度回调
- ✅ 错误处理和重试
- ✅ 完整的日志记录

## 快速开始

### 1. 配置文件设置

在 `config.toml` 中配置Cloudreve连接信息：

```toml
[file_uploader.uploaders.cloudreve]
base_url = "http://your-server.com:5212"  # Cloudreve服务器地址
username = "<EMAIL>"             # 用户邮箱（必须是email格式）
password = "your_password"                # 用户密码
timeout = 60                              # 请求超时时间
```

### 2. 基本使用

```python
from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError

# 创建客户端
client = CloudreveClient(
    base_url="http://your-server.com:5212",
    email="<EMAIL>", 
    password="your_password"
)

try:
    # 测试连接
    version = client.ping()
    print(f"服务器版本: {version}")
    
    # 登录
    login_result = client.login()
    print(f"登录成功: {login_result['user']['email']}")
    
    # 上传文件
    def progress_callback(uploaded, total):
        percent = (uploaded / total) * 100
        print(f"上传进度: {percent:.1f}%")
    
    result = client.upload_file(
        file_path="/path/to/your/file.mp4",
        progress_callback=progress_callback
    )
    print(f"上传成功: {result}")
    
    # 登出
    client.logout()
    
except CloudreveError as e:
    print(f"操作失败: {e}")
```

### 3. 处理验证码

如果服务器要求验证码，需要手动处理：

```python
try:
    client.login()
except CloudreveError as e:
    if e.code == 40003:  # 需要验证码
        # 获取验证码
        captcha_image, ticket = client.get_captcha()
        
        # 显示验证码图片给用户（captcha_image是base64格式）
        # 用户输入验证码
        captcha_input = input("请输入验证码: ")
        
        # 使用验证码登录
        client.login(captcha=captcha_input, ticket=ticket)
```

## API参考

### CloudreveClient类

#### 构造函数

```python
CloudreveClient(base_url: str, email: str, password: str, timeout: int = 60)
```

- `base_url`: Cloudreve服务器地址
- `email`: 用户邮箱地址
- `password`: 用户密码  
- `timeout`: 请求超时时间（秒）

#### 主要方法

##### ping() -> str
获取服务器版本信息

##### get_captcha() -> Tuple[str, str]
获取验证码，返回(验证码图片base64, ticket)

##### login(captcha: str = None, ticket: str = None) -> Dict[str, Any]
用户登录，返回用户信息和token

##### upload_file(file_path: str, policy_id: str = None, chunk_size: int = 5242880, progress_callback=None) -> str
上传文件
- `file_path`: 本地文件路径
- `policy_id`: 存储策略ID（可选）
- `chunk_size`: 文件块大小（默认5MB）
- `progress_callback`: 进度回调函数

##### logout() -> bool
用户登出

## 错误处理

所有API操作都可能抛出 `CloudreveError` 异常：

```python
try:
    client.login()
except CloudreveError as e:
    print(f"错误码: {e.code}")
    print(f"错误信息: {e}")
    print(f"完整响应: {e.response}")
```

常见错误码：
- `40001`: 邮箱格式错误
- `40002`: 密码错误
- `40003`: 需要验证码

## 测试

运行测试脚本验证客户端功能：

```bash
python test_cloudreve_client.py
```

## 注意事项

1. **邮箱格式**: 用户名必须是有效的邮箱格式
2. **网络连接**: 确保能够访问Cloudreve服务器
3. **文件大小**: 大文件会自动分块上传
4. **认证管理**: 客户端会自动处理token刷新
5. **错误重试**: 建议在生产环境中添加重试逻辑

## 集成到项目

在文件上传器中使用Cloudreve客户端：

```python
from src.uploaders.cloudreve_client import CloudreveClient
from src.common.config import get_config

def upload_to_cloudreve(file_path: str) -> str:
    """上传文件到Cloudreve"""
    
    # 从配置获取连接信息
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url')
    email = get_config('file_uploader.uploaders.cloudreve.username')
    password = get_config('file_uploader.uploaders.cloudreve.password')
    
    # 创建客户端并上传
    client = CloudreveClient(base_url, email, password)
    
    try:
        client.login()
        result = client.upload_file(file_path)
        client.logout()
        return result
    except CloudreveError as e:
        raise Exception(f"Cloudreve上传失败: {e}")
```
