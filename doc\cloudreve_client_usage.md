# Cloudreve客户端使用说明

## 概述

`CloudreveClient` 是一个基于Cloudreve V4 API的Python客户端，提供了完整的文件上传和管理功能。

## 功能特性

- ✅ 用户认证（登录/登出）
- ✅ 自动token刷新
- ✅ 验证码支持
- ✅ 文件分块上传
- ✅ 上传进度回调
- ✅ 错误处理和重试
- ✅ 完整的日志记录

## 快速开始

### 1. 配置文件设置

在 `config.toml` 中配置Cloudreve连接信息：

```toml
[file_uploader.uploaders.cloudreve]
base_url = "http://your-server.com:5212"  # Cloudreve服务器地址
username = "<EMAIL>"             # 用户邮箱（必须是email格式）
password = "your_password"                # 用户密码
timeout = 60                              # 请求超时时间
```

### 2. 基本使用

```python
from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError

# 创建客户端
client = CloudreveClient(
    base_url="http://your-server.com:5212",
    email="<EMAIL>", 
    password="your_password"
)

try:
    # 测试连接
    version = client.ping()
    print(f"服务器版本: {version}")
    
    # 登录
    login_result = client.login()
    print(f"登录成功: {login_result['user']['email']}")
    
    # 上传文件
    def progress_callback(uploaded, total):
        percent = (uploaded / total) * 100
        print(f"上传进度: {percent:.1f}%")
    
    result = client.upload_file(
        file_path="/path/to/your/file.mp4",
        progress_callback=progress_callback
    )
    print(f"上传成功: {result}")
    
    # 登出
    client.logout()
    
except CloudreveError as e:
    print(f"操作失败: {e}")
```

### 3. 处理验证码

如果服务器要求验证码，需要手动处理：

```python
try:
    client.login()
except CloudreveError as e:
    if e.code == 40003:  # 需要验证码
        # 获取验证码
        captcha_image, ticket = client.get_captcha()
        
        # 显示验证码图片给用户（captcha_image是base64格式）
        # 用户输入验证码
        captcha_input = input("请输入验证码: ")
        
        # 使用验证码登录
        client.login(captcha=captcha_input, ticket=ticket)
```

## API参考

### CloudreveClient类

#### 构造函数

```python
CloudreveClient(base_url: str, email: str, password: str, timeout: int = 60)
```

- `base_url`: Cloudreve服务器地址
- `email`: 用户邮箱地址
- `password`: 用户密码  
- `timeout`: 请求超时时间（秒）

#### 主要方法

##### ping() -> str
获取服务器版本信息

##### get_captcha() -> Tuple[str, str]
获取验证码，返回(验证码图片base64, ticket)

##### login(captcha: str = None, ticket: str = None) -> Dict[str, Any]
用户登录，返回用户信息和token

##### upload_file(file_path: str, policy_id: str = None, chunk_size: int = 5242880, progress_callback=None) -> str
上传文件
- `file_path`: 本地文件路径
- `policy_id`: 存储策略ID（可选）
- `chunk_size`: 文件块大小（默认5MB）
- `progress_callback`: 进度回调函数

##### logout() -> bool
用户登出

#### 文件管理方法

##### list_files(path: str = "/", page: int = 1, page_size: int = 50) -> Dict[str, Any]
列出文件和文件夹

##### get_file_info(file_id: str) -> Dict[str, Any]
获取文件详细信息

##### delete_file(file_ids: list, force: bool = False) -> bool
删除文件或文件夹
- `file_ids`: 文件ID列表
- `force`: 是否强制删除（跳过回收站）

##### create_download_url(file_id: str) -> str
创建文件下载链接

##### rename_file(file_id: str, new_name: str) -> bool
重命名文件或文件夹

##### move_files(file_ids: list, target_path: str, copy: bool = False) -> bool
移动或复制文件
- `copy`: False为移动，True为复制

##### create_folder(folder_name: str, parent_path: str = "/") -> Dict[str, Any]
创建文件夹

##### search_files(keyword: str, path: str = "/", file_type: str = None, page: int = 1, page_size: int = 50) -> Dict[str, Any]
搜索文件
- `file_type`: 文件类型过滤（'image', 'video', 'audio', 'document'）

#### 分享链接方法

##### create_share_link(file_ids: list, password: str = None, expire_days: int = None, download_limit: int = None, preview_enabled: bool = True) -> Dict[str, Any]
创建分享链接
- `password`: 分享密码（可选）
- `expire_days`: 过期天数（0表示永不过期）
- `download_limit`: 下载次数限制（0表示无限制）
- `preview_enabled`: 是否允许预览

##### list_share_links(page: int = 1, page_size: int = 50) -> Dict[str, Any]
获取我的分享链接列表

##### delete_share_link(share_id: str) -> bool
删除分享链接

##### get_share_info(share_key: str, password: str = None) -> Dict[str, Any]
获取分享链接信息

#### 其他方法

##### get_storage_info() -> Dict[str, Any]
获取存储空间信息

## 错误处理

所有API操作都可能抛出 `CloudreveError` 异常：

```python
try:
    client.login()
except CloudreveError as e:
    print(f"错误码: {e.code}")
    print(f"错误信息: {e}")
    print(f"完整响应: {e.response}")
```

常见错误码：
- `40001`: 邮箱格式错误
- `40002`: 密码错误
- `40003`: 需要验证码

## 测试

运行测试脚本验证客户端功能：

```bash
python test_cloudreve_client.py
```

## 注意事项

1. **邮箱格式**: 用户名必须是有效的邮箱格式
2. **网络连接**: 确保能够访问Cloudreve服务器
3. **文件大小**: 大文件会自动分块上传
4. **认证管理**: 客户端会自动处理token刷新
5. **错误重试**: 建议在生产环境中添加重试逻辑

## 高级使用示例

### 文件管理示例

```python
# 列出文件并获取详细信息
files = client.list_files("/videos")
for file_info in files['items']:
    if file_info['type'] == 'file':
        file_id = file_info['id']
        detail = client.get_file_info(file_id)
        print(f"文件: {detail['name']}, 大小: {detail['size']} 字节")

# 创建文件夹
client.create_folder("新文件夹", "/videos")

# 搜索视频文件
results = client.search_files("mp4", file_type="video")
print(f"找到 {len(results['items'])} 个视频文件")

# 重命名文件
client.rename_file(file_id, "新文件名.mp4")

# 移动文件到其他文件夹
client.move_files([file_id], "/videos/新文件夹")
```

### 分享链接示例

```python
# 创建带密码的分享链接
share_info = client.create_share_link(
    file_ids=[file_id],
    password="abc123",      # 设置密码
    expire_days=7,          # 7天后过期
    download_limit=100,     # 限制下载100次
    preview_enabled=True    # 允许预览
)

share_url = f"{client.base_url}/s/{share_info['key']}"
print(f"分享链接: {share_url}")
print(f"分享密码: abc123")

# 获取我的所有分享链接
my_shares = client.list_share_links()
for share in my_shares['items']:
    print(f"分享: {share['key']}, 下载次数: {share['downloads']}")

# 删除分享链接
client.delete_share_link(share_info['id'])
```

### 批量操作示例

```python
# 批量上传文件
import os
from pathlib import Path

def batch_upload(folder_path: str):
    """批量上传文件夹中的所有文件"""
    folder = Path(folder_path)

    for file_path in folder.glob("*"):
        if file_path.is_file():
            print(f"上传: {file_path.name}")
            try:
                result = client.upload_file(str(file_path))
                print(f"✅ 上传成功: {result}")
            except CloudreveError as e:
                print(f"❌ 上传失败: {e}")

# 批量删除文件
def batch_delete_by_pattern(pattern: str):
    """根据文件名模式批量删除文件"""
    search_results = client.search_files(pattern)
    file_ids = [item['id'] for item in search_results['items']]

    if file_ids:
        client.delete_file(file_ids)
        print(f"删除了 {len(file_ids)} 个文件")
```

## 集成到项目

在文件上传器中使用Cloudreve客户端：

```python
from src.uploaders.cloudreve_client import CloudreveClient
from src.common.config import get_config

def upload_to_cloudreve(file_path: str) -> str:
    """上传文件到Cloudreve"""

    # 从配置获取连接信息
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url')
    email = get_config('file_uploader.uploaders.cloudreve.username')
    password = get_config('file_uploader.uploaders.cloudreve.password')

    # 创建客户端并上传
    client = CloudreveClient(base_url, email, password)

    try:
        client.login()
        result = client.upload_file(file_path)
        client.logout()
        return result
    except CloudreveError as e:
        raise Exception(f"Cloudreve上传失败: {e}")

def create_video_share_link(file_path: str) -> str:
    """上传视频并创建分享链接"""
    client = CloudreveClient(base_url, email, password)

    try:
        client.login()

        # 上传文件
        result = client.upload_file(file_path)

        # 从上传结果中获取文件ID（需要根据实际API响应调整）
        # 这里假设需要通过搜索来找到刚上传的文件
        file_name = Path(file_path).name
        search_results = client.search_files(file_name)

        if search_results['items']:
            file_id = search_results['items'][0]['id']

            # 创建分享链接
            share_info = client.create_share_link(
                file_ids=[file_id],
                expire_days=30,  # 30天有效期
                preview_enabled=True
            )

            share_url = f"{client.base_url}/s/{share_info['key']}"
            client.logout()
            return share_url

    except CloudreveError as e:
        raise Exception(f"操作失败: {e}")
```
