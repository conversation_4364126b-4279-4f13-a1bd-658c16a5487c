"""存储引擎对外接口

定义存储引擎模块的统一接口，负责元数据管理、缓存管理和数据导出功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from core.models import ProcessResult, ExportFormat, MetadataInfo, ExportOptions
from common.exceptions import StorageError, ExportError


class StorageEngineInterface(ABC):
    """存储引擎接口"""
    
    @abstractmethod
    def save_metadata(self, metadata: MetadataInfo, file_path: str) -> bool:
        """保存元数据
        
        Args:
            metadata: 元数据信息
            file_path: 保存路径
            
        Returns:
            bool: 是否保存成功
            
        Raises:
            StorageError: 保存失败
        """
        pass
    
    @abstractmethod
    def load_metadata(self, file_path: str) -> Optional[MetadataInfo]:
        """加载元数据
        
        Args:
            file_path: 元数据文件路径
            
        Returns:
            Optional[MetadataInfo]: 元数据信息，如果文件不存在返回None
            
        Raises:
            StorageError: 加载失败
        """
        pass
    
    @abstractmethod
    def export_data(self, process_result: ProcessResult, options: ExportOptions) -> bool:
        """导出数据
        
        Args:
            process_result: 处理结果
            options: 导出选项
            
        Returns:
            bool: 是否导出成功
            
        Raises:
            ExportError: 导出失败
        """
        pass
    
    @abstractmethod
    def get_cache_info(self, video_path: str) -> Optional[Dict[str, Any]]:
        """获取缓存信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            Optional[Dict[str, Any]]: 缓存信息，如果不存在返回None
        """
        pass
    
    @abstractmethod
    def clear_cache(self, video_path: str = None) -> bool:
        """清理缓存
        
        Args:
            video_path: 视频文件路径，如果为None则清理所有缓存
            
        Returns:
            bool: 是否清理成功
        """
        pass
    
    @abstractmethod
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息
        
        Returns:
            Dict[str, Any]: 存储统计信息
        """
        pass
    
    @abstractmethod
    def backup_metadata(self, metadata_path: str, backup_dir: Optional[str] = None) -> str:
        """备份元数据文件
        
        Args:
            metadata_path: 元数据文件路径
            backup_dir: 备份目录，如果为None使用默认目录
            
        Returns:
            str: 备份文件路径
            
        Raises:
            StorageError: 备份失败
        """
        pass
    
    @abstractmethod
    def restore_metadata(self, backup_path: str, target_path: str) -> bool:
        """恢复元数据文件
        
        Args:
            backup_path: 备份文件路径
            target_path: 目标文件路径
            
        Returns:
            bool: 是否恢复成功
            
        Raises:
            StorageError: 恢复失败
        """
        pass
    
    @abstractmethod
    def list_metadata_files(self, directory: str) -> List[str]:
        """列出目录中的元数据文件
        
        Args:
            directory: 目录路径
            
        Returns:
            List[str]: 元数据文件路径列表
        """
        pass
    
    @abstractmethod
    def validate_metadata(self, metadata: MetadataInfo) -> bool:
        """验证元数据的完整性
        
        Args:
            metadata: 元数据信息
            
        Returns:
            bool: 是否有效
        """
        pass


class MetadataManagerInterface(ABC):
    """元数据管理器接口"""
    
    @abstractmethod
    def create_metadata(self, video_path: str, process_result: ProcessResult) -> MetadataInfo:
        """创建元数据"""
        pass
    
    @abstractmethod
    def update_metadata(self, metadata: MetadataInfo, updates: Dict[str, Any]) -> MetadataInfo:
        """更新元数据"""
        pass
    
    @abstractmethod
    def get_metadata_path(self, video_path: str) -> str:
        """获取元数据文件路径"""
        pass
    
    @abstractmethod
    def calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        pass


class CacheManagerInterface(ABC):
    """缓存管理器接口"""
    
    @abstractmethod
    def get_cache_key(self, video_path: str, config: Dict[str, Any]) -> str:
        """生成缓存键"""
        pass
    
    @abstractmethod
    def is_cached(self, cache_key: str) -> bool:
        """检查是否已缓存"""
        pass
    
    @abstractmethod
    def get_cached_result(self, cache_key: str) -> Optional[ProcessResult]:
        """获取缓存的结果"""
        pass
    
    @abstractmethod
    def cache_result(self, cache_key: str, result: ProcessResult) -> bool:
        """缓存结果"""
        pass
    
    @abstractmethod
    def invalidate_cache(self, cache_key: str) -> bool:
        """使缓存失效"""
        pass
    
    @abstractmethod
    def get_cache_size(self) -> int:
        """获取缓存大小（字节）"""
        pass
    
    @abstractmethod
    def cleanup_cache(self, max_age_days: int = 30) -> int:
        """清理过期缓存"""
        pass


class ExportManagerInterface(ABC):
    """导出管理器接口"""
    
    @abstractmethod
    def export_to_txt(self, process_result: ProcessResult, output_path: str, 
                     options: Dict[str, Any] = None) -> bool:
        """导出为文本文件"""
        pass
    
    @abstractmethod
    def export_to_docx(self, process_result: ProcessResult, output_path: str,
                      options: Dict[str, Any] = None) -> bool:
        """导出为Word文档"""
        pass
    
    @abstractmethod
    def export_to_pdf(self, process_result: ProcessResult, output_path: str,
                     options: Dict[str, Any] = None) -> bool:
        """导出为PDF文档"""
        pass
    
    @abstractmethod
    def export_to_srt(self, process_result: ProcessResult, output_path: str,
                     options: Dict[str, Any] = None) -> bool:
        """导出为SRT字幕文件"""
        pass
    
    @abstractmethod
    def export_to_vtt(self, process_result: ProcessResult, output_path: str,
                     options: Dict[str, Any] = None) -> bool:
        """导出为VTT字幕文件"""
        pass
    
    @abstractmethod
    def export_to_json(self, process_result: ProcessResult, output_path: str,
                      options: Dict[str, Any] = None) -> bool:
        """导出为JSON文件"""
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[ExportFormat]:
        """获取支持的导出格式"""
        pass
    
    @abstractmethod
    def validate_export_options(self, format: ExportFormat, 
                               options: Dict[str, Any]) -> bool:
        """验证导出选项"""
        pass


# 导出的接口类型别名
StorageEngine = StorageEngineInterface
MetadataManager = MetadataManagerInterface
CacheManager = CacheManagerInterface
ExportManager = ExportManagerInterface
