"""控制面板组件

提供视频处理、导出、设置等控制功能的UI组件。
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout
)
from PySide6.QtCore import Signal

try:
    from ...common.logging import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from src.common.logging import get_logger


logger = get_logger(__name__)


class ControlPanelWidget(QWidget):
    """控制面板组件 - 极简版本，只包含基本状态显示"""

    # 信号定义
    status_updated = Signal(str)  # 状态更新

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)

        # 初始化UI
        self._init_ui()
        self._connect_signals()

        self.logger.info("控制面板组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 不显示状态信息，只保留空的布局
        # 添加弹性空间
        layout.addStretch()
    
    def _connect_signals(self):
        """连接信号和槽"""
        pass

    def update_status(self, message: str, detail: str = ""):
        """更新状态显示（空实现，不显示状态信息）"""
        # 发送状态更新信号（保持兼容性）
        self.status_updated.emit(message)

    def set_processing_state(self, is_processing: bool, message: str = ""):
        """设置处理状态（空实现，不显示状态信息）"""
        pass

    def clear_status(self):
        """清除状态（空实现，不显示状态信息）"""
        pass




