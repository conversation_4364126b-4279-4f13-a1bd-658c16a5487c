"""通用基础模块

包含配置管理、工具函数、日志系统和异常定义等通用功能。
"""

# 导入主要模块
from . import config
from . import utils
from . import logging
from . import exceptions

# 导出常用功能
from .config import get_config, set_config, save_config
from .logging import get_logger
from .exceptions import VideoReaderError

__all__ = [
    'config',
    'utils',
    'logging',
    'exceptions',
    'get_config',
    'set_config',
    'save_config',
    'get_logger',
    'VideoReaderError'
]
