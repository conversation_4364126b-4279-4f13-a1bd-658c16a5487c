"""辅助工具函数

提供各种通用的辅助功能。
"""

import time
import json
import pickle
import threading
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable, Union
from functools import wraps
from pathlib import Path

from ..logging import get_logger


logger = get_logger(__name__)


def format_duration(seconds: float) -> str:
    """格式化时长为可读字符串"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}分{secs:.1f}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}小时{minutes}分{secs:.1f}秒"


def format_timestamp(timestamp: float, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间戳"""
    try:
        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime(format_str)
    except Exception as e:
        logger.warning(f"格式化时间戳失败: {e}")
        return str(timestamp)


def parse_time_string(time_str: str) -> float:
    """解析时间字符串为秒数
    
    支持格式：
    - "1:23:45" (1小时23分45秒)
    - "23:45" (23分45秒)
    - "45" (45秒)
    - "1h23m45s"
    """
    try:
        # 处理 "1h23m45s" 格式
        if 'h' in time_str or 'm' in time_str or 's' in time_str:
            total_seconds = 0
            parts = time_str.lower().replace('h', ':').replace('m', ':').replace('s', '').split(':')
            multipliers = [3600, 60, 1]  # 小时、分钟、秒
            
            for i, part in enumerate(reversed(parts)):
                if part.strip():
                    total_seconds += float(part) * multipliers[-(i+1)]
            
            return total_seconds
        
        # 处理 "1:23:45" 格式
        parts = time_str.split(':')
        if len(parts) == 1:
            return float(parts[0])
        elif len(parts) == 2:
            return float(parts[0]) * 60 + float(parts[1])
        elif len(parts) == 3:
            return float(parts[0]) * 3600 + float(parts[1]) * 60 + float(parts[2])
        else:
            raise ValueError("无效的时间格式")
            
    except Exception as e:
        logger.error(f"解析时间字符串失败: {e}")
        return 0.0


def seconds_to_time_string(seconds: float) -> str:
    """将秒数转换为时间字符串 (HH:MM:SS)"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes:02d}:{secs:02d}"


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """安全的JSON解析"""
    try:
        return json.loads(json_str)
    except Exception as e:
        logger.warning(f"JSON解析失败: {e}")
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """安全的JSON序列化"""
    try:
        return json.dumps(obj, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.warning(f"JSON序列化失败: {e}")
        return default


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      exceptions: tuple = (Exception,)):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败")
            
            raise last_exception
        return wrapper
    return decorator


def timing_decorator(func):
    """计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            duration = end_time - start_time
            logger.debug(f"函数 {func.__name__} 执行时间: {format_duration(duration)}")
    return wrapper


def thread_safe_singleton(cls):
    """线程安全的单例装饰器"""
    instances = {}
    lock = threading.Lock()
    
    def get_instance(*args, **kwargs):
        if cls not in instances:
            with lock:
                if cls not in instances:
                    instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int = 100, callback: Optional[Callable[[float, str], None]] = None):
        self.total = total
        self.current = 0
        self.callback = callback
        self.start_time = time.time()
        self.last_update_time = self.start_time
    
    def update(self, increment: int = 1, message: str = ""):
        """更新进度"""
        self.current = min(self.current + increment, self.total)
        progress = self.current / self.total
        
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        if self.callback:
            self.callback(progress, message)
        
        self.last_update_time = current_time
    
    def set_progress(self, value: int, message: str = ""):
        """设置进度值"""
        self.current = min(max(value, 0), self.total)
        progress = self.current / self.total
        
        if self.callback:
            self.callback(progress, message)
    
    def get_progress(self) -> float:
        """获取当前进度（0-1）"""
        return self.current / self.total
    
    def get_eta(self) -> Optional[float]:
        """获取预计剩余时间（秒）"""
        if self.current == 0:
            return None
        
        elapsed_time = time.time() - self.start_time
        rate = self.current / elapsed_time
        remaining = self.total - self.current
        
        if rate > 0:
            return remaining / rate
        return None
    
    def is_complete(self) -> bool:
        """检查是否完成"""
        return self.current >= self.total


class Cache:
    """简单的内存缓存"""
    
    def __init__(self, max_size: int = 100, ttl: Optional[float] = None):
        self.max_size = max_size
        self.ttl = ttl
        self._cache = {}
        self._access_times = {}
        self._lock = threading.Lock()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                return default
            
            # 检查TTL
            if self.ttl is not None:
                access_time = self._access_times.get(key, 0)
                if time.time() - access_time > self.ttl:
                    del self._cache[key]
                    del self._access_times[key]
                    return default
            
            # 更新访问时间
            self._access_times[key] = time.time()
            return self._cache[key]
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self._lock:
            # 如果缓存已满，删除最旧的项
            if len(self._cache) >= self.max_size and key not in self._cache:
                oldest_key = min(self._access_times.keys(), 
                               key=lambda k: self._access_times[k])
                del self._cache[oldest_key]
                del self._access_times[oldest_key]
            
            self._cache[key] = value
            self._access_times[key] = time.time()
    
    def delete(self, key: str):
        """删除缓存项"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                del self._access_times[key]
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)


def deep_merge_dict(dict1: Dict, dict2: Dict) -> Dict:
    """深度合并字典"""
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dict(result[key], value)
        else:
            result[key] = value
    
    return result


def flatten_dict(d: Dict, parent_key: str = '', sep: str = '.') -> Dict:
    """扁平化嵌套字典"""
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def chunk_list(lst: List, chunk_size: int) -> List[List]:
    """将列表分块"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def ensure_list(value: Any) -> List:
    """确保值是列表"""
    if isinstance(value, list):
        return value
    elif value is None:
        return []
    else:
        return [value]


def safe_cast(value: Any, target_type: type, default: Any = None) -> Any:
    """安全类型转换"""
    try:
        return target_type(value)
    except (ValueError, TypeError):
        return default
