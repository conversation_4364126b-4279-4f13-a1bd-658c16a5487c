#!/usr/bin/env python3
"""演示Cloudreve上传工作流程"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.common.config import get_config
from src.uploaders.cloudreve_client import CloudreveClient, CloudreveError


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


def demo_upload_workflow():
    """演示完整的上传工作流程"""
    print("🎬 Cloudreve上传工作流程演示")
    print("=" * 60)
    
    # 检查test.mp4文件
    test_file = Path("test.mp4")
    if not test_file.exists():
        print("❌ test.mp4文件不存在")
        return False
    
    file_size = test_file.stat().st_size
    print(f"📁 待上传文件信息:")
    print(f"   文件名: {test_file.name}")
    print(f"   文件大小: {format_size(file_size)}")
    print(f"   文件类型: 视频文件 (MP4)")
    print(f"   文件路径: {test_file.absolute()}")
    
    # 配置信息
    base_url = get_config('file_uploader.uploaders.cloudreve.base_url', '')
    username = get_config('file_uploader.uploaders.cloudreve.username', '')
    
    print(f"\n🔗 Cloudreve配置:")
    print(f"   服务器地址: {base_url}")
    print(f"   用户账户: {username}")
    
    print(f"\n📋 完整上传流程演示:")
    print(f"=" * 40)
    
    # 步骤1: 创建客户端
    print(f"\n1️⃣ 创建Cloudreve客户端")
    print(f"   ```python")
    print(f"   client = CloudreveClient('{base_url}', '{username}', 'password')")
    print(f"   ```")
    print(f"   ✅ 客户端初始化完成")
    
    # 步骤2: 测试连接
    print(f"\n2️⃣ 测试服务器连接")
    print(f"   ```python")
    print(f"   version = client.ping()")
    print(f"   ```")
    
    try:
        client = CloudreveClient(base_url, username, "dummy_password")
        version = client.ping()
        print(f"   ✅ 服务器响应正常，版本: {version}")
    except Exception as e:
        print(f"   ⚠️ 连接测试: {e}")
    
    # 步骤3: 用户认证
    print(f"\n3️⃣ 用户认证流程")
    print(f"   ```python")
    print(f"   # 可能需要验证码")
    print(f"   captcha_image, ticket = client.get_captcha()")
    print(f"   ")
    print(f"   # 用户登录")
    print(f"   login_result = client.login(captcha='1234', ticket=ticket)")
    print(f"   ```")
    
    try:
        captcha_image, ticket = client.get_captcha()
        print(f"   ✅ 验证码获取成功，ticket: {ticket[:20]}...")
        print(f"   📷 验证码图片大小: {len(captcha_image)} 字符 (base64)")
    except Exception as e:
        print(f"   ⚠️ 验证码获取: {e}")
    
    print(f"   💡 实际使用时，用户需要:")
    print(f"      - 查看验证码图片")
    print(f"      - 输入正确的用户名和密码")
    print(f"      - 输入验证码完成登录")
    
    # 步骤4: 文件上传
    print(f"\n4️⃣ 文件上传流程")
    print(f"   ```python")
    print(f"   # 定义进度回调")
    print(f"   def progress_callback(uploaded, total):")
    print(f"       percent = (uploaded / total) * 100")
    print(f"       print(f'上传进度: {{percent:.1f}}%')")
    print(f"   ")
    print(f"   # 开始上传")
    print(f"   result = client.upload_file(")
    print(f"       file_path='test.mp4',")
    print(f"       progress_callback=progress_callback")
    print(f"   )")
    print(f"   ```")
    
    print(f"   📤 模拟上传进度:")
    for i in range(0, 101, 20):
        time.sleep(0.1)  # 模拟上传时间
        uploaded = (file_size * i) // 100
        print(f"      📊 上传进度: {i}% ({format_size(uploaded)} / {format_size(file_size)})")
    
    print(f"   ✅ 文件上传完成")
    print(f"   📁 上传结果: cloudreve://my/test.mp4")
    
    # 步骤5: 创建分享链接
    print(f"\n5️⃣ 创建分享链接")
    print(f"   ```python")
    print(f"   # 搜索上传的文件")
    print(f"   search_results = client.search_files('test.mp4')")
    print(f"   file_id = search_results['items'][0]['id']")
    print(f"   ")
    print(f"   # 创建分享链接")
    print(f"   share_info = client.create_share_link(")
    print(f"       file_ids=[file_id],")
    print(f"       expire_days=7,")
    print(f"       preview_enabled=True")
    print(f"   )")
    print(f"   ```")
    
    # 模拟分享链接
    import random
    import string
    mock_share_key = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    mock_share_url = f"{base_url}/s/{mock_share_key}"
    
    print(f"   ✅ 分享链接创建成功:")
    print(f"   🌐 分享链接: {mock_share_url}")
    print(f"   ⏰ 有效期: 7天")
    print(f"   👁️ 允许预览: 是")
    
    # 步骤6: 文件管理
    print(f"\n6️⃣ 文件管理功能")
    print(f"   ```python")
    print(f"   # 列出文件")
    print(f"   files = client.list_files('/')")
    print(f"   ")
    print(f"   # 获取文件信息")
    print(f"   file_info = client.get_file_info(file_id)")
    print(f"   ")
    print(f"   # 创建下载链接")
    print(f"   download_url = client.create_download_url(file_id)")
    print(f"   ")
    print(f"   # 重命名文件")
    print(f"   client.rename_file(file_id, 'new_name.mp4')")
    print(f"   ")
    print(f"   # 删除文件")
    print(f"   client.delete_file([file_id])")
    print(f"   ```")
    
    print(f"   ✅ 支持完整的文件管理操作")
    
    # 步骤7: 清理和登出
    print(f"\n7️⃣ 清理和登出")
    print(f"   ```python")
    print(f"   # 用户登出")
    print(f"   client.logout()")
    print(f"   ```")
    print(f"   ✅ 会话清理完成")
    
    # 总结
    print(f"\n📋 功能特性总结:")
    print(f"=" * 40)
    print(f"✅ 用户认证 (支持验证码)")
    print(f"✅ 文件上传 (支持大文件分块)")
    print(f"✅ 上传进度跟踪")
    print(f"✅ 文件搜索和列表")
    print(f"✅ 分享链接创建")
    print(f"✅ 文件管理 (重命名、删除、移动)")
    print(f"✅ 下载链接生成")
    print(f"✅ 存储空间查询")
    print(f"✅ 完整的错误处理")
    
    print(f"\n💡 实际使用步骤:")
    print(f"1. 配置正确的Cloudreve服务器地址和账户信息")
    print(f"2. 运行 python upload_test_video.py")
    print(f"3. 根据提示输入验证码(如果需要)")
    print(f"4. 等待文件上传完成")
    print(f"5. 获取分享链接用于视频处理")
    
    return True


def main():
    """主函数"""
    print("Cloudreve上传工作流程演示")
    print("=" * 60)
    
    success = demo_upload_workflow()
    
    if success:
        print(f"\n🎉 工作流程演示完成!")
        print(f"\n📚 相关文档:")
        print(f"   - 使用说明: doc/cloudreve_client_usage.md")
        print(f"   - 客户端代码: src/uploaders/cloudreve_client.py")
        print(f"   - 测试脚本: test_cloudreve_client.py")
        print(f"   - 上传脚本: upload_test_video.py")


if __name__ == "__main__":
    main()
